import { EnvironmentType } from '../types';

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: EnvironmentType;
      WEB_APP_URL: 'http://localhost:3000' | 'https://app-staging.tpaccurate.com' | 'https://app.tpaccurate.com';
      SOLVER_URL: 'http://localhost:5000';
      GOTENBERG_URL: 'http://localhost:6000';
      OPLON_RISK_USERNAME: string;
      OPLON_RISK_PASSWORD: string;
    }
  }
}

export {};
