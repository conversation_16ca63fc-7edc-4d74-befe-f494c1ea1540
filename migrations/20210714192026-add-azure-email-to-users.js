'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.addColumn(
          'Users',
          'azureEmail',
          {
            type: Sequelize.STRING,
            allowNull: true,
          },
          { transaction },
        ),
        queryInterface.changeColumn(
          'Users',
          'username',
          {
            type: Sequelize.STRING,
            allowNull: true,
            unique: true,
          },
          { transaction },
        ),
        queryInterface.changeColumn(
          'Users',
          'password',
          {
            type: Sequelize.STRING,
            allowNull: true,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Users', 'azureEmail', { transaction }),
        queryInterface.changeColumn('Users', 'username', {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        }),
        queryInterface.changeColumn('Users', 'password', {
          type: Sequelize.STRING,
          allowNull: false,
        }),
      ]);
    });
  },
};
