'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.addIndex('Tokens', ['accessToken'], {
      unique: true,
      name: 'idx_token_unique',
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.removeIndex('Tokens', 'idx_token_unique');
  },
};
