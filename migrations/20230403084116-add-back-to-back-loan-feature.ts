'use strict';
import { QueryInterface, Transaction } from 'sequelize';

import { featureNames } from '../enums';
import { ClientFeatureType, ClientType } from '../types';

/** @type {import('sequelize-cli').Migration} */
export = {
  async up(queryInterface: QueryInterface) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      const now = new Date();
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            name: featureNames.BACK_TO_BACK_LOAN,
            fullName: 'Back-to-back Loans',
            path: null,
            isModule: false,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },
        ],
        { transaction },
      );

      const [queryClients] = await queryInterface.sequelize.query('SELECT * FROM public."Clients";', { transaction });
      const [queryFeatures] = await queryInterface.sequelize.query(
        `SELECT * FROM public."Features" WHERE name = '${featureNames.BACK_TO_BACK_LOAN}';`,
        { transaction },
      );

      const clients = queryClients as ClientType[];
      const feature = queryFeatures[0] as ClientFeatureType;
      const clientFeatures: Array<Omit<ClientFeatureType, 'id'>> = [];
      const clientFeatureInserts: Array<Promise<any>> = [];
      for (const client of clients) {
        clientFeatures.push({
          clientId: client.id,
          featureId: feature.id,
          isEnabled: true,
          values: null,
          createdAt: now,
          updatedAt: now,
        });

        clientFeatureInserts.push(queryInterface.bulkInsert('Client_Features', clientFeatures, { transaction }));
        clientFeatures.length = 0;
      }
      await Promise.all(clientFeatureInserts);
    });
  },

  async down(queryInterface: QueryInterface) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.bulkDelete('Features', { name: featureNames.BACK_TO_BACK_LOAN }, { transaction });
    });
  },
};
