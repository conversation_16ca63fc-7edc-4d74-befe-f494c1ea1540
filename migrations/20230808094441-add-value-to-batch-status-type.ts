import { QueryInterface, Transaction } from 'sequelize';

import { cashPoolEnums } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.changeColumn(
        'CashPoolBatches',
        'status',
        {
          type: Sequelize.TEXT,
        },
        { transaction },
      );
      await queryInterface.sequelize.query('DROP TYPE "enum_CashPoolBatches_status";', { transaction });

      await queryInterface.changeColumn(
        'CashPoolBatches',
        'status',
        {
          type: Sequelize.ENUM(...Object.values(cashPoolEnums.batchStatus)),
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.changeColumn(
        'CashPoolBatches',
        'status',
        {
          type: Sequelize.TEXT,
        },
        { transaction },
      );

      await queryInterface.sequelize.query('DROP TYPE "enum_CashPoolBatches_status";', { transaction });

      await queryInterface.changeColumn(
        'CashPoolBatches',
        'status',
        {
          type: Sequelize.ENUM(
            cashPoolEnums.batchStatus.UNPOOLED,
            cashPoolEnums.batchStatus.UNPAID,
            cashPoolEnums.batchStatus.PARTIALLY_PAID,
            cashPoolEnums.batchStatus.PAID,
          ),
          allowNull: false,
        },
        { transaction },
      );
    });
  },
};
