'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('ParticipantAccountIds', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolAccountId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolParticipantAccounts',
            key: 'id',
          },
        },
        externalId: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'accountIdentification');
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.dropTable('ParticipantAccountIds');
      await queryInterface.addColumn('CashPoolParticipantAccounts', 'accountIdentification', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    });
  },
};
