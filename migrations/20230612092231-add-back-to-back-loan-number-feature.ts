'use strict';
import { QueryInterface, Transaction } from 'sequelize';

import { featureNames } from '../enums';
import { ClientFeatureType, ClientType } from '../types';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      const now = new Date();
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            name: featureNames.BACK_TO_BACK_LOAN_NUMBER,
            fullName: 'Back-to-back Loan Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
        ],
        { transaction },
      );

      const [queryClients] = await queryInterface.sequelize.query('SELECT * FROM public."Clients";', { transaction });
      const [queryFeatures] = await queryInterface.sequelize.query(
        `SELECT * FROM public."Features" WHERE name = '${featureNames.BACK_TO_BACK_LOAN_NUMBER}';`,
        { transaction },
      );

      const clients = queryClients as ClientType[];
      const feature = queryFeatures[0] as ClientFeatureType;
      const clientFeatures: Array<Omit<ClientFeatureType, 'id'>> = [];
      const clientFeatureInserts: Array<Promise<any>> = [];
      for (const client of clients) {
        clientFeatures.push({
          clientId: client.id,
          featureId: feature.id,
          isEnabled: true,
          values: 20,
          createdAt: now,
          updatedAt: now,
        });

        clientFeatureInserts.push(
          queryInterface.bulkInsert('Client_Features', clientFeatures, { transaction }, {
            values: { type: new Sequelize.JSON() },
          } as unknown as any),
        );
        clientFeatures.length = 0;
      }
      await Promise.all(clientFeatureInserts);

      await queryInterface.addColumn('BackToBackLoans', 'createdBy', { type: Sequelize.STRING, allowNull: false });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.bulkDelete('Features', { name: featureNames.BACK_TO_BACK_LOAN_NUMBER }, { transaction });

      await queryInterface.removeColumn('BackToBackLoans', 'createdBy');
    });
  },
};
