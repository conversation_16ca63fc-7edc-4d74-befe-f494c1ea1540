'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.addColumn(
          'Loans',
          'log',
          {
            type: Sequelize.JSON,
            allowNull: true,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'log',
          {
            type: Sequelize.JSON,
            allowNull: true,
          },
          { transaction },
        ),
      ]);
      await Promise.all([
        queryInterface.sequelize.query(
          'UPDATE "Loans" SET "log"=jsonb_build_object(\'log\', "calculationLog") WHERE "calculationLog" IS NOT NULL;',
          { transaction },
        ),
        queryInterface.sequelize.query(
          'UPDATE "Guarantees" SET "log"=jsonb_build_object(\'log\', "calculationLog") WHERE "calculationLog" IS NOT NULL;',
          { transaction },
        ),
      ]);
      await Promise.all([
        queryInterface.removeColumn('Loans', 'calculationLog', { transaction }),
        queryInterface.removeColumn('Guarantees', 'calculationLog', { transaction }),
      ]);

      return Promise.all([
        queryInterface.renameColumn('Loans', 'log', 'calculationLog', { transaction }),
        queryInterface.renameColumn('Guarantees', 'log', 'calculationLog', { transaction }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.addColumn(
          'Loans',
          'log',
          {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'log',
          {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          { transaction },
        ),
      ]);
      await Promise.all([
        queryInterface.sequelize.query(
          'UPDATE "Loans" SET "log"="calculationLog"::json->>\'log\' WHERE "calculationLog" IS NOT NULL;',
          { transaction },
        ),
        queryInterface.sequelize.query(
          'UPDATE "Guarantees" SET "log"="calculationLog"::json->>\'log\' WHERE "calculationLog" IS NOT NULL;',
          { transaction },
        ),
      ]);
      await Promise.all([
        queryInterface.removeColumn('Loans', 'calculationLog', { transaction }),
        queryInterface.removeColumn('Guarantees', 'calculationLog', { transaction }),
      ]);

      return Promise.all([
        queryInterface.renameColumn('Loans', 'log', 'calculationLog', { transaction }),
        queryInterface.renameColumn('Guarantees', 'log', 'calculationLog', { transaction }),
      ]);
    });
  },
};
