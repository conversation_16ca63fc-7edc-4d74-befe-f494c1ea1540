'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.addColumn(
        'CreditRatingAttributes',
        'isTemplateUploaded',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.removeColumn('CreditRatingAttributes', 'isTemplateUploaded', { transaction });
    });
  },
};
