'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPools', 'interestType', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_CashPools_interestType";', { transaction });
      await queryInterface.addColumn(
        'CashPools',
        'interestType',
        {
          type: Sequelize.ENUM('fixed', 'float'),
          allowNull: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPools', 'interestType', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_CashPools_interestType";', { transaction });
      await queryInterface.addColumn(
        'CashPools',
        'interestType',
        {
          type: Sequelize.ENUM('fixed', 'float'),
          allowNull: false,
          defaultValue: 'fixed',
        },
        { transaction },
      );
    });
  },
};
