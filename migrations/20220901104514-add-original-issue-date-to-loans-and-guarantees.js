'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface
        .addColumn('Loans', 'originalIssueDate', { type: Sequelize.DATE }, { transaction })
        .then(() => {
          queryInterface.sequelize.query('SELECT * from "Loans"').then(async (rows) => {
            await Promise.all(
              rows[0].map((row) =>
                queryInterface.bulkUpdate('Loans', { originalIssueDate: row.issueDate }, { id: row.id }),
              ),
            );

            await queryInterface.changeColumn('Loans', 'originalIssueDate', {
              type: Sequelize.DATE,
              allowNull: false,
            });
          });
        });
      await queryInterface
        .addColumn('Guarantees', 'originalIssueDate', { type: Sequelize.DATE }, { transaction })
        .then(() => {
          queryInterface.sequelize.query('SELECT * from "Guarantees"').then(async (rows) => {
            await Promise.all(
              rows[0].map((row) =>
                queryInterface.bulkUpdate('Guarantees', { originalIssueDate: row.issueDate }, { id: row.id }),
              ),
            );

            await queryInterface.changeColumn('Guarantees', 'originalIssueDate', {
              type: Sequelize.DATE,
              allowNull: false,
            });
          });
        });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Loans', 'originalIssueDate', { transaction });
      await queryInterface.removeColumn('Guarantees', 'originalIssueDate', { transaction });
    });
  },
};
