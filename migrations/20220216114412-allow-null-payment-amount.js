'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Payments',
        'paymentAmount',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BulletPayments',
        'interestPayment',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BalloonPayments',
        'compoundedInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BalloonPayments',
        'additionalInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Payments',
        'paymentAmount',
        {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BulletPayments',
        'interestPayment',
        {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BalloonPayments',
        'compoundedInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'BalloonPayments',
        'additionalInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        { transaction },
      );
    });
  },
};
