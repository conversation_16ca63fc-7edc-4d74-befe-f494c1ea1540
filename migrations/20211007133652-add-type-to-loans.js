'use strict';
const { Op } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Loans',
        'type',
        {
          type: Sequelize.ENUM('Bullet', 'Balloon'),
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.bulkUpdate(
        'Loans',
        { type: 'Bullet' },
        {
          type: {
            [Op.eq]: null,
          },
        },
        { transaction },
      );

      await queryInterface.sequelize.query('ALTER TABLE "Loans" ALTER column "type" SET NOT NULL', {
        transaction,
      });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Loans', 'type', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_Loans_type";', { transaction });
    });
  },
};
