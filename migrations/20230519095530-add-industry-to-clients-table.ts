'use strict';
import { QueryInterface } from 'sequelize';

/** @type {import('sequelize-cli').Migration} */
export = {
  async up(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.addColumn('Clients', 'industry', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'Unknown',
    });
  },

  async down(queryInterface: QueryInterface) {
    await queryInterface.removeColumn('Clients', 'industry');
  },
};
