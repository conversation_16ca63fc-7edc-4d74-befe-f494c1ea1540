'use strict';
const { Op, col } = require('sequelize');

const finalizedTables = ['Guarantees', 'Loans', 'CreditRatings'];
const tables = [...finalizedTables, 'Companies', 'CompanyAuditTrails'];

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Add createdBy, updatedBy and finalizedBy queries
      await Promise.all(
        tables.map((table) => {
          const queries = [
            queryInterface.addColumn(
              table,
              'createdBy',
              {
                type: Sequelize.STRING,
                allowNull: true,
              },
              { transaction },
            ),
          ];

          if (table !== 'CompanyAuditTrails') {
            queries.push(
              queryInterface.addColumn(
                table,
                'updatedBy',
                {
                  type: Sequelize.STRING,
                  allowNull: true,
                },
                { transaction },
              ),
            );
          }

          if (finalizedTables.includes(table)) {
            queries.push(
              queryInterface.addColumn(
                table,
                'finalizedBy',
                {
                  type: Sequelize.STRING,
                  allowNull: true,
                },
                { transaction },
              ),
            );
          }

          return queries;
        }),
      );

      // If user exists then update createdBy and finalizedBy columns ( for staging environment )
      const user = await queryInterface.sequelize.query('SELECT "username" FROM "Users" LIMIT 1;', { transaction });
      if (user[0].length !== 0) {
        const username = user[0][0].username;
        await Promise.all(
          tables.map((table) => {
            const queries = [
              queryInterface.bulkUpdate(table, { createdBy: username }, { createdBy: null }, { transaction }),
            ];

            if (table !== 'CompanyAuditTrails') {
              queryInterface.bulkUpdate(
                table,
                { updatedBy: username },
                {
                  updatedBy: null,
                  updatedAt: {
                    [Op.ne]: col('createdAt'),
                  },
                },
                { transaction },
              );
            }

            if (finalizedTables.includes(table)) {
              queries.push(
                queryInterface.bulkUpdate(
                  table,
                  { finalizedBy: username },
                  {
                    finalizedBy: null,
                    status: 'Final',
                  },
                  { transaction },
                ),
              );
            }

            return queries;
          }),
        );
      }

      // set allowNull: false to createdBy column
      await Promise.all(
        tables.map((table) => {
          return queryInterface.changeColumn(
            table,
            'createdBy',
            {
              type: Sequelize.STRING,
              allowNull: false,
            },
            { transaction },
          );
        }),
      );

      // Remove all tokens because new tokens will have the username
      await queryInterface.bulkDelete('Tokens', {}, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all(
        tables.map((table) => {
          const queries = [queryInterface.removeColumn(table, 'createdBy', { transaction })];

          if (table !== 'CompanyAuditTrails') {
            queries.push(queryInterface.removeColumn(table, 'updatedBy', { transaction }));
          }

          if (finalizedTables.includes(table)) {
            queries.push(queryInterface.removeColumn(table, 'finalizedBy', { transaction }));
          }

          return queries;
        }),
      );
    });
  },
};
