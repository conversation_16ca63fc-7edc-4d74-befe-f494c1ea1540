'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction((transaction) => {
      return Promise.all([
        queryInterface.createTable(
          'CashPoolParticipantAccounts',
          {
            id: {
              allowNull: false,
              autoIncrement: true,
              primaryKey: true,
              type: Sequelize.INTEGER,
            },
            cashPoolParticipantId: {
              type: Sequelize.INTEGER,
              allowNull: false,
              onDelete: 'CASCADE',
              references: {
                model: 'Cash_Pool_Participants',
                key: 'id',
              },
            },
            balance: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            currency: {
              type: Sequelize.STRING,
              allowNull: true,
            },
            creditInterestRate: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            debitInterestRate: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            adjustedCreditInterestRate: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            adjustedDebitInterestRate: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            adjustedCreditInterestReceived: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            adjustedDebitInterestPaid: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            netInterestSynergy: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            grossInterestSynergy: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            netOffsettingSynergy: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            grossOffsettingSynergy: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            netInterestBenefit: {
              type: Sequelize.FLOAT,
              allowNull: true,
            },
            createdAt: {
              type: Sequelize.DATE,
              allowNull: false,
            },
            updatedAt: {
              type: Sequelize.DATE,
              allowNull: false,
            },
          },
          { transaction },
        ),
        queryInterface.removeColumn('Cash_Pool_Participants', 'balance', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'currency', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'creditInterestRate', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'debitInterestRate', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'adjustedCreditInterestRate', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'adjustedDebitInterestRate', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'adjustedCreditInterestReceived', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'adjustedDebitInterestPaid', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'netInterestSynergy', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'grossInterestSynergy', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'netOffsettingSynergy', { transaction }),
        queryInterface.removeColumn('Cash_Pool_Participants', 'grossOffsettingSynergy', { transaction }),

        queryInterface.addColumn(
          'CashPoolParticipantTrails',
          'netInterestBenefit',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.removeColumn('CashPoolParticipantTrails', 'isLeader', { transaction }),

        queryInterface.addColumn(
          'CashPools',
          'totalRisk',
          { type: Sequelize.FLOAT, allowNull: false },
          { transaction },
        ),

        queryInterface.addColumn(
          'CashPoolBatch_ParticipantPayments',
          'currency',
          { type: Sequelize.STRING, allowNull: false },
          { transaction },
        ),
      ]);
    });

    await queryInterface.sequelize.query(
      "ALTER TYPE \"enum_CashPools_type\" ADD VALUE IF NOT EXISTS 'Nordic' AFTER 'Notional';",
    );
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction((transaction) => {
      return Promise.all([
        queryInterface.dropTable('CashPoolParticipantAccounts', { transaction }),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'balance',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'currency',
          { type: Sequelize.STRING, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'creditInterestRate',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'debitInterestRate',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'adjustedCreditInterestRate',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'adjustedDebitInterestRate',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'adjustedCreditInterestReceived',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'adjustedDebitInterestPaid',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'netInterestSynergy',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'grossInterestSynergy',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'netOffsettingSynergy',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),
        queryInterface.addColumn(
          'Cash_Pool_Participants',
          'grossOffsettingSynergy',
          { type: Sequelize.FLOAT, allowNull: true },
          { transaction },
        ),

        queryInterface.removeColumn('CashPoolParticipantTrails', 'netInterestBenefit', { transaction }),
        queryInterface.addColumn(
          'CashPoolParticipantTrails',
          'isLeader',
          { type: Sequelize.BOOLEAN, allowNull: false, defaultValue: false },
          { transaction },
        ),

        queryInterface.removeColumn('CashPoolBatch_ParticipantPayments', 'currency', { transaction }),

        queryInterface.removeColumn('CashPools', 'totalRisk', { transaction }),
      ]);
    });
  },
};
