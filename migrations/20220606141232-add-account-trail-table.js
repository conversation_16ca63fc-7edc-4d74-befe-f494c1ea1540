'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.dropTable('CashPoolParticipantTrails', { transaction });
      await queryInterface.createTable(
        'ParticipantAccountTrails',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          participantAccountId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPoolParticipantAccounts',
              key: 'id',
            },
          },
          cashPoolBatchId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPoolBatches',
              key: 'id',
            },
          },
          balance: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          creditInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          debitInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedCreditInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedDebitInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedCreditInterestReceived: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedDebitInterestPaid: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netInterestSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          grossInterestSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netOffsettingSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          grossOffsettingSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netInterestBenefit: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'CashPoolParticipantTrails',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cashPoolParticipantId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Cash_Pool_Participants',
              key: 'id',
            },
          },
          cashPoolBatchId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPoolBatches',
              key: 'id',
            },
          },
          balance: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          creditInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedCreditInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          debitInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedDebitInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedCreditInterestReceived: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          adjustedDebitInterestPaid: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netInterestSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          grossInterestSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netOffsettingSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          grossOffsettingSynergy: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          netInterestBenefit: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
      await queryInterface.dropTable('ParticipantAccountTrails', { transaction });
    });
  },
};
