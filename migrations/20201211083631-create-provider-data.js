'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('ProviderData', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        issueDate: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        curveName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        country: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        region: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        currency: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        industryGroup: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        industry: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        seniority: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        rating: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        covered: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        baseDataPoint: {
          type: Sequelize.JSON,
          allowNull: false,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
      });

      await queryInterface.addIndex('ProviderData', [
        'issueDate',
        'currency',
        'seniority',
        'region',
        'industry',
        'rating',
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ProviderData');
  },
};
