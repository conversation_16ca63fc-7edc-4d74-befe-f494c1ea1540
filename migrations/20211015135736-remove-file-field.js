module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('LoanFiles', 'file');
      await queryInterface.removeColumn('GuaranteeFiles', 'file');
      await queryInterface.removeColumn('CreditRatingFiles', 'file');
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('LoanFiles', 'file', {
        type: Sequelize.BLOB,
        allowNull: false,
      });
      await queryInterface.addColumn('GuaranteeFiles', 'file', {
        type: Sequelize.BLOB,
        allowNull: false,
      });
      await queryInterface.addColumn('CreditRatingFiles', 'file', {
        type: Sequelize.BLOB,
        allowNull: false,
      });
    });
  },
};
