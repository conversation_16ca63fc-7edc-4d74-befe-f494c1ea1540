'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('CreditRatingAttributes', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      creditRatingId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        onDelete: 'CASCADE',
        references: {
          model: 'CreditRatings',
          key: 'id',
        },
      },
      naceSector: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      option: {
        type: Sequelize.ENUM('Consolidated', 'Unconsolidated'),
        allowNull: false,
      },
      months: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      currency: {
        type: Sequelize.STRING,
      },
      exchangeRate: {
        type: Sequelize.FLOAT,
      },
      fixedAssets: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      intangibleFixedAssets: {
        type: Sequelize.FLOAT,
      },
      tangibleFixedAssets: {
        type: Sequelize.FLOAT,
      },
      otherFixedAssets: {
        type: Sequelize.FLOAT,
      },
      currentAssets: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      stocks: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      debtors: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      otherCurrentAssets: {
        type: Sequelize.FLOAT,
      },
      cashAndCashEquivalent: {
        type: Sequelize.FLOAT,
      },
      totalAssets: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      shareholdersFunds: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      capital: {
        type: Sequelize.FLOAT,
      },
      otherShareholdersFunds: {
        type: Sequelize.FLOAT,
      },
      treasuryShares: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      nonCurrentLiabilities: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      longTermDebt: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      otherNonCurrentLiabilities: {
        type: Sequelize.FLOAT,
      },
      provisions: {
        type: Sequelize.FLOAT,
      },
      currentLiabilities: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      loans: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      creditors: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      otherCurrentLiabilities: {
        type: Sequelize.FLOAT,
      },
      totalShareFundsAndLiabilities: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      operatingRevenueTurnover: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      sales: {
        type: Sequelize.FLOAT,
      },
      materialCosts: {
        type: Sequelize.FLOAT,
      },
      costOfEmployees: {
        type: Sequelize.FLOAT,
      },
      EBITDA: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      depreciation: {
        type: Sequelize.FLOAT,
      },
      EBIT: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      financialRevenue: {
        type: Sequelize.FLOAT,
      },
      financialExpenses: {
        type: Sequelize.FLOAT,
      },
      interestPaid: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      financialPL: {
        type: Sequelize.FLOAT,
      },
      PLBeforeTax: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      taxation: {
        type: Sequelize.FLOAT,
      },
      PLAfterTax: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherRevenue: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherExpenses: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherPL: {
        type: Sequelize.FLOAT,
      },
      PLForPeriod: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      costOfGoodSold: {
        type: Sequelize.FLOAT,
      },
      otherOperatingExpenses: {
        type: Sequelize.FLOAT,
      },
      grossProfit: {
        type: Sequelize.FLOAT,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('CreditRatingAttributes');
  },
};
