'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Users',
        'email',
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Users',
        'azureEmail',
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Users',
        'email',
        {
          type: Sequelize.STRING,
          allowNull: false,
          unique: false,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Users',
        'azureEmail',
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
        },
        { transaction },
      );
    });
  },
};
