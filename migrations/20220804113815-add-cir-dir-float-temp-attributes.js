'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'cirWithOvernightRate',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'dirWithOvernightRate',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'cirWithOvernightRate', { transaction });
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'dirWithOvernightRate', { transaction });
    });
  },
};
