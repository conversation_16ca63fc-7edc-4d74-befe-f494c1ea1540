'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query('SELECT * FROM "CreditRatings";', { transaction });
      const queryRes = queryResponse[0];
      const creditRatingAttributes = [];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const {
          id,
          clientId,
          isPortfolio,
          editable,
          company,
          closingDate,
          creditRating,
          probabilityOfDefault,
          status,
          note,
          createdBy,
          updatedBy,
          finalizedBy,
          pdf,
          ...attributes
        } = queryRes[i];
        creditRatingAttributes.push({ creditRatingId: id, ...attributes });
      }

      await queryInterface.bulkInsert('CreditRatingAttributes', creditRatingAttributes, { transaction });

      await queryInterface.removeColumn('CreditRatings', 'naceSector', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'option', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'months', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'currency', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'exchangeRate', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'fixedAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'intangibleFixedAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'tangibleFixedAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherFixedAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'currentAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'stocks', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'debtors', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherCurrentAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'cashAndCashEquivalent', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'totalAssets', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'shareholdersFunds', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'capital', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherShareholdersFunds', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'treasuryShares', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'nonCurrentLiabilities', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'longTermDebt', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherNonCurrentLiabilities', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'provisions', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'currentLiabilities', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'loans', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'creditors', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherCurrentLiabilities', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'totalShareFundsAndLiabilities', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'operatingRevenueTurnover', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'sales', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'materialCosts', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'costOfEmployees', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'EBITDA', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'depreciation', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'EBIT', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'financialRevenue', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'financialExpenses', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'interestPaid', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'financialPL', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'PLBeforeTax', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'taxation', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'PLAfterTax', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'extrAndOtherRevenue', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'extrAndOtherExpenses', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'extrAndOtherPL', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'PLForPeriod', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'costOfGoodSold', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'otherOperatingExpenses', { transaction });
      await queryInterface.removeColumn('CreditRatings', 'grossProfit', { transaction });

      await queryInterface.sequelize.query('DROP TYPE "enum_CreditRatings_option";', { transaction });
    });
  },

  // TODO do the transfer in reverse
  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.addColumn(
          'CreditRatings',
          'naceSector',
          {
            type: Sequelize.STRING,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'option',
          {
            type: Sequelize.ENUM('Consolidated', 'Unconsolidated'),
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'months',
          {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currency',
          {
            type: Sequelize.STRING,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'exchangeRate',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'fixedAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'intangibleFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'tangibleFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currentAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'stocks',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'debtors',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherCurrentAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'cashAndCashEquivalent',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'totalAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'shareholdersFunds',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'capital',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherShareholdersFunds',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'treasuryShares',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'nonCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'longTermDebt',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherNonCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'provisions',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currentLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'loans',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'creditors',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'totalShareFundsAndLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'operatingRevenueTurnover',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'sales',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'materialCosts',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'costOfEmployees',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'EBITDA',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'depreciation',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'EBIT',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialRevenue',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'interestPaid',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialPL',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLBeforeTax',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'taxation',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLAfterTax',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherRevenue',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherPL',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLForPeriod',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'costOfGoodSold',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherOperatingExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'grossProfit',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
      ]);
    });
  },
};
