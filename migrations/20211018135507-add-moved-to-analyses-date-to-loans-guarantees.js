/**
 * Adds new movedToAnalysesDate column to <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> model
 * Initializes it to the createdAt of that row and sets allowNull to false
 */

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface
        .addColumn(
          'Loans',
          'movedToAnalysesDate',
          {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },
          { transaction },
        )
        .then(() => {
          queryInterface.sequelize.query('SELECT * from "Loans"').then(async (rows) => {
            await Promise.all(
              rows[0].map((row) =>
                queryInterface.bulkUpdate('Loans', { movedToAnalysesDate: row.createdAt }, { id: row.id }),
              ),
            );

            await queryInterface.changeColumn('Loans', 'movedToAnalysesDate', {
              type: Sequelize.DATE,
              defaultValue: Sequelize.NOW,
              allowNull: false,
            });
          });
        });

      await queryInterface
        .addColumn(
          'Guarantees',
          'movedToAnalysesDate',
          {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
          },
          { transaction },
        )
        .then(() => {
          queryInterface.sequelize.query('SELECT * from "Guarantees"').then(async (rows) => {
            await Promise.all(
              rows[0].map((row) =>
                queryInterface.bulkUpdate('Guarantees', { movedToAnalysesDate: row.createdAt }, { id: row.id }),
              ),
            );

            await queryInterface.changeColumn('Guarantees', 'movedToAnalysesDate', {
              type: Sequelize.DATE,
              defaultValue: Sequelize.NOW,
              allowNull: false,
            });
          });
        });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Loans', 'movedToAnalysesDate');
      await queryInterface.removeColumn('Guarantees', 'movedToAnalysesDate');
    });
  },
};
