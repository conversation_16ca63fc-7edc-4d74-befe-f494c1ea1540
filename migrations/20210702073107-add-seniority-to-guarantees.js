'use strict';
const { Op } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Guarantees',
        'seniority',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.bulkUpdate(
        'Guarantees',
        {
          seniority: 'Unsubordinated',
        },
        {
          seniority: {
            [Op.eq]: null,
          },
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Guarantees',
        'seniority',
        {
          type: Sequelize.STRING,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Guarantees', 'seniority');
  },
};
