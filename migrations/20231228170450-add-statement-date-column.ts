'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolStatementData', 'statementDate', {
        type: Sequelize.DATEONLY,
        allowNull: false,
      });

      await queryInterface.removeColumn('CashPoolStatementData', 'balance');
      
      await queryInterface.removeColumn('CashPoolStatementData', 'position');
      await queryInterface.sequelize.query('DROP TYPE "enum_CashPoolStatementData_position";');

      await queryInterface.changeColumn('CashPoolStatementData', 'date', {
        type: Sequelize.DATEONLY,
        allowNull: false,
      });
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolStatementData', 'statementDate');

      await queryInterface.addColumn('CashPoolStatementData', 'balance', {
        type: Sequelize.FLOAT,
        allowNull: false,
      });

      await queryInterface.changeColumn('CashPoolStatementData', 'date', {
        type: Sequelize.DATE,
        allowNull: false,
      });
    });
  },
};
