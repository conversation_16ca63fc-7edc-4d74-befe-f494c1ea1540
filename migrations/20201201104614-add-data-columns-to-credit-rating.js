'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('CreditRatings', 'name', {
          type: Sequelize.STRING,
        }),
        queryInterface.addColumn('CreditRatings', 'fiscalYear', {
          type: Sequelize.STRING,
        }),
        queryInterface.addColumn('CreditRatings', 'creditRating', {
          type: Sequelize.JSON,
        }),
        queryInterface.addColumn('CreditRatings', 'pdf', {
          type: Sequelize.BLOB,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('CreditRatings', 'name'),
        queryInterface.removeColumn('CreditRatings', 'fiscalYear'),
        queryInterface.removeColumn('CreditRatings', 'creditRating'),
        queryInterface.removeColumn('CreditRatings', 'pdf'),
      ]);
    });
  },
};
