'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('LoanFiles', 'isGenerated', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        }),
        queryInterface.addColumn('GuaranteeFiles', 'isGenerated', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('LoanFiles', 'isGenerated'),
        queryInterface.removeColumn('GuaranteeFiles', 'isGenerated'),
      ]);
    });
  },
};
