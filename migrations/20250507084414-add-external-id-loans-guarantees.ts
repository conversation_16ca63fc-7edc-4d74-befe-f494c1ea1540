'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('Loans', 'externalId', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('Guarantees', 'externalId', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('BackToBackLoans', 'externalId', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('Loans', 'externalId');
      await queryInterface.removeColumn('Guarantees', 'externalId');
      await queryInterface.removeColumn('BackToBackLoans', 'externalId');
    });
  },
};
