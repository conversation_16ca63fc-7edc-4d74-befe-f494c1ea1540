'use strict';
import { QueryInterface, Transaction } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.addColumn(
        'BackToBackLoanLegs',
        'calculationLog',
        { type: Sequelize.JSON, allowNull: true },
        { transaction },
      );
      await queryInterface.renameColumn('BackToBackLoanFiles', 'backToBackLoanId', 'loanId', { transaction });
      await queryInterface.renameColumn('BackToBackLoanLegs', 'backToBackLoanId', 'loanId', { transaction });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.removeColumn('BackToBackLoanLegs', 'calculationLog', { transaction });

      await queryInterface.renameColumn('BackToBackLoanFiles', 'loanId', 'backToBackLoanId', { transaction });
      await queryInterface.renameColumn('BackToBackLoanLegs', 'loanId', 'backToBackLoanId', { transaction });
    });
  },
};
