'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'savingsEnhancedRateComponent',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'savingsOffsettingComponent',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );

      await queryInterface.addColumn(
        'ParticipantAccountTrails',
        'savingsEnhancedRateComponent',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );
      await queryInterface.addColumn(
        'ParticipantAccountTrails',
        'savingsOffsettingComponent',
        { type: Sequelize.FLOAT, allowNull: true },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'savingsEnhancedRateComponent', { transaction });
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'savingsOffsettingComponent', { transaction });

      await queryInterface.removeColumn('ParticipantAccountTrails', 'savingsEnhancedRateComponent', { transaction });
      await queryInterface.removeColumn('ParticipantAccountTrails', 'savingsOffsettingComponent', { transaction });
    });
  },
};
