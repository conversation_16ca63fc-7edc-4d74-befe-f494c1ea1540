'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.addColumn('CashPools', 'estimateRatesCalculationLog', {
      type: Sequelize.JSON,
      allowNull: true,
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.removeColumn('CashPools', 'estimateRatesCalculationLog');
  },
};
