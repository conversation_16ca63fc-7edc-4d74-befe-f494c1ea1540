'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Guarantees', 'guarantorId', { transaction }),
        queryInterface.removeColumn('Guarantees', 'principalId', { transaction }),
        queryInterface.addColumn(
          'Guarantees',
          'guarantor',
          {
            type: Sequelize.JSON,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'principal',
          {
            type: Sequelize.JSON,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'clientId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Clients',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'editable',
          {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Guarantees', 'guarantor', { transaction }),
        queryInterface.removeColumn('Guarantees', 'principal', { transaction }),
        queryInterface.addColumn(
          'Guarantees',
          'guarantorId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Companies',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'principalId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Companies',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.removeColumn('Guarantees', 'clientId', { transaction }),
        queryInterface.removeColumn('Guarantees', 'editable', { transaction }),
      ]);
    });
  },
};
