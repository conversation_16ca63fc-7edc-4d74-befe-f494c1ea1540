'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('TopCurrencyAccountAuditTrails', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      cashPoolAuditTrailId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        onDelete: 'CASCADE',
        references: {
          model: 'CashPoolAuditTrails',
          key: 'id',
        },
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      currency: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      interestType: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      overnightRate: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      creditInterestRate: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      debitInterestRate: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      participants: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('TopCurrencyAccountAuditTrails');
  },
};
