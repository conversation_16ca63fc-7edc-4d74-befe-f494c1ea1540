'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn('Loans', 'note', { type: Sequelize.TEXT, allowNull: true }, { transaction });
      await queryInterface.changeColumn(
        'BackToBackLoans',
        'note',
        { type: Sequelize.TEXT, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Guarantees',
        'note',
        { type: Sequelize.TEXT, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CreditRatings',
        'note',
        { type: Sequelize.TEXT, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Companies',
        'note',
        { type: Sequelize.TEXT, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CompanyAuditTrails',
        'note',
        { type: Sequelize.TEXT, allowNull: true },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn('Loans', 'note', { type: Sequelize.STRING, allowNull: true }, { transaction });
      await queryInterface.changeColumn(
        'BackToBackLoans',
        'note',
        { type: Sequelize.STRING, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Guarantees',
        'note',
        { type: Sequelize.STRING, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CreditRatings',
        'note',
        { type: Sequelize.STRING, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Companies',
        'note',
        { type: Sequelize.STRING, allowNull: true },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CompanyAuditTrails',
        'note',
        { type: Sequelize.STRING, allowNull: true },
        { transaction },
      );
    });
  },
};
