'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Payments',
        'referenceRate',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'Payments',
        'interestCalculationDate',
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Payments', 'referenceRate', { transaction });
      await queryInterface.removeColumn('Payments', 'interestCalculationDate', { transaction });
    });
  },
};
