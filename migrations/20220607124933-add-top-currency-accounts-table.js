'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'TopCurrencyAccounts',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cashPoolId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPools',
              key: 'id',
            },
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          currency: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          interestType: {
            type: Sequelize.ENUM('fixed', 'float'),
            allowNull: false,
          },
          overnightRate: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          creditInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          debitInterestRate: {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          totalInterestPayableToLeader: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          totalInterestReceivableFromLeader: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          grossBenefit: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'topCurrencyAccountId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'TopCurrencyAccounts',
            key: 'id',
          },
        },
        { transaction },
      );

      await queryInterface.removeColumn('CashPoolBatches', 'totalInterestPayableToLeader', { transaction });
      await queryInterface.removeColumn('CashPoolBatches', 'totalInterestReceivableFromLeader', { transaction });
      await queryInterface.removeColumn('CashPoolBatches', 'grossBenefit', { transaction });

      await queryInterface.changeColumn(
        'CashPoolBatches',
        'startDate',
        {
          type: Sequelize.DATEONLY,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPoolBatches',
        'endDate',
        {
          type: Sequelize.DATEONLY,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'topCurrencyAccountId', { transaction });
      await queryInterface.dropTable('TopCurrencyAccounts', { transaction });

      await queryInterface.addColumn(
        'CashPoolBatches',
        'totalInterestPayableToLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatches',
        'totalInterestReceivableFromLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatches',
        'grossBenefit',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'CashPoolBatches',
        'startDate',
        {
          type: Sequelize.DATE,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPoolBatches',
        'endDate',
        {
          type: Sequelize.DATE,
          allowNull: false,
        },
        { transaction },
      );
    });
  },
};
