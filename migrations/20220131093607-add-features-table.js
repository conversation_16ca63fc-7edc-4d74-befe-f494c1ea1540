'use strict';
const { featureNames } = require('../enums');
const { getDefaultClientFeatureValues } = require('../utils/featureUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'Features',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          name: {
            type: Sequelize.STRING,
            unique: true,
            allowNull: false,
          },
          fullName: {
            type: Sequelize.STRING,
            unique: true,
            allowNull: false,
          },
          path: {
            type: Sequelize.STRING,
            unique: true,
            allowNull: true,
          },
          isModule: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
          note: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'Client_Features',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          clientId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Clients',
              key: 'id',
            },
          },
          featureId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Features',
              key: 'id',
            },
          },
          isEnabled: {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
            allowNull: false,
          },
          values: {
            type: Sequelize.JSON,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      const now = new Date();
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            name: featureNames.PAYMENT,
            fullName: 'Payments',
            path: '/payments',
            isModule: true,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.CASH_POOL,
            fullName: 'Cash Pools',
            path: '/cash-pools',
            isModule: true,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.CREDIT_RATING,
            fullName: 'Credit Ratings',
            path: '/credit-rating',
            isModule: true,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },

          {
            name: featureNames.LOAN,
            fullName: 'Loans',
            path: null,
            isModule: false,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.GUARANTEE,
            fullName: 'Guarantees',
            path: null,
            isModule: false,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },

          {
            name: featureNames.CURRENCY,
            fullName: 'Currency',
            path: null,
            isModule: false,
            note: '{"currencies": ["USD", "EUR", "GBP"]}',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.GEOGRAPHY_DATA,
            fullName: 'Geography Data',
            path: null,
            isModule: false,
            note: 'Either `full` (country provider data) or `basic` (regional provider data)',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.USER_NUMBER,
            fullName: 'User Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.LOAN_NUMBER,
            fullName: 'Loan Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.GUARANTEE_NUMBER,
            fullName: 'Guarantee Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.CREDIT_RATING_NUMBER,
            fullName: 'Credit Rating Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.CASH_POOL_NUMBER,
            fullName: 'Cash Pool Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
          {
            name: featureNames.FINANCING_ADVISORY,
            fullName: 'Financing Advisory',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: now,
            updatedAt: now,
          },
        ],
        { transaction },
      );

      const [clients] = await queryInterface.sequelize.query('SELECT * FROM public."Clients"', { transaction });
      const [features] = await queryInterface.sequelize.query('SELECT * FROM public."Features"', { transaction });
      const clientFeatureInserts = [];
      const clientFeatures = [];

      for (const client of clients) {
        for (const feature of features) {
          clientFeatures.push({
            clientId: client.id,
            featureId: feature.id,
            isEnabled: true,
            values: getDefaultClientFeatureValues(feature.name),
            createdAt: now,
            updatedAt: now,
          });
        }
        clientFeatureInserts.push(
          queryInterface.bulkInsert(
            'Client_Features',
            clientFeatures,
            { transaction },
            { values: { type: new Sequelize.JSON() } },
          ),
        );
        clientFeatures.length = 0;
      }
      await Promise.all(clientFeatureInserts);
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.dropTable('Client_Features');
      await queryInterface.dropTable('Features');
    });
  },
};
