'use strict';
const { formatDate } = require('../utils/dates');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query('SELECT * FROM "CreditRatings";', { transaction });
      const queryRes = queryResponse[0];
      const creditRatingFiles = [];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const { id, company, createdAt, updatedAt, pdf } = queryRes[i];
        creditRatingFiles.push({
          creditRatingId: id,
          name: `${company.name}_${formatDate(createdAt)}`,
          extension: '.pdf',
          label: 'Credit Rating',
          file: pdf,
          mimeType: 'application/pdf',
          status: 'Final',
          isGenerated: true,
          createdAt: createdAt,
          updatedAt: createdAt,
        });
      }

      await queryInterface.bulkInsert('CreditRatingFiles', creditRatingFiles, { transaction });

      await queryInterface.removeColumn('CreditRatings', 'pdf', { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CreditRatings',
        'pdf',
        {
          type: Sequelize.BLOB,
          allowNull: true,
        },
        { transaction },
      );

      const queryResponse = await queryInterface.sequelize.query('SELECT * FROM "CreditRatingFiles";', { transaction });
      const queryRes = queryResponse[0];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const { creditRatingId, file } = queryRes[i];
        await queryInterface.bulkUpdate('CreditRatings', { pdf: file }, { id: creditRatingId }, { transaction });
      }

      await queryInterface.changeColumn(
        'CreditRatings',
        'pdf',
        {
          type: Sequelize.BLOB,
          allowNull: false,
        },
        { transaction },
      );
    });
  },
};
