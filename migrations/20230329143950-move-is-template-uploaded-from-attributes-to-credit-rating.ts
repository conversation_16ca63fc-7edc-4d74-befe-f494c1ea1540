'use strict';
import { QueryInterface, Transaction } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.removeColumn('CreditRatingAttributes', 'isTemplateUploaded', { transaction });
      await queryInterface.addColumn(
        'CreditRatings',
        'isTemplateUploaded',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );

      await queryInterface.removeColumn('CreditRatingAttributesDrafts', 'isTemplateUploaded', { transaction });
      await queryInterface.addColumn(
        'CreditRatingDrafts',
        'isTemplateUploaded',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.addColumn(
        'CreditRatingAttributes',
        'isTemplateUploaded',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.removeColumn('CreditRatings', 'isTemplateUploaded', { transaction });

      await queryInterface.addColumn(
        'CreditRatingAttributesDrafts',
        'isTemplateUploaded',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.removeColumn('CreditRatingDrafts', 'isTemplateUploaded', { transaction });
    });
  },
};
