'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('Companies', 'assessmentName'),
        queryInterface.removeColumn('CompanyAuditTrails', 'assessmentName'),
        queryInterface.renameColumn('Companies', 'assessmentAnswers', 'assessment'),
        queryInterface.renameColumn('CompanyAuditTrails', 'assessmentAnswers', 'assessment'),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('Companies', 'assessmentName', {
          type: Sequelize.STRING,
        }),
        queryInterface.addColumn('CompanyAuditTrails', 'assessmentName', {
          type: Sequelize.STRING,
        }),
        queryInterface.renameColumn('Companies', 'assessment', 'assessmentAnswers'),
        queryInterface.renameColumn('CompanyAuditTrails', 'assessment', 'assessmentAnswers'),
      ]);
    });
  },
};
