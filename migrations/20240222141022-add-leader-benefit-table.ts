'use strict';
import { cashPoolLeaderBenefitRepository } from '../repositories';
import insertB2BData from '../backToBackDataImport/insertB2BData';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('CashPoolLeaderBenefits', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolId: {
          type: Sequelize.INTEGER,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPools',
            key: 'id',
          },
        },
        cashPoolBatchId: {
          type: Sequelize.INTEGER,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolBatches',
            key: 'id',
          },
        },
        leaderBenefit: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        date: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
          allowNull: false,
        },
      });

      const [leaderBenefit] = (await queryInterface.sequelize.query(`
        SELECT trail."leaderBenefit", "cashPoolId", "date", "cashPoolBatchId"
          FROM "Cash_Pool_Participants" participant
          JOIN "CashPoolParticipantAccounts" account ON participant.id = account."cashPoolParticipantId"
          JOIN "ParticipantAccountTrails" trail ON account.id = trail."participantAccountId" 
          WHERE participant."isLeader" = true;
      `)) as unknown as [Array<{ cashPoolId: number; cashPoolBatchId: number; leaderBenefit: number; date: Date }>];
      console.log('leaderBenefit :>> ', leaderBenefit);
      await Promise.all(leaderBenefit.map((lB) => cashPoolLeaderBenefitRepository.createLeaderBenefit(lB)));

      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'leaderBenefit');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'leaderBenefit');

      await insertB2BData();
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolParticipantAccounts', 'leaderBenefit', {
        type: Sequelize.FLOAT,
        allowNull: true,
      });
      await queryInterface.addColumn('ParticipantAccountTrails', 'leaderBenefit', {
        type: Sequelize.FLOAT,
        allowNull: true,
      });
      await queryInterface.dropTable('CashPoolLeaderBenefits');
    });
  },
};
