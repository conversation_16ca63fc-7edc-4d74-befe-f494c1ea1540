'use strict';
const { cashPoolEnums } = require('../enums');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CashPools',
        'interestType',
        {
          type: Sequelize.ENUM('fixed', 'float'),
          allowNull: false,
          defaultValue: 'fixed',
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'CashPools',
        'overnightRate',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.createTable(
        'CashPoolBatches',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cashPoolId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPools',
              key: 'id',
            },
          },
          createdByUserId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'RESTRICT',
            references: {
              model: 'Users',
              key: 'id',
            },
          },
          status: {
            type: Sequelize.ENUM(...Object.values(cashPoolEnums.batchStatus)),
            defaultValue: cashPoolEnums.batchStatus.UNPOOLED,
            allowNull: false,
          },
          totalInterestPayableToLeader: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          totalInterestReceivableFromLeader: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          startDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          endDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          grossBenefit: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'CashPoolBatchFiles',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cashPoolBatchId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPoolBatches',
              key: 'id',
            },
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          extension: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          mimeType: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'CashPoolBatch_ParticipantPayments',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cashPoolBatchId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CashPoolBatches',
              key: 'id',
            },
          },
          cashPoolParticipantId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Cash_Pool_Participants',
              key: 'id',
            },
          },
          interestPayable: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          interestReceivable: {
            type: Sequelize.FLOAT,
            allowNull: true,
          },
          isPaid: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.sequelize.query('TRUNCATE TABLE "CashPoolParticipantTrails"');
      await queryInterface.addColumn(
        'CashPoolParticipantTrails',
        'cashPoolBatchId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolBatches',
            key: 'id',
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPools', 'interestType');
      await queryInterface.removeColumn('CashPools', 'overnightRate');
      await queryInterface.removeColumn('CashPoolParticipantTrails', 'cashPoolBatchId');
      await queryInterface.dropTable('CashPoolBatch_ParticipantPayments', { transaction });
      await queryInterface.dropTable('CashPoolBatchFiles', { transaction });
      await queryInterface.dropTable('CashPoolBatches', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_CashPools_interestType";', { transaction });
    });
  },
};
