'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Loans',
        'dayCount',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.sequelize.query('UPDATE "Loans" SET "dayCount" = :dayCount', {
        replacements: { dayCount: 'ACT/365' },
        transaction,
      });

      await queryInterface.changeColumn(
        'Loans',
        'dayCount',
        { type: Sequelize.STRING, allowNull: false },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.removeColumn('Loans', 'dayCount');
  },
};
