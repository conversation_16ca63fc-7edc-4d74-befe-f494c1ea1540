'use strict';
import { QueryInterface } from 'sequelize';

export = {
  async up(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('BackToBackLoans', 'standardRemuneration', {
        type: Sequelize.JSON,
      });
      await queryInterface.addColumn('BackToBackLoans', 'riskTakerId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    });
  },
  async down(queryInterface: QueryInterface) {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('BackToBackLoans', 'standardRemuneration');
      await queryInterface.removeColumn('BackToBackLoans', 'riskTakerId');
    });
  },
};
