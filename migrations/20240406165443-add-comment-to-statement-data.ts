'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolStatementData', 'comment', {
        type: Sequelize.STRING,
        allowNull: true,
      });
      await queryInterface.addColumn('CashPoolStatementData', 'cashPoolBatchId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: 'SET NULL',
        references: {
          model: 'CashPoolBatches',
          key: 'id',
        },
      });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolStatementData', 'comment');
      await queryInterface.removeColumn('CashPoolStatementData', 'cashPoolBatchId');
    });
  },
};
