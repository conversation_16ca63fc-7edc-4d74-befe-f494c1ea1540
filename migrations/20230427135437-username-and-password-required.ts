'use strict';
import { QueryInterface, Transaction } from 'sequelize';

/** @type {import('sequelize-cli').Migration} */
export = {
  async up(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.changeColumn(
        'Users',
        'username',
        {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Users',
        'fullName',
        {
          type: Sequelize.STRING,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  async down(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.changeColumn(
        'Users',
        'username',
        {
          type: Sequelize.STRING,
          allowNull: true,
          unique: true,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Users',
        'fullName',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
    });
  },
};
