/**
 * Adds new value to roles enum, superadmin. Removes old enum value, client.
 * User remains user, admin becomes superadmin. After that admin is basically a new role.
 */

'use strict';
const { Op } = require('sequelize');

const rolesEnum = require('../enums/roles');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn('Users', 'role', {
        type: Sequelize.TEXT,
      });
      await queryInterface.sequelize.query('DROP TYPE "enum_Users_role";', { transaction });

      await queryInterface.changeColumn(
        'Users',
        'role',
        {
          type: Sequelize.ENUM(rolesEnum.SUPERADMIN, rolesEnum.ADMIN, rolesEnum.USER),
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.bulkUpdate(
        'Users',
        {
          role: rolesEnum.SUPERADMIN,
        },
        {
          role: {
            [Op.eq]: rolesEnum.ADMIN,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn('Users', 'role', {
        type: Sequelize.TEXT,
      });

      await queryInterface.sequelize.query('DROP TYPE "enum_Users_role";', { transaction });

      await queryInterface.bulkUpdate(
        'Users',
        {
          role: rolesEnum.ADMIN,
        },
        {
          role: {
            [Op.eq]: rolesEnum.SUPERADMIN,
          },
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Users',
        'role',
        {
          type: Sequelize.ENUM(rolesEnum.ADMIN, 'client', rolesEnum.USER),
          allowNull: false,
        },
        { transaction },
      );
    });
  },
};
