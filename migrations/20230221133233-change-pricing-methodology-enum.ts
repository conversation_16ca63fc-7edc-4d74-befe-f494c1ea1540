'use strict';
import { QueryInterface } from 'sequelize';
import { reportEnums } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Guarantees', 'pricingMethodology', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_Guarantees_pricingMethodology";', { transaction });
      await queryInterface.addColumn(
        'Guarantees',
        'pricingMethodology',
        {
          type: Sequelize.ENUM(...Object.values(reportEnums.pricingMethodologyEnum)),
          allowNull: false,
          defaultValue: reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {},
};
