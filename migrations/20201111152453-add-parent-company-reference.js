'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('Companies', 'parentCompanyId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
      references: {
        model: 'Companies',
        key: 'id',
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeConstraint('Companies', 'Companies_parentCompanyId_fkey'),
        queryInterface.changeColumn('Companies', 'parentCompanyId', {
          type: Sequelize.INTEGER,
          allowNull: true,
        }),
      ]);
    });
  },
};
