'use strict';
import { QueryInterface, Transaction } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.createTable(
        'BackToBackLoanLegs',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          backToBackLoanId: {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'BackToBackLoans',
              key: 'id',
            },
          },
          lender: {
            type: Sequelize.JSON,
            allowNull: false,
          },
          borrower: {
            type: Sequelize.JSON,
            allowNull: false,
          },
          report: {
            type: Sequelize.JSON,
            allowNull: true,
          },
          ordinal: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.NOW,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.renameColumn('BackToBackLoans', 'lenders', 'ultimateLender', { transaction });
      await queryInterface.renameColumn('BackToBackLoans', 'borrowers', 'ultimateBorrower', { transaction });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.dropTable('BackToBackLoanLegs', { transaction });

      await queryInterface.renameColumn('BackToBackLoans', 'ultimateLender', 'lenders', { transaction });
      await queryInterface.renameColumn('BackToBackLoans', 'ultimateBorrower', 'borrowers', { transaction });
    });
  },
};
