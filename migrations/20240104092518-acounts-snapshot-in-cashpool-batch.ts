'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolBatches', 'accountsSnapshot', {
        type: Sequelize.JSON,
        allowNull: false,
      });

      await queryInterface.changeColumn('CashPoolStatementData', 'statementDate', {
        type: Sequelize.DATEONLY,
        allowNull: true,
      });
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolBatches', 'accountsSnapshot');

      await queryInterface.changeColumn('CashPoolStatementData', 'statementDate', {
        type: Sequelize.DATEONLY,
        allowNull: false,
      });
    });
  },
};
