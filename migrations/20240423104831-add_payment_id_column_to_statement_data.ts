'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolStatementData', 'cashPoolPaymentId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: 'CASCADE',
        references: {
          model: 'CashPoolBatch_ParticipantPayments',
          key: 'id',
        },
      });

      await queryInterface.addColumn('CashPoolParticipantAccounts', 'generateInterestStatementData', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolStatementData', 'cashPoolPaymentId');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'generateInterestStatementData');
    });
  },
};
