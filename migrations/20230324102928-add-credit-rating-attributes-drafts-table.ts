'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.createTable('CreditRatingAttributesDrafts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      creditRatingDraftId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        onDelete: 'CASCADE',
        references: {
          model: 'CreditRatingDrafts',
          key: 'id',
        },
      },
      naceSector: {
        type: Sequelize.STRING,
      },
      naceSectorGroup: {
        type: Sequelize.STRING,
      },
      option: {
        type: Sequelize.ENUM('Consolidated', 'Unconsolidated'),
      },
      months: {
        type: Sequelize.INTEGER,
      },
      currency: {
        type: Sequelize.STRING,
      },
      exchangeRate: {
        type: Sequelize.FLOAT,
      },
      fixedAssets: {
        type: Sequelize.FLOAT,
      },
      intangibleFixedAssets: {
        type: Sequelize.FLOAT,
      },
      tangibleFixedAssets: {
        type: Sequelize.FLOAT,
      },
      otherFixedAssets: {
        type: Sequelize.FLOAT,
      },
      currentAssets: {
        type: Sequelize.FLOAT,
      },
      stocks: {
        type: Sequelize.FLOAT,
      },
      debtors: {
        type: Sequelize.FLOAT,
      },
      otherCurrentAssets: {
        type: Sequelize.FLOAT,
      },
      cashAndCashEquivalent: {
        type: Sequelize.FLOAT,
      },
      totalAssets: {
        type: Sequelize.FLOAT,
      },
      shareholdersFunds: {
        type: Sequelize.FLOAT,
      },
      capital: {
        type: Sequelize.FLOAT,
      },
      otherShareholdersFunds: {
        type: Sequelize.FLOAT,
      },
      treasuryShares: {
        type: Sequelize.FLOAT,
      },
      nonCurrentLiabilities: {
        type: Sequelize.FLOAT,
      },
      longTermDebt: {
        type: Sequelize.FLOAT,
      },
      otherNonCurrentLiabilities: {
        type: Sequelize.FLOAT,
      },
      provisions: {
        type: Sequelize.FLOAT,
      },
      currentLiabilities: {
        type: Sequelize.FLOAT,
      },
      loans: {
        type: Sequelize.FLOAT,
      },
      creditors: {
        type: Sequelize.FLOAT,
      },
      otherCurrentLiabilities: {
        type: Sequelize.FLOAT,
      },
      totalShareFundsAndLiabilities: {
        type: Sequelize.FLOAT,
      },
      operatingRevenueTurnover: {
        type: Sequelize.FLOAT,
      },
      sales: {
        type: Sequelize.FLOAT,
      },
      materialCosts: {
        type: Sequelize.FLOAT,
      },
      costOfEmployees: {
        type: Sequelize.FLOAT,
      },
      EBITDA: {
        type: Sequelize.FLOAT,
      },
      depreciation: {
        type: Sequelize.FLOAT,
      },
      EBIT: {
        type: Sequelize.FLOAT,
      },
      financialRevenue: {
        type: Sequelize.FLOAT,
      },
      financialExpenses: {
        type: Sequelize.FLOAT,
      },
      financialPL: {
        type: Sequelize.FLOAT,
      },
      PLBeforeTax: {
        type: Sequelize.FLOAT,
      },
      taxation: {
        type: Sequelize.FLOAT,
      },
      PLAfterTax: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherRevenue: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherExpenses: {
        type: Sequelize.FLOAT,
      },
      extrAndOtherPL: {
        type: Sequelize.FLOAT,
      },
      PLForPeriod: {
        type: Sequelize.FLOAT,
      },
      costOfGoodSold: {
        type: Sequelize.FLOAT,
      },
      otherOperatingExpenses: {
        type: Sequelize.FLOAT,
      },
      grossProfit: {
        type: Sequelize.FLOAT,
      },
      overriddenStatus: {
        type: Sequelize.JSON,
      },
      isTemplateUploaded: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.dropTable('CreditRatingAttributesDrafts');
  },
};
