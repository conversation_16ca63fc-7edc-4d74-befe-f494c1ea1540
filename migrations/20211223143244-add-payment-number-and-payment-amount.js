'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Payments',
        'paymentNumber',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'Payments',
        'paymentAmount',
        {
          type: Sequelize.DECIMAL,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Payments', 'paymentNumber', { transaction });
      await queryInterface.removeColumn('Payments', 'paymentAmount', { transaction });
    });
  },
};
