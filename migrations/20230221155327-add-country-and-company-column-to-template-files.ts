'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.addColumn(
        'TemplateFiles',
        'country',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'TemplateFiles',
        'companyId',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: 'CASCADE',
          references: {
            model: 'Companies',
            key: 'id',
          },
        },
        { transaction },
      );

      await queryInterface.removeConstraint('TemplateFiles', 'TemplateFiles_clientId_label_key', { transaction });
      // These indicies are constraints that make sure that combination of clientId, country, companyId and label fields are unique
      // That constraint is implemented like this rather than with single unique constraint because we want to treat NULLs as not distinctive values
      await queryInterface.sequelize.query(
        'CREATE UNIQUE INDEX client_country_company_label_unique_index ON "TemplateFiles" ("clientId", "country", "companyId", "label") WHERE ("country", "companyId") IS NOT NULL',
        { transaction },
      );
      await queryInterface.sequelize.query(
        'CREATE UNIQUE INDEX client_country_label_unique_index ON "TemplateFiles" ("clientId", "country", "label") WHERE "country" IS NOT NULL AND "companyId" IS NULL',
        { transaction },
      );
      await queryInterface.sequelize.query(
        'CREATE UNIQUE INDEX client_company_label_unique_index ON "TemplateFiles" ("clientId", "companyId", "label") WHERE "companyId" IS NOT NULL AND "country" IS NULL',
        { transaction },
      );
      await queryInterface.sequelize.query(
        'CREATE UNIQUE INDEX client_label_unique_index ON "TemplateFiles" ("clientId", "label") WHERE ("country", "companyId") IS NULL',
        { transaction },
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.removeColumn('TemplateFiles', 'country', { transaction });
      await queryInterface.removeColumn('TemplateFiles', 'companyId', { transaction });

      await queryInterface.addConstraint('TemplateFiles', {
        fields: ['clientId', 'label'],
        type: 'unique',
        name: 'TemplateFiles_clientId_label_key',
        transaction,
      });
      await queryInterface.removeIndex('TemplateFiles', 'client_country_company_label_unique_index', { transaction });
      await queryInterface.removeIndex('TemplateFiles', 'client_country_label_unique_index', { transaction });
      await queryInterface.removeIndex('TemplateFiles', 'client_company_label_unique_index', { transaction });
      await queryInterface.removeIndex('TemplateFiles', 'client_label_unique_index', { transaction });
    });
  },
};
