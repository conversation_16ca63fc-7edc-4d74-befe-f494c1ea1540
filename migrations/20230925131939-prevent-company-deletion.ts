'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        `
        ALTER TABLE "CashPools"
        DROP CONSTRAINT IF EXISTS "CashPools_leaderId_fkey";
        
        ALTER TABLE "CashPools"
        ADD CONSTRAINT "CashPools_leaderId_fkey"
        FOREIGN KEY ("leaderId")
        REFERENCES "Companies" (id)
        ON DELETE NO ACTION;
        
        ALTER TABLE "Cash_Pool_Participants"
        DROP CONSTRAINT IF EXISTS "Cash_Pool_Participants_companyId_fkey";
        
        ALTER TABLE "Cash_Pool_Participants"
        ADD CONSTRAINT "Cash_Pool_Participants_companyId_fkey"
        FOREIGN KEY ("companyId")
        REFERENCES "Companies" (id)
        ON DELETE NO ACTION;
        `,
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        `
        ALTER TABLE "CashPools"
        DROP CONSTRAINT IF EXISTS "CashPools_leaderId_fkey";
        
        ALTER TABLE "CashPools"
        ADD CONSTRAINT "CashPools_leaderId_fkey"
        FOREIGN KEY ("leaderId")
        REFERENCES "Companies" (id)
        ON DELETE CASCADE;
        
        ALTER TABLE "Cash_Pool_Participants"
        DROP CONSTRAINT IF EXISTS "Cash_Pool_Participants_companyId_fkey";
        
        ALTER TABLE "Cash_Pool_Participants"
        ADD CONSTRAINT "Cash_Pool_Participants_companyId_fkey"
        FOREIGN KEY ("companyId")
        REFERENCES "Companies" (id)
        ON DELETE CASCADE;
        `,
        { transaction },
      );
    });
  },
};
