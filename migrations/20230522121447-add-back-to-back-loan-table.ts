'use strict';
import { QueryInterface } from 'sequelize';

/** @type {import('sequelize-cli').Migration} */
export = {
  async up(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.createTable('BackToBackLoans', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      clientId: {
        type: Sequelize.INTEGER,
        onDelete: 'CASCADE',
        references: {
          model: 'Clients',
          key: 'id',
        },
      },
      lenders: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      borrowers: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      issueDate: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      originalIssueDate: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      maturityDate: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      currency: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      amount: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      paymentFrequency: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      seniority: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      rateType: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      report: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      pricingApproach: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      editable: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      type: {
        type: Sequelize.ENUM('Bullet', 'Balloon'),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      note: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      isPortfolio: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      capm: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      capmOverride: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      capmRecommendation: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      expectedLoss: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      expectedLossOverride: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      expectedLossRecommendation: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      overrideToggles: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      calculationLog: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      isThirdParty: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      totalInterest: {
        type: Sequelize.DECIMAL,
        allowNull: true,
      },
      movedToAnalysesDate: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false,
      },
      updatedBy: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      finalizedBy: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false,
      },
    });
  },

  async down(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.dropTable('BackToBackLoans');
  },
};
