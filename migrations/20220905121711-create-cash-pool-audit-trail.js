'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('CashPoolAuditTrails', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      cashPoolId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        onDelete: 'CASCADE',
        references: {
          model: 'CashPools',
          key: 'id',
        },
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      creditInterestRate: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      debitInterestRate: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      overnightRate: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      operatingCost: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      operatingCostMarkup: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      riskAnalysisAnswers: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      assessment: {
        type: Sequelize.ENUM('Low', 'Medium', 'High'),
        allowNull: false,
      },
      totalRisk: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      participants: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('CashPoolAuditTrails');
  },
};
