'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.changeColumn('CreditRatingAttributes', 'stocks', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'debtors', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'treasuryShares', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'longTermDebt', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'loans', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'creditors', {
          type: Sequelize.FLOAT,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'interestPaid', {
          type: Sequelize.FLOAT,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.changeColumn('CreditRatingAttributes', 'stocks', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'debtors', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'treasuryShares', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'longTermDebt', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'loans', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'creditors', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
        queryInterface.changeColumn('CreditRatingAttributes', 'interestPaid', {
          type: Sequelize.FLOAT,
          allowNull: false,
        }),
      ]);
    });
  },
};
