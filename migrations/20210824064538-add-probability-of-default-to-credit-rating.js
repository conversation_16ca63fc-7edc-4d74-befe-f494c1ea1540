'use strict';
const { CreditRating, CreditRatingAttribute } = require('../models');
const { createCreditRatingReport } = require('../services');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CreditRatings',
        'probabilityOfDefault',
        {
          type: Sequelize.REAL,
          allowNull: true,
        },
        { transaction },
      );

      const creditRatings = await CreditRating.findAll({
        include: {
          model: CreditRatingAttribute,
          as: 'attributes',
        },
        transaction,
      });

      for (let i = 0, len = creditRatings.length; i < len; i++) {
        const creditRating = {
          ...creditRatings[i].dataValues,
          attributes: {
            ...creditRatings[i].dataValues.attributes.dataValues,
          },
        };

        const { probabilityOfDefault } = await createCreditRatingReport(creditRating, null, false);

        await queryInterface.bulkUpdate(
          'CreditRatings',
          { probabilityOfDefault },
          { id: creditRating.id },
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'CreditRatings',
        'probabilityOfDefault',
        {
          type: Sequelize.REAL,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('CreditRatings', 'probabilityOfDefault');
  },
};
