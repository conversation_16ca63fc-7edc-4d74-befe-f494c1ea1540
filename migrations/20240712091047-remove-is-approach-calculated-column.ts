'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Loans', 'isApproachCalculated', { transaction });

      await queryInterface.addColumn(
        'Clients',
        'isLoanApproachCalculated',
        { type: Sequelize.BOOLEAN, allowNull: false, defaultValue: true },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Loans',
        'isApproachCalculated',
        { type: Sequelize.BOOLEAN, allowNull: false, defaultValue: true },
        { transaction },
      );

      await queryInterface.removeColumn('Clients', 'isLoanApproachCalculated', { transaction });
    });
  },
};
