'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('Loans', 'pricingApproach', {
          type: Sequelize.STRING,
        }),
        queryInterface.addColumn('Guarantees', 'pricingApproach', {
          type: Sequelize.STRING,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('Loans', 'pricingApproach'),
        queryInterface.removeColumn('Guarantees', 'pricingApproach'),
      ]);
    });
  },
};
