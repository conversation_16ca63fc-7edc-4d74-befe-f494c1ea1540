'use strict';
const { stringTenors } = require('../utils/providerDataUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.bulkDelete('ProviderData', {}, { transaction });
      await queryInterface.removeIndex(
        'ProviderData',
        ['issueDate', 'currency', 'seniority', 'region', 'industry', 'rating'],
        { transaction },
      );
      await queryInterface.removeColumn('ProviderData', 'industry', { transaction });
      await queryInterface.removeColumn('ProviderData', 'covered', { transaction });
      await queryInterface.removeColumn('ProviderData', 'baseDataPoint', { transaction });

      await Promise.all(
        stringTenors.map((tenor) => {
          return [
            queryInterface.addColumn(
              'ProviderData',
              `fixed${tenor}`,
              {
                type: Sequelize.REAL,
                allowNull: true,
              },
              { transaction },
            ),
            queryInterface.addColumn(
              'ProviderData',
              `float${tenor}`,
              {
                type: Sequelize.REAL,
                allowNull: true,
              },
              { transaction },
            ),
          ];
        }),
      );

      await queryInterface.changeColumn(
        'ProviderData',
        'curveName',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'ProviderData',
        'paymentFrequency',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.addIndex(
        'ProviderData',
        ['issueDate', 'region', 'country', 'industryGroup', 'currency', 'seniority'],
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ProviderData');
  },
};
