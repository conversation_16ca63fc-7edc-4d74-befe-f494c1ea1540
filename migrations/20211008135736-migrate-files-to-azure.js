'use strict';
require('dotenv').config();
const { BlobServiceClient } = require('@azure/storage-blob');
const { Readable } = require('stream');

const creditRatingFileRepository = require('../repositories/creditRatingFileRepository');
const guaranteeFileRepository = require('../repositories/guaranteeFileRepository');
const loanFileRepository = require('../repositories/loanFileRepository');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
      const containerClient = blobServiceClient.getContainerClient(process.env.CONTAINER_NAME);

      const filesUploadPromises = [];

      const loanFiles = await loanFileRepository.getAll();
      for (const { dataValues } of loanFiles) {
        filesUploadPromises.push(
          containerClient.getBlockBlobClient(`loan/${dataValues.id}`).uploadStream(Readable.from(dataValues.file)),
        );
      }

      const guaranteeFiles = await guaranteeFileRepository.getAll();
      for (const { dataValues } of guaranteeFiles) {
        filesUploadPromises.push(
          containerClient.getBlockBlobClient(`guarantee/${dataValues.id}`).uploadStream(Readable.from(dataValues.file)),
        );
      }

      const creditRatingFiles = await creditRatingFileRepository.getAll();
      for (const { dataValues } of creditRatingFiles) {
        filesUploadPromises.push(
          containerClient
            .getBlockBlobClient(`creditRating/${dataValues.id}`)
            .uploadStream(Readable.from(dataValues.file)),
        );
      }

      await Promise.all(filesUploadPromises);
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async () => {});
  },
};
