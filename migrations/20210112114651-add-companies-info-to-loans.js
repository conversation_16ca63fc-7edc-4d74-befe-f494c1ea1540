'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Loans', 'lenderId', { transaction }),
        queryInterface.removeColumn('Loans', 'borrowerId', { transaction }),
        queryInterface.addColumn(
          'Loans',
          'lender',
          {
            type: Sequelize.JSON,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Loans',
          'borrower',
          {
            type: Sequelize.JSON,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Loans',
          'clientId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Clients',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Loans',
          'editable',
          {
            type: Sequelize.BOOLEAN,
            defaultValue: true,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Loans', 'lender', { transaction }),
        queryInterface.removeColumn('Loans', 'borrower', { transaction }),
        queryInterface.addColumn(
          'Loans',
          'lenderId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Companies',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Loans',
          'borrowerId',
          {
            type: Sequelize.INTEGER,
            onDelete: 'CASCADE',
            references: {
              model: 'Companies',
              key: 'id',
            },
          },
          { transaction },
        ),
        queryInterface.removeColumn('Loans', 'clientId', { transaction }),
        queryInterface.removeColumn('Loans', 'editable', { transaction }),
      ]);
    });
  },
};
