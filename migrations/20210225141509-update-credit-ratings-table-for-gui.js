'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.bulkDelete('CreditRatings', {}, { transaction });
      await queryInterface.sequelize.query('CREATE SEQUENCE CreditRatings_id_seq OWNED BY "CreditRatings".id;', {
        transaction,
      });
      await queryInterface.sequelize.query(
        'ALTER TABLE "CreditRatings" ALTER COLUMN "id" SET DEFAULT nextval(\'CreditRatings_id_seq\')',
        { transaction },
      );

      await Promise.all([
        queryInterface.removeColumn('CreditRatings', 'name', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'fiscalYear', { transaction }),

        queryInterface.addColumn(
          'CreditRatings',
          'isPortfolio',
          {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'editable',
          {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: true,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'company',
          {
            type: Sequelize.JSON,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'status',
          {
            type: Sequelize.ENUM('Draft', 'Final'),
            allowNull: false,
            defaultValue: 'Draft',
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'note',
          {
            type: Sequelize.STRING,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'closingDate',
          {
            type: Sequelize.DATE,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'naceSector',
          {
            type: Sequelize.STRING,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'option',
          {
            type: Sequelize.ENUM('Consolidated', 'Unconsolidated'),
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'months',
          {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currency',
          {
            type: Sequelize.STRING,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'exchangeRate',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'fixedAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'intangibleFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'tangibleFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherFixedAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currentAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'stocks',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'debtors',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherCurrentAssets',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'cashAndCashEquivalent',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'totalAssets',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'shareholdersFunds',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'capital',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherShareholdersFunds',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'treasuryShares',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'nonCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'longTermDebt',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherNonCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'provisions',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'currentLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'loans',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'creditors',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherCurrentLiabilities',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'totalShareFundsAndLiabilities',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'operatingRevenueTurnover',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'sales',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'materialCosts',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'costOfEmployees',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'EBITDA',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'depreciation',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'EBIT',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialRevenue',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'interestPaid',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'financialPL',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLBeforeTax',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'taxation',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLAfterTax',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherRevenue',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'extrAndOtherPL',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'PLForPeriod',
          {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'costOfGoodSold',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'otherOperatingExpenses',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'grossProfit',
          {
            type: Sequelize.FLOAT,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query('DROP SEQUENCE IF EXISTS CreditRatings_id_seq CASCADE;', { transaction });

      await Promise.all([
        queryInterface.addColumn(
          'CreditRatings',
          'name',
          {
            type: Sequelize.STRING,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'CreditRatings',
          'fiscalYear',
          {
            type: Sequelize.STRING,
          },
          { transaction },
        ),

        queryInterface.removeColumn('CreditRatings', 'naceSector', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'note', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'isPortfolio', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'editable', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'company', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'status', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'closingDate', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'option', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'months', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'currency', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'exchangeRate', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'fixedAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'intangibleFixedAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'tangibleFixedAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherFixedAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'currentAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'stocks', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'debtors', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherCurrentAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'cashAndCashEquivalent', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'totalAssets', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'shareholdersFunds', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'capital', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherShareholdersFunds', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'treasuryShares', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'nonCurrentLiabilities', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'longTermDebt', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherNonCurrentLiabilities', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'provisions', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'currentLiabilities', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'loans', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'creditors', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherCurrentLiabilities', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'totalShareFundsAndLiabilities', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'operatingRevenueTurnover', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'sales', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'materialCosts', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'costOfEmployees', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'EBITDA', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'depreciation', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'EBIT', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'financialRevenue', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'financialExpenses', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'interestPaid', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'financialPL', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'PLBeforeTax', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'taxation', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'PLAfterTax', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'extrAndOtherRevenue', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'extrAndOtherExpenses', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'extrAndOtherPL', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'PLForPeriod', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'costOfGoodSold', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'otherOperatingExpenses', { transaction }),
        queryInterface.removeColumn('CreditRatings', 'grossProfit', { transaction }),
      ]);

      await queryInterface.sequelize.query('DROP TYPE "enum_CreditRatings_option";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_CreditRatings_status";', { transaction });
    });
  },
};
