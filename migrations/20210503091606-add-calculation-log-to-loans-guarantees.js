'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('Loans', 'calculationLog', {
          type: Sequelize.TEXT,
          allowNull: true,
        }),
        queryInterface.addColumn('Guarantees', 'calculationLog', {
          type: Sequelize.TEXT,
          allowNull: true,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('Loans', 'calculationLog'),
        queryInterface.removeColumn('Guarantees', 'calculationLog'),
      ]);
    });
  },
};
