'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeConstraint('Companies', 'Companies_parentCompanyId_fkey');
      return Promise.all([
        queryInterface.changeColumn('Companies', 'parentCompanyId', {
          type: Sequelize.INTEGER,
          allowNull: true,
        }),
        queryInterface.addConstraint('Companies', {
          fields: ['parentCompanyId'],
          name: 'Companies_parentCompanyId_fkey',
          type: 'foreign key',
          references: {
            table: 'Companies',
            field: 'id',
          },
          onDelete: 'SET NULL',
          onUpdate: 'CASCADE',
        }),
        queryInterface.changeColumn('CompanyAuditTrails', 'parentCompanyId', {
          type: Sequelize.INTEGER,
          allowNull: true,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeConstraint('Companies', 'Companies_parentCompanyId_fkey');
      return Promise.all([
        queryInterface.changeColumn('Companies', 'parentCompanyId', {
          type: Sequelize.INTEGER,
          allowNull: true,
        }),
        queryInterface.addConstraint('Companies', {
          fields: ['parentCompanyId'],
          name: 'Companies_parentCompanyId_fkey',
          type: 'foreign key',
          references: {
            table: 'Companies',
            field: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        }),
        queryInterface.changeColumn('CompanyAuditTrails', 'parentCompanyId', {
          type: Sequelize.INTEGER,
          allowNull: true,
        }),
      ]);
    });
  },
};
