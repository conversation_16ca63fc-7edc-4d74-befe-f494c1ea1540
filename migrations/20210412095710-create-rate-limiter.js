'use strict';
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('RateLimiter', {
      key: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.STRING,
      },
      points: {
        allowNull: false,
        defaultValue: 0,
        type: Sequelize.INTEGER,
      },
      expire: {
        type: Sequelize.BIGINT,
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('RateLimiter');
  },
};
