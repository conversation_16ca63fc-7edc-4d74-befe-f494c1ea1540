'use strict';
import { QueryInterface } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'CashPoolStatementDataFiles',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          extension: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          mimeType: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          startDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          createdByUserId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'RESTRICT',
            references: {
              model: 'Users',
              key: 'id',
            },
          },
          cashPoolId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPools',
            key: 'id',
            },
          },
          endDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.dropTable('CashPoolStatementDataFiles', { transaction });
    });
  },
};
