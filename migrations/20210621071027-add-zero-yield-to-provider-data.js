'use strict';
const { stringTenors } = require('../utils/providerDataUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('ProviderData', 'paymentFrequency', { transaction });

      await Promise.all(
        stringTenors.map((tenor) => {
          return queryInterface.addColumn(
            'ProviderData',
            `yield${tenor}`,
            {
              type: Sequelize.REAL,
              allowNull: true,
            },
            { transaction },
          );
        }),
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'ProviderData',
        'paymentFrequency',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );

      await Promise.all(
        stringTenors.map((tenor) => {
          return queryInterface.removeColumn('ProviderData', `yield${tenor}`, { transaction });
        }),
      );
    });
  },
};
