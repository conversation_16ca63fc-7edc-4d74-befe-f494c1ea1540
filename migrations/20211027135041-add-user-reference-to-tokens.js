'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Tokens',
        'userId',
        {
          type: Sequelize.INTEGER,
          defaultValue: 1,
          allowNull: false,
          references: {
            model: 'Users',
            key: 'id',
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Tokens', 'userId', { transaction });
    });
  },
};
