'use strict';

const FEATURE_ID = 14;

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            id: FEATURE_ID,
            name: 'loanGuaranteeNumber',
            fullName: 'Loan and Guarantee Number',
            path: null,
            isModule: false,
            note: 'A number',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        { transaction },
      );

      const [clients] = await queryInterface.sequelize.query('SELECT * FROM "Clients";', { transaction });

      const clientFeatures = [];
      for (const client of clients) {
        clientFeatures.push({
          clientId: client.id,
          featureId: FEATURE_ID,
          isEnabled: false,
          values: 20,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      await queryInterface.bulkInsert(
        'Client_Features',
        clientFeatures,
        { transaction },
        { values: { type: new Sequelize.JSON() } },
      );
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.bulkDelete('Features', { id: FEATURE_ID }, { transaction });
    });
  },
};
