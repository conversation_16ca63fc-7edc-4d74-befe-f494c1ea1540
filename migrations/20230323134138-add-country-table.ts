'use strict';
import { QueryInterface, Transaction } from 'sequelize';

import { countryToISOMapping } from '../utils/creditRatingUtils';
import { countryToRegionMapper, countryFlagMapper } from '../utils/reportUtils';
import { CountryToCuftCountryCodeEnum } from '../enums/cuftData';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.createTable(
        'Countries',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          isoCode: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          region: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          cuftCode: {
            type: Sequelize.STRING,
            defaultValue: 'UNKNOWN',
            allowNull: false,
          },
          flagEmoji: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            defaultValue: new Date(),
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            defaultValue: new Date(),
            allowNull: false,
          },
        },
        { transaction },
      );

      const now = new Date();
      const countriesToInsert = Object.keys(countryToISOMapping).map((name) => ({
        name,
        isoCode: countryToISOMapping[name as keyof typeof countryToISOMapping],
        region: countryToRegionMapper[name as keyof typeof countryToRegionMapper],
        cuftCode: CountryToCuftCountryCodeEnum[name as keyof typeof CountryToCuftCountryCodeEnum] || 'UNKNOWN',
        flagEmoji: countryFlagMapper[name as keyof typeof countryFlagMapper],
        createdAt: now,
        updatedAt: now,
      }));

      await queryInterface.bulkInsert('Countries', countriesToInsert, { transaction });

      await queryInterface.bulkDelete('WHTOrigins', {});

      await queryInterface.addColumn(
        'WHTOrigins',
        'countryId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Countries',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.removeColumn('WHTOrigins', 'country', { transaction });

      await queryInterface.addColumn(
        'WHTRecipients',
        'countryId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Countries',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.removeColumn('WHTRecipients', 'country', { transaction });
    });
  },
  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.bulkDelete('WHTOrigins', {});
      await queryInterface.removeColumn('WHTOrigins', 'countryId', { transaction });
      await queryInterface.removeColumn('WHTRecipients', 'countryId', { transaction });
      await queryInterface.dropTable('Countries', { transaction });

      await queryInterface.addColumn(
        'WHTOrigins',
        'country',
        { type: Sequelize.STRING, allowNull: false },
        { transaction },
      );
      await queryInterface.addColumn(
        'WHTRecipients',
        'country',
        { type: Sequelize.STRING, allowNull: false },
        { transaction },
      );
    });
  },
};
