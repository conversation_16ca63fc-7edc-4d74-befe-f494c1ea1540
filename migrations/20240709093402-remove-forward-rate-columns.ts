'use strict';
import { QueryInterface } from 'sequelize';

import { stringTenors } from '../utils/providerDataUtils';

export = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all(
        stringTenors.map((tenor) => queryInterface.removeColumn('ProviderData', `forward${tenor}`, { transaction })),
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all(
        stringTenors.map((tenor) =>
          queryInterface.addColumn(
            'ProviderData',
            `forward${tenor}`,
            { type: Sequelize.REAL, allowNull: true },
            { transaction },
          ),
        ),
      );
    });
  },
};
