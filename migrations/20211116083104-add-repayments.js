'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'Payments',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          loanId: {
            type: Sequelize.INTEGER,
            allowNull: true,
            onDelete: 'CASCADE',
            references: {
              model: 'Loans',
              key: 'id',
            },
          },
          guaranteeId: {
            type: Sequelize.INTEGER,
            allowNull: true,
            onDelete: 'CASCADE',
            references: {
              model: 'Guarantees',
              key: 'id',
            },
          },
          interestRatePerInterestRepaymentFrequency: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          isPaid: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
          },
          paymentDueDate: {
            type: Sequelize.DATE,
            allowNull: true,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'Loans',
        'totalInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.createTable(
        'BulletPayments',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          paymentId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Payments',
              key: 'id',
            },
          },
          interestPaymentPerInterestRepaymentFrequency: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'Guarantees',
        'totalInterest',
        {
          type: Sequelize.DECIMAL,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.createTable(
        'BalloonPayments',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          paymentId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Payments',
              key: 'id',
            },
          },
          compoundedInterest: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          additionalInterest: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          compoundingPeriodEndDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Loans', 'totalInterest', { transaction });
      await queryInterface.removeColumn('Guarantees', 'totalInterest', { transaction });

      await queryInterface.dropTable('BulletPayments', { transaction });
      await queryInterface.dropTable('BalloonPayments', { transaction });
      await queryInterface.dropTable('Payments', { transaction });
    });
  },
};
