'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('CashPools', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        leaderId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Companies',
            key: 'id',
          },
        },
        clientId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Clients',
            key: 'id',
          },
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        country: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        type: {
          type: Sequelize.ENUM('Notional', 'Physical'),
          allowNull: false,
        },
        currencies: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        riskAnalysisAnswers: {
          type: Sequelize.JSON,
          allowNull: false,
        },
        assessment: {
          type: Sequelize.ENUM('Low', 'Medium', 'High'),
          allowNull: false,
        },
        creditInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        debitInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        grossBenefit: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        operatingCost: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        operatingCostMarkup: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        note: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      await queryInterface.createTable('Cash_Pool_Participants', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        companyId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Companies',
            key: 'id',
          },
        },
        cashPoolId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPools',
            key: 'id',
          },
        },
        balance: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        isLeader: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        creditInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedCreditInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        debitInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedDebitInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedCreditInterestReceived: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedDebitInterestPaid: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        netInterestSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        grossInterestSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        netOffsettingSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        grossOffsettingSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        currency: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        deletedAt: {
          type: Sequelize.DATE,
          allowNull: true,
        },
      });

      await queryInterface.createTable('CashPoolParticipantTrails', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolParticipantId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        balance: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        isLeader: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        creditInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedCreditInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        debitInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedDebitInterestRate: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedCreditInterestReceived: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        adjustedDebitInterestPaid: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        netInterestSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        grossInterestSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        netOffsettingSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        grossOffsettingSynergy: {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        currency: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      await queryInterface.createTable('CashPoolOperatingExpenses', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolParticipantId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        paymentToLeader: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        paymentDate: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      await queryInterface.createTable('CashPoolFiles', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPools',
            key: 'id',
          },
        },
        label: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        extension: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        status: {
          type: Sequelize.ENUM('Final', 'Draft'),
          defaultValue: 'Draft',
          allowNull: false,
        },
        mimeType: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        isGenerated: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
      });
    });
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.dropTable('CashPoolParticipantTrails');
      await queryInterface.dropTable('CashPoolOperatingExpenses');
      await queryInterface.dropTable('Cash_Pool_Participants');
      await queryInterface.dropTable('CashPoolFiles');
      await queryInterface.dropTable('CashPools');
    });
  },
};
