'use strict';
import { Buffer } from 'buffer';
import fs from 'fs/promises';
import * as azureServices from '../services/azureService';
import { clientTemplateRepository } from '../repositories';
import { TemplateFileLabelsEnum } from '../enums/templateFiles';

const SCATEC_ID = 2;
const DNV_ID = 3;

export = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.createTable(
        'TemplateFiles',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          clientId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Clients',
              key: 'id',
            },
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          label: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          type: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          extension: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          mimeType: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
        },
        {
          uniqueKeys: {
            clientLabelUnique: { fields: ['clientId', 'label'] },
          },
        },
      );
      /* -------------SCATEC------------- */
      const scatecLoanAgreementFixedFile = await fs.readFile('static/Scatec_loan_agreement_(fixed).docx');
      const scatecLoanAgreementFixedDb = await clientTemplateRepository.createTemplateFile__Migrations(
        SCATEC_ID,
        'Scatec Loan Agreement Fixed',
        TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${scatecLoanAgreementFixedDb.id}`,
        scatecLoanAgreementFixedFile,
        Buffer.byteLength(scatecLoanAgreementFixedFile),
      );

      const scatecLoanAgreementFloatFile = await fs.readFile('static/Scatec_loan_agreement_(float).docx');
      const scatecLoanAgreementFloatDb = await clientTemplateRepository.createTemplateFile__Migrations(
        SCATEC_ID,
        'Scatec Loan Agreement Float',
        TemplateFileLabelsEnum.LOAN_FLOAT_AGREEMENT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${scatecLoanAgreementFloatDb.id}`,
        scatecLoanAgreementFloatFile,
        Buffer.byteLength(scatecLoanAgreementFloatFile),
      );

      const scatecStandaloneReportFile = await fs.readFile('static/Scatec_Loan_Standalone_(template).docx');
      const scatecStandaloneReportDb = await clientTemplateRepository.createTemplateFile__Migrations(
        SCATEC_ID,
        'Scatec Loan Standalone',
        TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${scatecStandaloneReportDb.id}`,
        scatecStandaloneReportFile,
        Buffer.byteLength(scatecStandaloneReportFile),
      );

      const scatecImplicitSupportReportFile = await fs.readFile(
        'static/Scatec_Loan_Implicit_Support_Adjusted_(template).docx',
      );
      const scatecImplicitSupportReportDb = await clientTemplateRepository.createTemplateFile__Migrations(
        SCATEC_ID,
        'Scatec Loan Implicit Support Adjusted',
        TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${scatecImplicitSupportReportDb.id}`,
        scatecImplicitSupportReportFile,
        Buffer.byteLength(scatecImplicitSupportReportFile),
      );

      /* -------------DNV------------- */
      const dnvLoanAgreementFixedFile = await fs.readFile('static/DNV_loan_agreement_(fixed).docx');
      const dnvLoanAgreementFixedDb = await clientTemplateRepository.createTemplateFile__Migrations(
        DNV_ID,
        'DNV Loan Agreement Fixed',
        TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${dnvLoanAgreementFixedDb.id}`,
        dnvLoanAgreementFixedFile,
        Buffer.byteLength(dnvLoanAgreementFixedFile),
      );

      const dnvLoanAgreementFloatFile = await fs.readFile('static/DNV_loan_agreement_(float).docx');
      const dnvLoanAgreementFloatDb = await clientTemplateRepository.createTemplateFile__Migrations(
        DNV_ID,
        'DNV Loan Agreement Fixed',
        TemplateFileLabelsEnum.LOAN_FLOAT_AGREEMENT,
        'loan',
      );
      await azureServices.uploadFile(
        `template/${dnvLoanAgreementFloatDb.id}`,
        dnvLoanAgreementFloatFile,
        Buffer.byteLength(dnvLoanAgreementFloatFile),
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.dropTable('TemplateFiles');
  },
};
