'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Guarantees',
        'paymentFrequency',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.bulkUpdate(
        'Guarantees',
        { paymentFrequency: 'Annual' },
        { paymentFrequency: null },
        { transaction },
      );
      await queryInterface.changeColumn(
        'Guarantees',
        'paymentFrequency',
        {
          type: Sequelize.STRING,
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Guarantees', 'paymentFrequency');
  },
};
