'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.addColumn(
        'CreditRatingAttributes',
        'overriddenStatus',
        {
          type: Sequelize.JSON,
          defaultValue: {
            fixedAssets: false,
            currentAssets: false,
            totalAssets: false,
            shareholdersFunds: false,
            nonCurrentLiabilities: false,
            currentLiabilities: false,
            totalShareFundsAndLiabilities: false,
            EBITDA: false,
            EBIT: false,
            financialPL: false,
            PLBeforeTax: false,
            PLAfterTax: false,
            extrAndOtherPL: false,
            PLForPeriod: false,
            grossProfit: false,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.removeColumn('CreditRatingAttributes', 'overriddenStatus', { transaction });
    });
  },
};
