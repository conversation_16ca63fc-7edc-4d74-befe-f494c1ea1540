'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.dropTable('CashPoolOperatingExpenses');

      await queryInterface.createTable('CashPoolStatementData', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolAccountId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolParticipantAccounts',
            key: 'id',
          },
        },
        transactionReferenceNumber: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        statementNumber: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        position: {
          type: Sequelize.ENUM('C', 'D'),
          allowNull: false,
        },
        balance: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        date: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      await queryInterface.addColumn('CashPoolParticipantAccounts', 'accountIdentification', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('CashPoolOperatingExpenses', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        cashPoolParticipantId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        paymentToLeader: {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        paymentDate: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      await queryInterface.dropTable('CashPoolStatementData');

      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'accountIdentification');
    });
  },
};
