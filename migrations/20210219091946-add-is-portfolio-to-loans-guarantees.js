'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('Loans', 'isPortfolio', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        }),
        queryInterface.addColumn('Guarantees', 'isPortfolio', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('Loans', 'isPortfolio'),
        queryInterface.removeColumn('Guarantees', 'isPortfolio'),
      ]);
    });
  },
};
