'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.createTable('Notifications', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        createdByUserId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Users',
            key: 'id',
          },
        },
        url: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        action: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        note: {
          type: Sequelize.TEXT,
        },
        createdAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
        updatedAt: {
          allowNull: false,
          type: Sequelize.DATE,
        },
      });

      await queryInterface.createTable(
        'Receiving_Users_Notifications',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          receivingUserId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Users',
              key: 'id',
            },
          },
          notificationId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Notifications',
              key: 'id',
            },
          },
          isHandled: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
        },
        {
          uniqueKeys: {
            receivingUsersNotificationsUnique: { fields: ['receivingUserId', 'notificationId'], customIndex: true },
          },
        },
      );

      await queryInterface.addColumn('Users', 'areNotificationsMuted', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      });

      await queryInterface.changeColumn('Tokens', 'accessToken', {
        type: Sequelize.TEXT,
        allowNull: false,
      });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.dropTable('Receiving_Users_Notifications');
      await queryInterface.dropTable('Notifications');
      await queryInterface.removeColumn('Users', 'areNotificationsMuted');
      await queryInterface.bulkUpdate('Tokens', { accessToken: '' });
      await queryInterface.changeColumn('Tokens', 'accessToken', {
        type: Sequelize.STRING,
        allowNull: false,
      });
    });
  },
};
