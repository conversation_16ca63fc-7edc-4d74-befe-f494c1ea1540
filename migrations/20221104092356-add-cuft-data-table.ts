'use strict';
import { QueryInterface, Transaction } from 'sequelize';
import { ClientFeatureType, ClientType } from '../types';

import { featureNames, cuftDataEnums } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.createTable(
        'CuftDataFiles',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          name: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          extension: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          mimeType: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'CuftData',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          cuftDataFileId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'CuftDataFiles',
              key: 'id',
            },
          },
          filingCompanyName: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          cuftBorrowerName: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          cuftBorrowerCountry: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          primarySic: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          allCurrencies: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          moodyPrincipalObligorCreditRating: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          spyPrincipalObligorCreditRating: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          cuftTrancheExecutionDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          cuftTrancheMaturityDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          cuftTrancheTenor: {
            type: Sequelize.FLOAT,
            allowNull: false,
          },
          cuftTrancheAssetClass: {
            type: Sequelize.ENUM(...Object.values(cuftDataEnums.TrancheAssetClassEnum)),
            allowNull: false,
          },
          cuftTrancheType: {
            type: Sequelize.ENUM(cuftDataEnums.TrancheTypeEnum.Revolver, cuftDataEnums.TrancheTypeEnum.Term),
            allowNull: false,
          },
          cuftTranchePrimaryReferenceRate: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          trancheOrderID: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          reviewCtrlrID: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          exhibitLink: {
            type: Sequelize.TEXT,
            allowNull: false,
          },
          deliveryDate: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction },
      );

      const now = new Date();
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            name: featureNames.CUFT_DATA,
            fullName: 'CUFT Data',
            path: '/cuft',
            isModule: true,
            note: 'No values',
            createdAt: now,
            updatedAt: now,
          },
        ],
        { transaction },
      );

      const [queryClients] = await queryInterface.sequelize.query('SELECT * FROM public."Clients";', { transaction });
      const [queryFeatures] = await queryInterface.sequelize.query(
        `SELECT * FROM public."Features" WHERE name = '${featureNames.CUFT_DATA}';`,
        { transaction },
      );

      const clients = queryClients as ClientType[];
      const feature = queryFeatures[0] as ClientFeatureType;
      const clientFeatures = [];
      const clientFeatureInserts = [];
      for (const client of clients) {
        clientFeatures.push({
          clientId: client.id,
          featureId: feature.id,
          isEnabled: true,
          values: null,
          createdAt: now,
          updatedAt: now,
        });

        clientFeatureInserts.push(queryInterface.bulkInsert('Client_Features', clientFeatures, { transaction }));
        clientFeatures.length = 0;
      }
      await Promise.all(clientFeatureInserts);
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.dropTable('CuftData', { transaction });
      await queryInterface.dropTable('CuftDataFiles', { transaction });

      await queryInterface.sequelize.query('DROP TYPE "enum_CuftData_cuftTrancheAssetClass";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_CuftData_cuftTrancheType";', { transaction });

      await queryInterface.bulkDelete('Features', { name: featureNames.CUFT_DATA }, { transaction });
    });
  },
};
