'use strict';
import { QueryInterface } from 'sequelize';
import { reportEnums } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.addColumn('Guarantees', 'pricingMethodology', {
      type: Sequelize.ENUM(...Object.values(reportEnums.pricingMethodologyEnum)),
      allowNull: false,
      defaultValue: reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH,
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.removeColumn('Guarantees', 'pricingMethodology');
  },
};
