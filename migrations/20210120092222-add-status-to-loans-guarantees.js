'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      return Promise.all([
        queryInterface.addColumn(
          'Loans',
          'status',
          {
            type: Sequelize.ENUM('Final', 'Draft'),
            defaultValue: 'Draft',
            allowNull: false,
          },
          { transaction },
        ),
        queryInterface.addColumn(
          'Guarantees',
          'status',
          {
            type: Sequelize.ENUM('Final', 'Draft'),
            defaultValue: 'Draft',
            allowNull: false,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.removeColumn('Loans', 'status', { transaction }),
        queryInterface.removeColumn('Guarantees', 'status', { transaction }),
      ]);
    });
  },
};
