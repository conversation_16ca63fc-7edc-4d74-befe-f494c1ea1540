'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'WHTOrigins',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          country: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          dividend: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          interest: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          royalty: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: new Date(),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: new Date(),
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'WHTRecipients',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          originId: {
            type: Sequelize.INTEGER,
            allowNull: true,
            onDelete: 'CASCADE',
            references: {
              model: 'WHTOrigins',
              key: 'id',
            },
          },
          country: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          dividend: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          interest: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          royalty: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          interestExplanation: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DATE,
          },
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.dropTable('WHTRecipients', { transaction });
      await queryInterface.dropTable('WHTOrigins', { transaction });
    });
  },
};
