'use strict';

import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.createTable(
        'SocialLogins',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          userId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Users',
              key: 'id',
            },
          },
          provider: {
            type: Sequelize.ENUM('azure', 'google'),
            allowNull: false,
          },
          credential: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: new Date(),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: new Date(),
          },
        },
        { transaction },
      );

      const [users] = await queryInterface.sequelize.query('SELECT * FROM "Users"', { transaction });

      const insertIntoSocialLoginPromises = users
        .filter((u: any) => !!u.azureEmail)
        .map((user: any) =>
          queryInterface.sequelize.query(
            `INSERT INTO "SocialLogins" ("userId", "provider", "credential") VALUES (${user.id}, 'azure', '${user.azureEmail}')`,
            { transaction },
          ),
        );

      const updateEmailField = users
        .filter((u: any) => !!u.azureEmail)
        .map((user: any) =>
          queryInterface.sequelize.query(`UPDATE "Users" SET email = '${user.azureEmail}' WHERE id = ${user.id}`, {
            transaction,
          }),
        );

      await queryInterface.removeColumn('Users', 'azureEmail', { transaction });
      await Promise.all([...insertIntoSocialLoginPromises, ...updateEmailField]);
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.dropTable('SocialLogins');

    await queryInterface.addColumn('Users', 'azureEmail', {
      type: Sequelize.STRING,
      allowNull: true,
      unique: false,
    });
  },
};
