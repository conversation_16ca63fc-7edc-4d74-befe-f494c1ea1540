'use strict';
const { dateFormats, decimalPoints, timezones } = require('../enums');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Users',
        'dateFormat',
        {
          type: Sequelize.ENUM(...Object.values(dateFormats)),
          allowNull: false,
          defaultValue: dateFormats['YYYY-MM-DD'],
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'Users',
        'timezone',
        {
          type: Sequelize.ENUM(...Object.values(timezones)),
          allowNull: false,
          defaultValue: timezones['Etc/GMT'],
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'Users',
        'decimalPoint',
        {
          type: Sequelize.ENUM(...Object.values(decimalPoints)),
          allowNull: false,
          defaultValue: decimalPoints.COMMA,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Users', 'dateFormat', { transaction });
      await queryInterface.removeColumn('Users', 'timezone', { transaction });
      await queryInterface.removeColumn('Users', 'decimalPoint', { transaction });

      await queryInterface.sequelize.query('DROP TYPE "enum_Users_dateFormat";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_Users_timezone";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_Users_decimalPoint";', { transaction });
    });
  },
};
