'use strict';
import { QueryInterface, Transaction } from 'sequelize';
import { cuftDataEnums } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.changeColumn('CuftDataCurrencies', 'currency', {
        type: Sequelize.TEXT,
      });
      await queryInterface.sequelize.query('DROP TYPE "enum_CuftDataCurrencies_currency";', { transaction });

      await queryInterface.changeColumn(
        'CuftDataCurrencies',
        'currency',
        {
          type: Sequelize.ENUM(...Object.values(cuftDataEnums.CurrencyToCuftCurrencyEnum)),
          allowNull: false,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {},
};
