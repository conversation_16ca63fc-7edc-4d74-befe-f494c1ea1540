'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'netInterestSynergy');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'grossInterestSynergy');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'netOffsettingSynergy');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'grossOffsettingSynergy');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'savingsEnhancedRateComponent');
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'savingsOffsettingComponent');

      await queryInterface.removeColumn('ParticipantAccountTrails', 'netInterestSynergy');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'grossInterestSynergy');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'netOffsettingSynergy');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'grossOffsettingSynergy');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'savingsEnhancedRateComponent');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'savingsOffsettingComponent');
    });
  },

  down: async () => {},
};
