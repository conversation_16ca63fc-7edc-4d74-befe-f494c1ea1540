'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'CashPoolStatementData',
        'balanceChange',
        {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 0,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'CashPools',
        'externalId',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolStatementData', 'balanceChange', { transaction });
      await queryInterface.removeColumn('CashPools', 'externalId', { transaction });
    });
  },
};
