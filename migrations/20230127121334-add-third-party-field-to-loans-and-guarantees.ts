'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.addColumn(
        'Loans',
        'isThirdParty',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'Guarantees',
        'isThirdParty',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        { transaction },
      );

      // Seniority is null for third-party guarantees
      // Change seniorty column to allow null
      await queryInterface.changeColumn(
        'Guarantees',
        'seniority',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction: any) => {
      await queryInterface.removeColumn('Loans', 'isThirdParty', { transaction });
      await queryInterface.removeColumn('Guarantees', 'isThirdParty', { transaction });

      await queryInterface.changeColumn(
        'Guarantees',
        'seniority',
        {
          type: Sequelize.STRING,
          allowNull: false,
          defaultValue: 'undefined',
        },
        { transaction },
      );
    });
  },
};
