'use strict';
import { QueryInterface, Transaction } from 'sequelize';

import { betaUtils } from '../utils/betaUtils';
import backToBackDataImport from '../backToBackDataImport';
import { regionAbbreviations } from '../utils/providerDataUtils/constants';
import regionalErpData from '../backToBackDataImport/regionalErp';

/** @type {import('sequelize-cli').Migration} */
export = {
  async up(queryInterface: QueryInterface, Sequelize: any) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.createTable(
        'Betas',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          industry: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          region: {
            type: Sequelize.ENUM(...Object.keys(betaUtils.regions)),
            allowNull: false,
          },
          numberOfFirms: {
            type: Sequelize.INTEGER,
            allowNull: true,
          },
          beta: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          deRatio: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          effectiveTaxRate: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          unleveredBeta: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          cashFirmValue: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          unleveredBetaCorrectedForCash: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          hiloRisk: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          standardDeviationOfEquity: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          standardDeviationInOperatingIncomeLast10Years: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          date: {
            type: Sequelize.DATEONLY,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'EquityRiskPremiums',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          countryId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            onDelete: 'CASCADE',
            references: {
              model: 'Countries',
              key: 'id',
            },
          },
          moodyRating: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          ratingBasedDefaultSpread: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          totalEquityRiskPremium: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          countryRiskPremium: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          sovereignCds: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          totalEquityRiskPremium2: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          countryRiskPremium3: {
            type: Sequelize.DECIMAL,
            allowNull: true,
          },
          date: {
            type: Sequelize.DATEONLY,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.createTable(
        'RegionalEquityRiskPremiums',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          region: {
            type: Sequelize.ENUM(...Object.keys(regionAbbreviations)),
            allowNull: false,
          },
          equityRiskPremium: {
            type: Sequelize.DECIMAL,
            allowNull: false,
          },
          date: {
            type: Sequelize.DATEONLY,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
          updatedAt: {
            type: Sequelize.DATEONLY,
            defaultValue: new Date(),
            allowNull: false,
          },
        },
        { transaction },
      );

      await queryInterface.bulkInsert('RegionalEquityRiskPremiums', regionalErpData);
    });

    await backToBackDataImport();
  },

  async down(queryInterface: QueryInterface) {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.dropTable('Betas', { transaction });
      await queryInterface.dropTable('EquityRiskPremiums', { transaction });
      await queryInterface.dropTable('RegionalEquityRiskPremiums', { transaction });

      await queryInterface.sequelize.query('DROP TYPE "enum_Betas_region";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_RegionalEquityRiskPremiums_region";', { transaction });
    });
  },
};
