'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('CashPoolParticipantAccounts', 'leaderBenefit', {
        type: Sequelize.FLOAT,
        allowNull: true,
      });
      await queryInterface.addColumn('ParticipantAccountTrails', 'leaderBenefit', {
        type: Sequelize.FLOAT,
        allowNull: true,
      });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'leaderBenefit');
      await queryInterface.removeColumn('ParticipantAccountTrails', 'leaderBenefit');
    });
  },
};
