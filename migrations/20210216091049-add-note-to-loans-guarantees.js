'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.addColumn('Loans', 'note', {
          type: Sequelize.STRING,
        }),
        queryInterface.addColumn('Guarantees', 'note', {
          type: Sequelize.STRING,
        }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(() => {
      return Promise.all([
        queryInterface.removeColumn('Loans', 'note'),
        queryInterface.removeColumn('Guarantees', 'note'),
      ]);
    });
  },
};
