'use strict';
import { QueryInterface, Transaction } from 'sequelize';

import { featureNames } from '../enums';
import { ClientFeatureType, ClientType } from '../types';

export = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      const now = new Date();
      await queryInterface.bulkInsert(
        'Features',
        [
          {
            name: featureNames.IS_TEMPLATE_CASH_POOL_BATCH_UPLOAD,
            fullName: 'Is Template Cash Pool Batch Upload',
            path: null,
            isModule: false,
            note: 'Determines whether users upload cash pool balances data through templates or is the data delivered some other way (SFTP)',
            createdAt: now,
            updatedAt: now,
          },
        ],
        { transaction },
      );

      const [queryClients] = await queryInterface.sequelize.query('SELECT * FROM public."Clients";', { transaction });
      const [queryFeatures] = await queryInterface.sequelize.query(
        `SELECT * FROM public."Features" WHERE name = '${featureNames.IS_TEMPLATE_CASH_POOL_BATCH_UPLOAD}';`,
        { transaction },
      );

      const clients = queryClients as ClientType[];
      const feature = queryFeatures[0] as ClientFeatureType;
      const clientFeatures = [];
      const clientFeatureInserts = [];
      for (const client of clients) {
        clientFeatures.push({
          clientId: client.id,
          featureId: feature.id,
          isEnabled: true,
          values: null,
          createdAt: now,
          updatedAt: now,
        });

        clientFeatureInserts.push(queryInterface.bulkInsert('Client_Features', clientFeatures, { transaction }));
        clientFeatures.length = 0;
      }
      await Promise.all(clientFeatureInserts);
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction: Transaction) => {
      await queryInterface.bulkDelete(
        'Features',
        { name: featureNames.IS_TEMPLATE_CASH_POOL_BATCH_UPLOAD },
        { transaction },
      );
    });
  },
};
