'use strict';

module.exports = {
  up: async (queryInterface) => {
    await queryInterface.sequelize.query(`
      ALTER TABLE "Cash_Pool_Participants"
        ADD CONSTRAINT "cashPoolCompanyParticipantUnique" UNIQUE ("companyId", "cashPoolId");
    `);
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.query(`
      ALTER TABLE "Cash_Pool_Participants"
        DROP CONSTRAINT "cashPoolCompanyParticipantUnique";
    `);
  },
};
