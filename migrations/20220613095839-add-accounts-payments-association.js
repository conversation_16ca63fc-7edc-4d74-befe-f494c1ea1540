'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolBatch_ParticipantPayments', 'cashPoolParticipantId', { transaction });
      await queryInterface.addColumn(
        'CashPoolBatch_ParticipantPayments',
        'cashPoolParticipantAccountId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolParticipantAccounts',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatch_ParticipantPayments',
        'creditorId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatch_ParticipantPayments',
        'debtorId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPools',
        'creditInterestRate',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPools',
        'debitInterestRate',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.removeColumn('ParticipantAccountTrails', 'cashPoolBatchId', { transaction });
      await queryInterface.addColumn(
        'ParticipantAccountTrails',
        'cashPoolBatchId',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolBatches',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'ParticipantAccountTrails',
        'currency',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolParticipantAccounts',
        'deletedAt',
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.removeColumn('Cash_Pool_Participants', 'deletedAt', { transaction });

      await queryInterface.addColumn(
        'CashPoolBatches',
        'totalInterestPayableToLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatches',
        'totalInterestReceivableFromLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'CashPoolBatches',
        'grossBenefit',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.removeColumn('TopCurrencyAccounts', 'totalInterestPayableToLeader', { transaction });
      await queryInterface.removeColumn('TopCurrencyAccounts', 'totalInterestReceivableFromLeader', { transaction });
      await queryInterface.removeColumn('TopCurrencyAccounts', 'grossBenefit', { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('CashPoolBatch_ParticipantPayments', 'cashPoolParticipantAccountId', {
        transaction,
      });
      await queryInterface.removeColumn('CashPoolBatch_ParticipantPayments', 'creditorId', { transaction });
      await queryInterface.removeColumn('CashPoolBatch_ParticipantPayments', 'debtorId', { transaction });
      await queryInterface.addColumn(
        'CashPoolBatch_ParticipantPayments',
        'cashPoolParticipantId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'Cash_Pool_Participants',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPools',
        'creditInterestRate',
        {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.changeColumn(
        'CashPools',
        'debitInterestRate',
        {
          type: Sequelize.FLOAT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.removeColumn('ParticipantAccountTrails', 'cashPoolBatchId', { transaction });
      await queryInterface.addColumn(
        'ParticipantAccountTrails',
        'cashPoolBatchId',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          onDelete: 'CASCADE',
          references: {
            model: 'CashPoolBatches',
            key: 'id',
          },
        },
        { transaction },
      );
      await queryInterface.removeColumn('ParticipantAccountTrails', 'currency', { transaction });
      await queryInterface.removeColumn('CashPoolParticipantAccounts', 'deletedAt', { transaction });
      await queryInterface.addColumn(
        'Cash_Pool_Participants',
        'deletedAt',
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.removeColumn('CashPoolBatches', 'totalInterestPayableToLeader', { transaction });
      await queryInterface.removeColumn('CashPoolBatches', 'totalInterestReceivableFromLeader', { transaction });
      await queryInterface.removeColumn('CashPoolBatches', 'grossBenefit', { transaction });

      await queryInterface.addColumn(
        'TopCurrencyAccounts',
        'totalInterestPayableToLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'TopCurrencyAccounts',
        'totalInterestReceivableFromLeader',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'TopCurrencyAccounts',
        'grossBenefit',
        {
          type: Sequelize.FLOAT,
          allowNull: true,
        },
        { transaction },
      );
    });
  },
};
