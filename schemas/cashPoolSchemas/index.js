'use strict';
const createBatchFromSftpDataSchema = require('./createBatchFromSftpDataSchema');
const createNordicCashPoolSchema = require('./createNordicCashPoolSchema');
const createNotionalCashPoolSchema = require('./createNotionalCashPoolSchema');
const createPhysicalCashPoolSchema = require('./createPhysicalCashPoolSchema');
const estimateParticipantRatesSchema = require('./estimateParticipantRatesSchema');
const exportCashPoolSchema = require('./exportCashPoolSchema');
const getCashPoolTrailsSchema = require('./getCashPoolTrailsSchema');
const getStructuralPositionsSchema = require('./getStructuralPositionsSchema');
const markCashPoolPaymentAsPaidSchema = require('./markCashPoolPaymentAsPaidSchema');
const updateCashPoolNoteSchema = require('./updateCashPoolNoteSchema');
const updateNordicCashPoolSchema = require('./updateNordicCashPoolSchema');
const updateNotionalCashPoolSchema = require('./updateNotionalCashPoolSchema');
const updatePhysicalCashPoolSchema = require('./updatePhysicalCashPoolSchema');
const updateParticipantUniqueIdSchema = require('./updateParticipantUniqueIdSchema');

module.exports = {
  createBatchFromSftpDataSchema,
  createPhysicalCashPoolSchema,
  createNordicCashPoolSchema,
  createNotionalCashPoolSchema,
  updatePhysicalCashPoolSchema,
  updateNordicCashPoolSchema,
  updateNotionalCashPoolSchema,
  updateCashPoolNoteSchema,
  getCashPoolTrailsSchema,
  getStructuralPositionsSchema,
  exportCashPoolSchema,
  markCashPoolPaymentAsPaidSchema,
  estimateParticipantRatesSchema,
  updateParticipantUniqueIdSchema,
};
