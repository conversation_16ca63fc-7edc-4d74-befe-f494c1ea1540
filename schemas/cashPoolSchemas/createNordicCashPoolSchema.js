'use strict';
const joi = require('../Joi');
const { getNordicPhysicalRiskAnalysisAnswers } = require('./riskAnalysisAnswers');

module.exports = joi.object({
  type: joi.string().allow('Physical', 'Notional', 'Nordic').required(),
  name: joi.string().required(),
  currencies: joi.string().required(),
  country: joi.string().required(),
  leaderId: joi.number().required(),
  operatingCost: joi.number().allow(null),
  operatingCostMarkup: joi.number().allow(null),
  riskAnalysisAnswers: getNordicPhysicalRiskAnalysisAnswers(joi),
  totalRisk: joi.number().required(),
  assessment: joi.string().functionalAnalysisAssessment().required(),
  topCurrencyAccounts: joi.array().items(
    joi.object({
      name: joi.string().required(),
      currency: joi.string().required(),
      interestType: joi.string().rateType().required(),
      creditInterestRate: joi.number().required(),
      debitInterestRate: joi.number().required(),
      overnightRate: joi.string().cashPoolOvernightRate().allow(null),
      accounts: joi.array().items(
        joi.object({
          companyId: joi.number().required(),
          creditInterestRate: joi.number().required(),
          debitInterestRate: joi.number().required(),
        }),
      ),
    }),
  ),
});
