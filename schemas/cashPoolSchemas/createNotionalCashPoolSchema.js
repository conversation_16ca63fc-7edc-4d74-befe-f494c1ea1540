'use strict';
const joi = require('../Joi');
const { getNotionalRiskAnalysisAnswers } = require('./riskAnalysisAnswers');

module.exports = joi.object({
  type: joi.string().allow('Physical', 'Notional', 'Nordic').required(),
  name: joi.string().required(),
  currencies: joi.string().required(),
  country: joi.string().required(),
  leaderId: joi.number().required(),
  creditInterestRate: joi.number().required(),
  debitInterestRate: joi.number().required(),
  interestType: joi.string().rateType().required(),
  overnightRate: joi.string().cashPoolOvernightRate().allow(null),
  operatingCost: joi.number().allow(null),
  operatingCostMarkup: joi.number().allow(null),
  riskAnalysisAnswers: getNotionalRiskAnalysisAnswers(joi),
  totalRisk: joi.number().required(),
  assessment: joi.string().functionalAnalysisAssessment().required(),
  accounts: joi.array().items(
    joi.object({
      companyId: joi.number().required(),
      creditInterestRate: joi.number().required(),
      debitInterestRate: joi.number().required(),
      currency: joi.string().required(),
    }),
  ),
});
