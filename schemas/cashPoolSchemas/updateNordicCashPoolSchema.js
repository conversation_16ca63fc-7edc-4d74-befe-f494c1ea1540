'use strict';
const joi = require('../Joi');
const { getNordicPhysicalRiskAnalysisAnswers } = require('./riskAnalysisAnswers');

module.exports = joi.object({
  name: joi.string().required(),
  operatingCost: joi.number().allow(null),
  operatingCostMarkup: joi.number().allow(null),
  riskAnalysisAnswers: getNordicPhysicalRiskAnalysisAnswers(joi),
  totalRisk: joi.number().required(),
  assessment: joi.string().functionalAnalysisAssessment().required(),
  topCurrencyAccounts: joi.array().items(
    joi.object({
      id: joi.number(),
      name: joi.string().required(),
      currency: joi.string(),
      creditInterestRate: joi.number().required(),
      debitInterestRate: joi.number().required(),
      interestType: joi.string().rateType().required(),
      overnightRate: joi.string().cashPoolOvernightRate().allow(null),
      accounts: joi.array().items(
        joi.object({
          companyId: joi.number().required(),
          creditInterestRate: joi.number().required(),
          debitInterestRate: joi.number().required(),
        }),
      ),
    }),
  ),
  shouldCreateAuditTrail: joi.boolean().required(),
});
