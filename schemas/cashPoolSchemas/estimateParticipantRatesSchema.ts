'use strict';
import { companySchema } from '../companySchema';
import joi from '../Joi';

module.exports = joi.object({
  participants: joi.array().items(companySchema),
  issueDate: joi.date().required(),
  rateType: joi
    .object({
      type: joi.string().rateType().required(),
    })
    .required(),
  creditRate: joi.number().required(),
  pricingApproach: joi.string().pricingApproach().required(),
  cashPoolDebitRate: joi.number().required(),
  currency: joi.string().required(),
});
