'use strict';
const joi = require('../Joi');
const { getNotionalRiskAnalysisAnswers } = require('./riskAnalysisAnswers');

module.exports = joi.object({
  name: joi.string().required(),
  creditInterestRate: joi.number().required(),
  debitInterestRate: joi.number().required(),
  interestType: joi.string().rateType().required(),
  operatingCost: joi.number().allow(null),
  operatingCostMarkup: joi.number().allow(null),
  overnightRate: joi.string().cashPoolOvernightRate().allow(null),
  riskAnalysisAnswers: getNotionalRiskAnalysisAnswers(joi),
  totalRisk: joi.number().required(),
  assessment: joi.string().functionalAnalysisAssessment().required(),
  accounts: joi.array().items(
    joi.object({
      companyId: joi.number().required(),
      creditInterestRate: joi.number().required(),
      debitInterestRate: joi.number().required(),
      currency: joi.string().required(),
    }),
  ),
  shouldCreateAuditTrail: joi.boolean().required(),
});
