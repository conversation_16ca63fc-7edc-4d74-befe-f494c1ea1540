const getNordicPhysicalRiskAnalysisAnswers = (joi) =>
  joi.object({
    guarantee: joi.boolean().required(),
    liquidityRisk1: joi.boolean().required(),
    liquidityRisk2: joi.boolean().required(),
    creditRisk1: joi.boolean().required(),
    creditRisk2: joi.boolean().required(),
    functions1: joi.boolean().required(),
    functions2: joi.boolean().required(),
    functions3: joi.boolean().required(),
    functions4: joi.boolean().required(),
    functions5: joi.boolean().required(),
    functions6: joi.boolean().required(),
  });

const getNotionalRiskAnalysisAnswers = (joi) =>
  joi.object({
    guarantee: joi.boolean().required(),
    liquidityRisk1: joi.boolean().required(),
    liquidityRisk2: joi.boolean().required(),
    creditRisk1: joi.boolean().required(),
    creditRisk2: joi.boolean().required(),
    fxRisk1: joi.boolean().required(),
    fxRisk2: joi.boolean().required(),
    functions1: joi.boolean().required(),
    functions2: joi.boolean().required(),
    functions3: joi.boolean().required(),
    functions4: joi.boolean().required(),
    functions5: joi.boolean().required(),
    functions6: joi.boolean().required(),
    functions7: joi.boolean().required(),
  });

module.exports = { getNordicPhysicalRiskAnalysisAnswers, getNotionalRiskAnalysisAnswers };
