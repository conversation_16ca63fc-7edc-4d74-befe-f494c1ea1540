import joi from './Joi';
import { companyInfoSchema } from './constants';

export const companySchema = joi
  .object({
    ...companyInfoSchema,
    creditRating: joi
      .object({
        rating: joi.string().rating().required().allow(null),
        ratingAdj: joi.string().rating().required().allow(null),
        probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
        probabilityOfDefaultAdj: joi.number().probabilityOfDefault().required().allow(null),
        cumulativeProbabilityOfDefault: joi.number().probabilityOfDefault().allow(null), // only applies to principal (in guarantees)
      })
      .required(),
  })
  .required();
