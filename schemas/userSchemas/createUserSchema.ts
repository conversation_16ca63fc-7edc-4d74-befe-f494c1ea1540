import { z } from 'zod';

import { rolesEnum } from '../../enums';

const sharedFields = {
  email: z.string().email(),
  fullName: z.string(),
  clientId: z.number().nullable(),
  role: z.string().refine((role) => Object.values(rolesEnum).includes(role as any), {
    message: 'Must be a valid role type',
  }),
};

const azureProvider = z
  .object({
    provider: z.literal('azure'),
    ...sharedFields,
  })
  .strict();

const usernameProvider = z
  .object({
    provider: z.literal('username'),
    username: z.string().max(255),
    password: z.string().max(255),
    ...sharedFields,
  })
  .strict();

export const createUserSchema = z.union([azureProvider, usernameProvider]);

export type CreateUserType = z.infer<typeof createUserSchema>;
