import { z } from 'zod';

import * as enums from '../../enums';

export const updateSettingsSchema = z
  .object({
    decimalPoint: z.nativeEnum(enums.decimalPoints),
    dateFormat: z.nativeEnum(enums.dateFormats),
    timezone: z.nativeEnum(enums.timezones),
    fullName: z.string().max(255),
    newPassword: z.string().max(32).optional(),
    oldPassword: z.string().max(32).optional(),
  })
  .strict();

export type UpdateSettingsType = z.infer<typeof updateSettingsSchema>;
