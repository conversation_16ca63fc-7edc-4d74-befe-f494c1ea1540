'use strict';
import joi from '../Joi';
import { TrancheAssetClassEnum } from '../../enums/cuftData';

export default joi.object({
  name: joi.string().required(),
  issueDate: joi.date().required().allow(null),
  numberOfMonthsBeforeIssueDate: joi.number().required(),
  tenor: joi.number().required().allow(null),
  numberOfYearsBeforeAndAfterTenor: joi.number().required(),
  creditRatings: joi.array().items(joi.string().rating()).required(),
  currencies: joi.array().items(joi.string().currency()).required(),
  countries: joi.array().items(joi.string()).required(),
  trancheAssetClasses: joi
    .array()
    .items(joi.string().valid(...Object.keys(TrancheAssetClassEnum)))
    .required(),
});
