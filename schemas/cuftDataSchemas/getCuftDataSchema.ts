'use strict';
import joi from '../Joi';
import { TrancheAssetClassEnum } from '../../enums/cuftData';

export default joi.object({
  limit: joi.number().max(100).required(),
  offset: joi.number().required(),
  issueDate: joi.date().required(),
  dateMonthsBeforeIssueDate: joi.date().required(),
  tenor: joi.number().required().allow(null),
  numberOfYearsBeforeAndAfterTenor: joi.number().required(),
  creditRatings: joi.array().items(joi.string().rating().required()).required(),
  currencies: joi.array().items(joi.string().currency()).required(),
  countries: joi.array().items(joi.string()).required(),
  trancheAssetClasses: joi
    .array()
    .items(joi.string().valid(...Object.keys(TrancheAssetClassEnum)))
    .required(),
  excludedCreditRatingIds: joi.array().items(joi.number()).required(),
  sortBy: joi.string().required().allow(null),
});
