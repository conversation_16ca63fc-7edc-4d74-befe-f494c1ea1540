import { z } from 'zod';

export const camt053Schema = z.object({
  Document: z.object({
    ['$']: z.object({ xmlns: z.string() }),
    BkToCstmrStmt: z.array(
      z.object({
        GrpHdr: z.array(z.object({ MsgId: z.tuple([z.string()]) })),
        Stmt: z.array(
          z.object({
            Id: z.tuple([z.string()]),
            Acct: z.array(z.object({ Id: z.tuple([z.object({ IBAN: z.tuple([z.string()]) })]) })),
            Bal: z.array(
              z.object({
                Tp: z.tuple([z.object({ CdOrPrtry: z.tuple([z.object({ Cd: z.tuple([z.string()]) })]) })]),
                Dt: z.tuple([z.object({ Dt: z.tuple([z.string()]) })]),
              }),
            ),
            Ntry: z.array(
              z.object({
                Amt: z.tuple([z.object({ _: z.string(), $: z.object({ Ccy: z.string() }) })]),
                ValDt: z.tuple([z.object({ Dt: z.tuple([z.string()]) })]),
                AcctSvcrRef: z.tuple([z.string()]),
                BkTxCd: z.array(
                  z.object({ Domn: z.tuple([z.object({ Fmly: z.tuple([z.object({ Cd: z.tuple([z.string()]) })]) })]) }),
                ),
                CdtDbtInd: z.array(z.enum(['CRDT', 'DBIT'])),
                AddtlNtryInf: z.tuple([z.string()]).optional(),
                NtryDtls: z
                  .array(
                    z
                      .object({
                        TxDtls: z.array(
                          z.object({
                            RmtInf: z
                              .union([
                                z.array(z.object({ Ustrd: z.array(z.string()).optional() })),
                                z.array(z.string()),
                                z.object({ Ustrd: z.array(z.string()).optional() }),
                                z.string(),
                              ])
                              .optional(),
                          }),
                        ),
                      })
                      .optional(),
                  )
                  .optional(),
              }),
            ),
          }),
        ),
      }),
    ),
  }),
});

export type Camt053Type = z.infer<typeof camt053Schema>;
export type Camt053EntryType = Camt053Type['Document']['BkToCstmrStmt'][0]['Stmt'][0]['Ntry'][0];
