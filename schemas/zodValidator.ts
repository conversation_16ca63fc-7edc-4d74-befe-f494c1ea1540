import { Request, Response, NextFunction } from 'express';
import { <PERSON><PERSON><PERSON>, ZodError } from 'zod';
import { ValidationError } from '../utils/ErrorHandler';

export default (schema: Schema) => async (req: Request, _: Response, next: NextFunction) => {
  try {
    req.body = await schema.parseAsync(req.body);
    next();
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      return next(new ValidationError(error.issues[0]?.message, error.issues));
    }

    return next(error);
  }
};
