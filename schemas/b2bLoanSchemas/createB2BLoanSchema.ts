'use strict';
import { companySchema } from '../companySchema';
import joi from '../Joi';

const sharedCapmObject = {
  requiredRateOfReturn: joi.number().allow(null),
  riskFreeRate: joi.number().allow(null),
  riskFreeRateSource: joi.string().allow(null),
  beta: joi.number().allow(null),
  betaSource: joi.string().allow(null),
  betaIndustrySector: joi.string().allow(null),
  betaRegion: joi.string().allow(null),
  betaType: joi.string().allow(null),
  equityRiskPremium: joi.number().allow(null),
  equityRiskPremiumSource: joi.string().allow(null),
  equityRiskPremiumCountry: joi.string().allow(null),
};

const campSchemaWithUnleveredAttributes = {
  ...sharedCapmObject,
  betaDebt: joi.number().allow(null),
  betaEquity: joi.number().allow(null),
  betaUnlevered: joi.number().allow(null),
};

const capmSchema = joi.object(sharedCapmObject).required();

const capmOverrideSchema = joi.object({
  ...sharedCapmObject,
  riskFreeRateIssueDate: joi.date().allow(null),
  riskFreeRateCurrency: joi.string().allow(null),
  riskFreeRateTenor: joi.number().allow(null),
  betaIssueDate: joi.date().allow(null),
  equityRiskPremiumIssueDate: joi.date().allow(null),
});

const expectedLossSchema = joi.object({
  expectedLoss: joi.number().allow(null),
  probabilityOfDefault: joi.number().allow(null),
  probabilityOfDefaultSource: joi.string().allow(null),
  probabilityOfDefaultType: joi.string().allow(null),
  lossGivenDefault: joi.number().allow(null),
  lossGivenDefaultSource: joi.string().allow(null),
});

export default joi.object({
  amount: joi.number().integer().min(0).required(),
  borrowers: joi.array().items(companySchema).min(2).required(),
  capm: campSchemaWithUnleveredAttributes,
  capmOverride: capmOverrideSchema,
  capmRecommendation: capmSchema,
  currency: joi.string().currency().required(),
  expectedLoss: expectedLossSchema,
  expectedLossOverride: expectedLossSchema,
  expectedLossRecommendation: expectedLossSchema,
  issueDate: joi.date().required(),
  lenders: joi.array().items(companySchema).min(2).required(),
  maturityDate: joi.date().required(),
  overrideToggles: joi
    .object({
      riskFreeRate: joi.boolean(),
      beta: joi.boolean(),
      equityRiskPremium: joi.boolean(),
      probabilityOfDefault: joi.boolean(),
      lossGivenDefault: joi.boolean(),
    })
    .required(),
  paymentFrequency: joi.string().paymentFrequency().required(),
  pricingApproach: joi.string().pricingApproach().required(),
  rateType: joi
    .object({
      type: joi.string().rateType().required(),
    })
    .required(),
  riskTakerId: joi.number().integer().min(1).required(),
  seniority: joi.string().seniority().required(),
  standardRemuneration: joi.object().pattern(
    joi.number().integer().required(),
    joi
      .object({
        name: joi.string().required(),
        id: joi.number().integer().required(),
        type: joi.string().valid('%', 'basis points', 'Operating cost & Markup').required(),
        margin: joi.number().when('type', {
          is: joi.string().valid('%', 'basis points'),
          then: joi.number().required(),
          otherwise: joi.forbidden(),
        }),
        operationalCost: joi.number().when('type', {
          is: joi.string().valid('Operating cost & Markup'),
          then: joi.number().required(),
          otherwise: joi.forbidden(),
        }),
        markup: joi.number().when('type', {
          is: joi.string().valid('Operating cost & Markup'),
          then: joi.number().required(),
          otherwise: joi.forbidden(),
        }),
      })
      .required(),
  ),
  type: joi.string().loanType().required(),
});
