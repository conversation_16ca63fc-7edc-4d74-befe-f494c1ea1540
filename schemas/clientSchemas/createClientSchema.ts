'use strict';
import joi from '../Joi';

export default joi.object({
  name: joi.string().required(),
  industry: joi.string().required(),
  emailDomains: joi.array().items(joi.string()).required(),
  companiesToCreate: joi
    .array()
    .items(
      joi.object({
        name: joi.string().max(255).required(),
        industry: joi.string().industry().required(),
        country: joi.string().country().required(),
        creditRating: joi
          .object({
            rating: joi.string().rating().required().allow(null),
            ratingAdj: joi.string().rating().required().allow(null),
            probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
            probabilityOfDefaultAdj: joi.number().probabilityOfDefault().required().allow(null),
          })
          .required(),
      }),
    )
    .allow(null)
    .optional(),
});
