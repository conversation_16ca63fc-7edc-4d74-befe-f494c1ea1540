'use strict';
import joi from './Joi';
import { overriddenStatusSchema } from './constants';

const companySchema = (isDraft = false) => {
  const presence = isDraft ? 'required' : 'optional';
  return joi.object({
    id: joi.number().integer().min(1).presence(presence),
    name: joi.string().max(255).presence(presence),
    country: joi.string().country().presence(presence),
    pseudonym: joi.string(),
  });
};

const attributesSchema = (isDraft = false) => {
  const presence = isDraft ? 'required' : 'optional';
  const allowNullIfDraft = (joiObject: any) => {
    return isDraft ? joiObject.allow(null) : joiObject;
  };

  return joi.object({
    naceSectorGroup: allowNullIfDraft(joi.string().presence(presence)),
    naceSector: allowNullIfDraft(joi.string().required(false)),
    option: joi.string().valid('Consolidated', 'Unconsolidated').required(),
    months: allowNullIfDraft(joi.number().required()),
    currency: joi.string().currency().required().allow(null),
    exchangeRate: joi.number().required().allow(null),
    fixedAssets: allowNullIfDraft(joi.number().required()),
    intangibleFixedAssets: joi.number().min(0).required().allow(null),
    tangibleFixedAssets: joi.number().min(0).required().allow(null),
    otherFixedAssets: joi.number().min(0).required().allow(null),
    currentAssets: allowNullIfDraft(joi.number().required()),
    stocks: joi.number().min(0).required().allow(null),
    debtors: joi.number().min(0).required().allow(null),
    otherCurrentAssets: joi.number().min(0).required().allow(null),
    cashAndCashEquivalent: joi.number().min(0).required().allow(null),
    totalAssets: allowNullIfDraft(joi.number().required()),
    shareholdersFunds: allowNullIfDraft(joi.number().required()),
    capital: joi.number().required().allow(null),
    otherShareholdersFunds: joi.number().required().allow(null),
    treasuryShares: joi.number().required().allow(null),
    nonCurrentLiabilities: allowNullIfDraft(joi.number().required()),
    longTermDebt: joi.number().min(0).required().allow(null),
    otherNonCurrentLiabilities: joi.number().min(0).required().allow(null),
    provisions: joi.number().min(0).required().allow(null),
    currentLiabilities: allowNullIfDraft(joi.number().required()),
    loans: joi.number().min(0).required().allow(null),
    creditors: joi.number().min(0).required().allow(null),
    otherCurrentLiabilities: joi.number().min(0).required().allow(null),
    totalShareFundsAndLiabilities: allowNullIfDraft(joi.number().required()),
    operatingRevenueTurnover: allowNullIfDraft(joi.number().min(0).required()),
    sales: joi.number().min(0).required().allow(null),
    costOfGoodSold: joi.number().min(0).required().allow(null),
    grossProfit: joi.number().required().allow(null),
    materialCosts: joi.number().min(0).required().allow(null),
    costOfEmployees: joi.number().min(0).required().allow(null),
    otherOperatingExpenses: joi.number().min(0).required().allow(null),
    depreciation: joi.number().min(0).required().allow(null),
    EBIT: allowNullIfDraft(joi.number().required()),
    EBITDA: allowNullIfDraft(joi.number().required()),
    financialPL: joi.number().required().allow(null),
    financialRevenue: joi.number().min(0).required().allow(null),
    financialExpenses: joi.number().min(0).required().allow(null),
    PLBeforeTax: allowNullIfDraft(joi.number().required()),
    taxation: joi.number().required().allow(null),
    PLAfterTax: joi.number().required().allow(null),
    extrAndOtherPL: joi.number().required().allow(null),
    extrAndOtherRevenue: joi.number().min(0).required().allow(null),
    extrAndOtherExpenses: joi.number().min(0).required().allow(null),
    PLForPeriod: allowNullIfDraft(joi.number().required()),
    overriddenStatus: overriddenStatusSchema.required(),
  });
};

export { companySchema, attributesSchema };
