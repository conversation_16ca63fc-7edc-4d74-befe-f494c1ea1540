'use strict';
const { BadRequestError } = require('../utils/ErrorHandler');

module.exports = function (schema) {
  return async function (req, res, next) {
    try {
      // Sanitized body
      req.body = await schema.validateAsync(req.body, {
        abortEarly: true,
      });
      next();
    } catch (error) {
      const errorMessage = {};
      error.details.forEach(({ message, path }) => {
        errorMessage[path[0]] = { message };
      });
      next(new BadRequestError(errorMessage));
    }
  };
};
