'use strict';
const adjustCreditRatingSchema = require('./adjustCreditRatingSchema');
const adjustProbabilityOfDefaultSchema = require('./adjustProbabilityOfDefaultSchema');
const createCompaniesSchema = require('./createCompaniesSchema');
const createCompanySchema = require('./createCompanySchema');
const implicitSupportSchema = require('./implicitSupportSchema');
const updateCompanySchema = require('./updateCompanySchema');

module.exports = {
  adjustCreditRatingSchema,
  adjustProbabilityOfDefaultSchema,
  createCompanySchema,
  createCompaniesSchema,
  implicitSupportSchema,
  updateCompanySchema,
};
