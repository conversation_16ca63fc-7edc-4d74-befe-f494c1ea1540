'use strict';
const joi = require('../Joi');

module.exports = joi.object({
  ringFencing: joi.boolean().required(),
  assessmentName: joi.string().assessmentName().required(),
  creditRating: joi
    .object({
      rating: joi.string().rating().required(),
      probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
    })
    .required(),
  parentCompanyId: joi.number().integer().required(),
});
