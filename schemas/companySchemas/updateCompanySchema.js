'use strict';
const { implicitSupportAnswersSchema } = require('../constants');
const joi = require('../Joi');

module.exports = joi.object({
  parentCompanyId: joi.number().integer().required().allow(null),
  name: joi.string().max(255).required(),
  industry: joi.string().industry().required(),
  country: joi.string().country().required(),
  note: joi.string().max(1500).allow('', null).required(),
  creditRating: joi
    .object({
      rating: joi.string().rating().required().allow(null),
      ratingAdj: joi.string().rating().required().allow(null),
      probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
      probabilityOfDefaultAdj: joi.number().probabilityOfDefault().required().allow(null),
    })
    .required(),
  assessment: joi
    .object({
      answers: implicitSupportAnswersSchema,
      name: joi.string().assessmentName().required(),
    })
    .required()
    .allow(null),
  isParent: joi.boolean().required(),
  createAuditTrail: joi.boolean().required(),
});
