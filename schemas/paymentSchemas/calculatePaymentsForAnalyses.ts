'use strict';
import joi from '../Joi';

const whtKeys = joi.object().keys({
  isWhtEnabled: joi.boolean(),
  whtInterestRate: joi.number().allow(null),
  approach: joi.string().whtApproach().allow(null),
  lowerBound: joi.number(),
  midPoint: joi.number(),
  upperBound: joi.number(),
  isConfirmed: joi.boolean(),
  floatingInterestRate: joi.number(),
});

export default joi.alternatives().try(
  // when report is fixed rate loan or guarantee
  whtKeys.keys({
    finalInterestRate: joi.number(),
    basisPoints: joi.number(),
  }),
  // when report is floating rate loan
  whtKeys.keys({
    finalInterestRate: joi.number(),
    basisPoints: joi.number(),
    estimationOfReferenceRate: joi.number(),
  }),
);
