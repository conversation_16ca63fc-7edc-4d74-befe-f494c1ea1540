'use strict';
import joi from '../Joi';

export = joi.object({
  featureAvailability: {
    payment: joi.boolean().required(),
    cashPool: joi.boolean().required(),
    creditRating: joi.boolean().required(),
    loan: joi.boolean().required(),
    backToBackLoan: joi.boolean().required(),
    guarantee: joi.boolean().required(),
    currency: joi.boolean().required(),
    geographyData: joi.boolean().required(),
    userNumber: joi.boolean().required(),
    loanNumber: joi.boolean().required(),
    backToBackLoanNumber: joi.boolean().required(),
    guaranteeNumber: joi.boolean().required(),
    loanGuaranteeNumber: joi.boolean().required(),
    creditRatingNumber: joi.boolean().required(),
    cashPoolNumber: joi.boolean().required(),
    financingAdvisory: joi.boolean().required(),
    cuftData: joi.boolean().required(),
    isTemplateCashPoolBatchUpload: joi.boolean().required(),
    physicalCashPool: joi.boolean().required(),
    notionalCashPool: joi.boolean().required(),
    nordicCashPool: joi.boolean().required(),
  },
  loanNumber: joi.number().required(),
  guaranteeNumber: joi.number().required(),
  backToBackLoanNumber: joi.number().required(),
  loanGuaranteeNumber: joi.number().required(),
  userNumber: joi.number().required(),
  cashPoolNumber: joi.number().required(),
  creditRatingNumber: joi.number().required(),
  geographyData: joi.string().required(),
  currency: joi.object({ currencies: joi.array().items(joi.string().required()).required() }),
});
