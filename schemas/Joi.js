const joi = require('joi');

const { notificationActions, rolesEnum, reportEnums } = require('../enums');
const { whtEnums, dayCountEnums } = require('../enums');
const { cashPoolOvernightRates } = require('../utils/cashPool');
const { assessmentConstants } = require('../utils/companyUtils');
const { creditRatingConstants, currencies: creditRatingCurrencies } = require('../utils/creditRatingUtils');
const { countryToISOMapping } = require('../utils/creditRatingUtils');
const {
  currencyToReferenceRateMapper,
  paymentFrequencyToReferenceRateMaturityMapper,
  usdGbpChfJpyMapper,
} = require('../utils/loanUtils');
const { industryAbbreviations, seniorityAbbreviations } = require('../utils/providerDataUtils');

const assessmentNames = Object.values(assessmentConstants);
const industries = Object.keys(industryAbbreviations);
const countries = Object.keys(countryToISOMapping);
const reportCurrencies = Object.keys(currencyToReferenceRateMapper);
const paymentFrequencies = ['Monthly', 'Quarterly', 'Semi-annual', 'Annual'];
const seniorities = Object.keys(seniorityAbbreviations);
const pricingApproaches = ['stand-alone', 'implicit', 'stand-alone non-standard', 'implicit non-standard'];
const referenceRates = Object.values(currencyToReferenceRateMapper);
const usdGbpChfJpyNewReferenceRates = Object.values(usdGbpChfJpyMapper);
const referenceRateMaturities = Object.values(paymentFrequencyToReferenceRateMaturityMapper);
const roles = Object.values(rolesEnum);
const notificationActionsValues = Object.values(notificationActions.NOTIFICATION_ACTIONS);
const cashPoolOvernightRatesValues = Object.values(cashPoolOvernightRates);
const pricingMethodologyValues = Object.values(reportEnums.pricingMethodologyEnum);
const dayCountValues = Object.values(dayCountEnums.dayCountMapper);

const currenciesSet = new Set([...reportCurrencies, ...creditRatingCurrencies]);
const currencies = [...currenciesSet];
const referenceRatesCombined = [...referenceRates, ...usdGbpChfJpyNewReferenceRates];
const whtApproaches = [...Object.values(whtEnums.WHT_APPROACHES)];

function validateValue(value, helpers, validValues, error) {
  if (validValues.includes(value)) {
    return value;
  } else {
    return helpers.error(error);
  }
}

const StringExtension = function (joi) {
  return {
    type: 'string',
    base: joi.string(),
    messages: {
      rating: '{{#label}} must be a valid credit rating.',
      industry: '{{#label}} must be a valid industry.',
      country: '{{#label}} must be a valid country.',
      assessmentName: '{{#label}} must be a valid assessment name.',
      currency: '{{#label}} must be a valid currency.',
      paymentFrequency: '{{#label}} must be a valid payment frequency.',
      seniority: '{{#label}} must be a valid seniority.',
      pricingApproach: '{{#label}} must be a valid pricing approach.',
      status: '{{#label}} must be a valid status.',
      rateType: '{{#label}} must be a valid rate type.',
      functionalAnalysisAssessment: '{{#label}} must be a valid assessment.',
      cashPoolOvernightRate: '{{#label}} must be a valid overnight rate.',
      referenceRate: '{{#label}} must be a valid reference rate.',
      referenceRateMaturity: '{{#label}} must be a valid reference rate maturity.',
      loanType: '{{#label}} must be a valid loan type.',
      dayCount: 'Day count must be a valid value.',
      role: '{{#label}} must be a valid role type.',
      notificationAction: '{{#label}} must be a valid notification action.',
    },
    rules: {
      rating: {
        validate(value, helpers) {
          return validateValue(value, helpers, creditRatingConstants, 'rating');
        },
      },
      industry: {
        validate(value, helpers) {
          return validateValue(value, helpers, industries, 'industry');
        },
      },
      country: {
        validate(value, helpers) {
          return validateValue(value, helpers, countries, 'country');
        },
      },
      assessmentName: {
        validate(value, helpers) {
          return validateValue(value, helpers, assessmentNames, 'assessmentName');
        },
      },
      currency: {
        validate(value, helpers) {
          return validateValue(value, helpers, currencies, 'currency');
        },
      },
      paymentFrequency: {
        validate(value, helpers) {
          return validateValue(value, helpers, paymentFrequencies, 'paymentFrequency');
        },
      },
      seniority: {
        validate(value, helpers) {
          return validateValue(value, helpers, seniorities, 'seniority');
        },
      },
      pricingApproach: {
        validate(value, helpers) {
          return validateValue(value, helpers, pricingApproaches, 'pricingApproach');
        },
      },
      pricingMethodology: {
        validate(value, helpers) {
          return validateValue(value, helpers, pricingMethodologyValues, 'pricingMethodology');
        },
      },
      dayCount: {
        validate(value, helpers) {
          return validateValue(value, helpers, dayCountValues, 'dayCount');
        },
      },
      status: {
        validate(value, helpers) {
          return validateValue(value, helpers, ['Draft', 'Final'], 'status');
        },
      },
      rateType: {
        validate(value, helpers) {
          return validateValue(value, helpers, ['fixed', 'float'], 'rateType');
        },
      },
      functionalAnalysisAssessment: {
        validate(value, helpers) {
          return validateValue(value, helpers, ['Low', 'Medium', 'High'], 'functionalAnalysisAssessment');
        },
      },
      cashPoolOvernightRate: {
        validate(value, helpers) {
          return validateValue(value, helpers, cashPoolOvernightRatesValues, 'cashPoolOvernightRate');
        },
      },
      referenceRate: {
        validate(value, helpers) {
          return validateValue(value, helpers, referenceRatesCombined, 'referenceRate');
        },
      },
      referenceRateMaturity: {
        validate(value, helpers) {
          return validateValue(value, helpers, referenceRateMaturities, 'referenceRateMaturity');
        },
      },
      loanType: {
        validate(value, helpers) {
          return validateValue(value, helpers, ['Bullet', 'Balloon'], 'loanType');
        },
      },
      role: {
        validate(value, helpers) {
          return validateValue(value, helpers, roles, 'role');
        },
      },
      notificationAction: {
        validate(value, helpers) {
          return validateValue(value, helpers, notificationActionsValues, 'notificationAction');
        },
      },
      whtApproach: {
        validate(value, helpers) {
          return validateValue(value, helpers, whtApproaches, 'whtApproach');
        },
      },
    },
  };
};

const NumberExtension = function (joi) {
  return {
    type: 'number',
    base: joi.number(),
    messages: {
      probabilityOfDefault: 'Probability of default needs to be a number in a range [0, 100].',
    },
    rules: {
      probabilityOfDefault: {
        validate(value, helpers) {
          if (value >= 0 && value <= 100) {
            return value;
          } else {
            return helpers.error('probabilityOfDefault');
          }
        },
      },
    },
  };
};

module.exports = joi.extend(StringExtension, NumberExtension);
