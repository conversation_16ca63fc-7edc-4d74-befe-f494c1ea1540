const joi = require('./Joi');

const companyInfoSchema = {
  id: joi.number().integer().min(1).required(),
  name: joi.string().max(255).required(),
  industry: joi.string().industry().required(),
  country: joi.string().country().required(),
  parentCompanyId: joi.number().integer().required().allow(null),
};

const implicitSupportAnswersSchema = joi.object({
  ringFencing: joi.boolean().required(),
  question1: joi.boolean().required(),
  question2: joi.boolean().required(),
  question3: joi.boolean().required(),
  question4: joi.boolean().required(),
  question5: joi.boolean().required(),
  question6: joi.boolean().required(),
  question7: joi.boolean().required(),
  question8: joi.boolean().required(),
  question9: joi.boolean().required(),
  question10: joi.boolean().required(),
});

const creditRatingSchema = joi.object({
  rating: joi.string().rating().required().allow(null),
  ratingAdj: joi.string().rating().required().allow(null),
  probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
  probabilityOfDefaultAdj: joi.number().probabilityOfDefault().required().allow(null),
  newRating: joi.string().rating().allow(null),
});

const overriddenStatusSchema = joi.object({
  fixedAssets: joi.boolean().required(),
  currentAssets: joi.boolean().required(),
  totalAssets: joi.boolean().required(),
  shareholdersFunds: joi.boolean().required(),
  capital: joi.boolean().required(),
  otherShareholdersFunds: joi.boolean().required(),
  treasuryShares: joi.boolean().required(),
  nonCurrentLiabilities: joi.boolean().required(),
  currentLiabilities: joi.boolean().required(),
  totalShareFundsAndLiabilities: joi.boolean().required(),
  EBITDA: joi.boolean().required(),
  EBIT: joi.boolean().required(),
  financialPL: joi.boolean().required(),
  PLBeforeTax: joi.boolean().required(),
  PLAfterTax: joi.boolean().required(),
  extrAndOtherPL: joi.boolean().required(),
  PLForPeriod: joi.boolean().required(),
  grossProfit: joi.boolean().required(),
});

module.exports = {
  companyInfoSchema,
  implicitSupportAnswersSchema,
  creditRatingSchema,
  overriddenStatusSchema,
};
