'use strict';
const { companySchema } = require('../companySchema');
const joi = require('../Joi');

module.exports = joi.object({
  guarantor: companySchema,
  principal: companySchema,
  issueDate: joi.date().required(),
  terminationDate: joi.date().required(),
  currency: joi.string().currency().required(),
  amount: joi.number().integer().min(0).required(),
  paymentFrequency: joi.string().paymentFrequency().required(),
  seniority: joi.string().seniority().required().allow(null),
  pricingApproach: joi.string().pricingApproach().required(),
  pricingMethodology: joi.string().pricingMethodology().required(),
});
