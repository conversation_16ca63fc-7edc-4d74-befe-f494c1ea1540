const guaranteeSchema = require('./guaranteeSchema');
const guaranteesSchema = require('./guaranteesSchema');
const importedGuaranteeSchema = require('./importedGuaranteeSchema');
const importedGuaranteesSchema = require('./importedGuaranteesSchema');
const updateGuaranteeIsPortfolioSchema = require('./updateGuaranteeIsPortfolioSchema');
const updateGuaranteeNoteSchema = require('./updateGuaranteeNoteSchema');
const updateGuaranteeRates = require('./updateGuaranteeRates');
const updateGuaranteeStatusSchema = require('./updateGuaranteeStatusSchema');
const updateGuaranteeWithUpdatedDataPoints = require('./updateGuaranteeWithUpdatedDataPoints');

module.exports = {
  guaranteeSchema,
  guaranteesSchema,
  importedGuaranteeSchema,
  importedGuaranteesSchema,
  updateGuaranteeIsPortfolioSchema,
  updateGuaranteeNoteSchema,
  updateGuaranteeStatusSchema,
  updateGuaranteeWithUpdatedDataPoints,
  updateGuaranteeRates,
};
