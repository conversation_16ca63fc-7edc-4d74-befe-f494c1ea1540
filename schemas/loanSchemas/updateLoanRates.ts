'use strict';
import joi from '../Joi';

const baseKeys = joi.object().keys({
  isWhtEnabled: joi.boolean().required(),
  whtInterestRate: joi.number().allow(null),
  approach: joi.string().whtApproach().allow(null),
  finalInterestRate: joi.number().required(),
});

export = joi.alternatives().try(
  // when loan has fixed rate
  baseKeys,
  // when loan has floating rate
  baseKeys.keys({
    estimationOfReferenceRate: joi.number().allow(null),
  }),
);
