'use strict';
const { companyInfoSchema, creditRatingSchema } = require('../constants');
const joi = require('../Joi');

module.exports = joi.object({
  lender: joi
    .object({
      ...{
        ...companyInfoSchema,
        id: companyInfoSchema.id.allow(null),
        country: joi.string().required(),
        industry: companyInfoSchema.industry.allow(null),
      },
      creditRating: creditRatingSchema.required().allow(null),
    })
    .required(),
  borrower: joi
    .object({
      ...companyInfoSchema,
      creditRating: creditRatingSchema.required(),
    })
    .required(),
  issueDate: joi.date().required(),
  maturityDate: joi.date().required(),
  currency: joi.string().currency().required(),
  amount: joi.number().integer().min(0).required(),
  paymentFrequency: joi.string().paymentFrequency().required(),
  seniority: joi.string().seniority().required(),
  pricingApproach: joi.string().required().allow(null),
  rateType: joi
    .object({
      type: joi.string().rateType().required(),
      referenceRate: joi.string().referenceRate().allow(null),
      referenceRateMaturity: joi.string().referenceRateMaturity().allow(null),
    })
    .required(),
  type: joi.string().loanType().required(),
  report: joi
    .object({
      finalInterestRate: joi.number().required(),
    })
    .required(),
  note: joi.string().max(1500).required().allow('', null),
  isThirdParty: joi.boolean().required(),
});
