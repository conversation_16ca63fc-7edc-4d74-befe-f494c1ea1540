'use strict';
const joi = require('../Joi');

module.exports = joi.object({
  pricingApproach: joi.string().required(),
  issueDate: joi.date().required(),
  maturityDate: joi.date().required(),
  report: joi
    .object({
      lowerBound: joi.number().required(),
      upperBound: joi.number().required(),
      midPoint: joi.number().required(),
      finalInterestRate: joi.number().allow(null),
      isWhtEnabled: joi.boolean(),
      whtInterestRate: joi.number().allow(null),
      estimationOfReferenceRate: joi.number().allow(null),
      isConfirmed: joi.boolean(),
    })
    .required(),
});
