'use strict';
const { companySchema } = require('../companySchema');
const joi = require('../Joi');

module.exports = joi.object({
  lender: companySchema,
  borrower: companySchema,
  issueDate: joi.date().required(),
  maturityDate: joi.date().required(),
  currency: joi.string().currency().required(),
  amount: joi.number().integer().min(0).required(),
  paymentFrequency: joi.string().paymentFrequency().required(),
  seniority: joi.string().seniority().required(),
  rateType: joi.object({ type: joi.string().rateType().required() }).required(),
  type: joi.string().loanType().required(),
  pricingApproach: joi.string().pricingApproach().required(),
  dayCount: joi.string().dayCount().required(),
});
