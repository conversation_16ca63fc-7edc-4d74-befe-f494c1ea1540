const importedLoanSchema = require('./importedLoanSchema');
const importedLoansSchema = require('./importedLoansSchema');
const loanSchema = require('./loanSchema');
const loansSchema = require('./loansSchema');
const updateLoanIsPortfolioSchema = require('./updateLoanIsPortfolioSchema');
const updateLoanNoteSchema = require('./updateLoanNoteSchema');
const updateLoanRates = require('./updateLoanRates');
const updateLoanStatusSchema = require('./updateLoanStatusSchema');
const updateLoanWithUpdatedDataPoints = require('./updateLoanWithUpdatedDataPoints');

module.exports = {
  loanSchema,
  loansSchema,
  importedLoanSchema,
  importedLoansSchema,
  updateLoanIsPortfolioSchema,
  updateLoanNoteSchema,
  updateLoanStatusSchema,
  updateLoanWithUpdatedDataPoints,
  updateLoanRates,
};
