'use strict';
const joi = require('../Joi');

module.exports = joi.object({
  company: joi
    .object({
      id: joi.number().integer().min(1).required(),
      name: joi.string().max(255).required(),
      country: joi.string().country().required(),
    })
    .required(),
  closingDate: joi.date().required(),
  creditRating: joi
    .object({
      rating: joi.string().rating().required(),
    })
    .required(),
  probabilityOfDefault: joi.number().probabilityOfDefault().required(),
  attributes: joi.any().valid(null).required(),
  note: joi.string().max(1500).required().allow('', null),
});
