const creditRatingSchema = require('./creditRatingSchema');
const creditRatingsSchema = require('./creditRatingsSchema');
const importedCreditRatingSchema = require('./importedCreditRatingSchema');
const importedCreditRatingsSchema = require('./importedCreditRatingsSchema');
const updateCreditRatingIsPortfolioSchema = require('./updateCreditRatingIsPortfolioSchema');
const updateCreditRatingNoteSchema = require('./updateCreditRatingNoteSchema');
const updateCreditRatingStatusSchema = require('./updateCreditRatingStatusSchema');
const creditRatingDraftSchema = require('./creditRatingDraftSchema');

module.exports = {
  creditRatingSchema,
  creditRatingsSchema,
  importedCreditRatingSchema,
  importedCreditRatingsSchema,
  updateCreditRatingNoteSchema,
  updateCreditRatingIsPortfolioSchema,
  updateCreditRatingStatusSchema,
  creditRatingDraftSchema,
};
