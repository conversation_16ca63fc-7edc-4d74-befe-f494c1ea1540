import request from 'supertest';

import app from '../../app';
import models from '../../models';
import { cashPoolStatementDataRepository } from '../../repositories';
import { authSetup, featureSetup, rateLimiterSetup, transactionSetup } from '../setup';
import redisService from '../../services/redis';

jest.mock('../../repositories/featureRepository');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../models');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
  featureSetup.enabledFeatureSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Cash Pool Controller', () => {
  test('It should mass delete statement data successfully', async () => {
    transactionSetup.mockTransaction(models);

    const clientId = 1;

    const mockGetStatementDataById = jest.spyOn(cashPoolStatementDataRepository, 'getStatementDataById');
    mockGetStatementDataById.mockImplementation((id, passedClientId) => {
      expect(passedClientId).toBe(clientId);
      return Promise.resolve({
        id,
        cashPoolId: 1,
        cashPoolAccountId: 1,
        transactionReferenceNumber: '1',
        statementNumber: '1',
        balanceChange: 1,
        date: new Date(),
        statementDate: new Date(),
        comment: 'comment',
        cashPoolBatchId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    const mockDeleteStatementById = jest.spyOn(cashPoolStatementDataRepository, 'deleteStatementById');
    mockDeleteStatementById.mockResolvedValue(1);

    const response = await request(app)
      .delete('/api/cash-pool/:id/statement-data')
      .send({ ids: [1, 2, 3] });

    expect(response.statusCode).toBe(204);
    expect(mockGetStatementDataById).toHaveBeenCalledTimes(3);
    expect(mockDeleteStatementById).toHaveBeenCalledTimes(3);
    expect(mockDeleteStatementById).toHaveBeenCalledWith(3);
  });

  test('It should fail to mass delete statement data when one of the statements is not found', async () => {
    transactionSetup.mockTransaction(models);

    const clientId = 1;

    const mockGetStatementDataById = jest.spyOn(cashPoolStatementDataRepository, 'getStatementDataById');
    mockGetStatementDataById.mockImplementation((id, passedClientId) => {
      expect(passedClientId).toBe(clientId);
      if (id === 2) return Promise.resolve(null as any);
      return Promise.resolve({
        id,
        cashPoolId: 1,
        cashPoolAccountId: 1,
        transactionReferenceNumber: '1',
        statementNumber: '1',
        balanceChange: 1,
        date: new Date(),
        statementDate: new Date(),
        comment: 'comment',
        cashPoolBatchId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    const response = await request(app)
      .delete('/api/cash-pool/:id/statement-data')
      .send({ ids: [1, 2, 3] });

    expect(response.statusCode).toBe(404);
  });
});
