import jwt, { JwtPayload, SignOptions, VerifyOptions } from 'jsonwebtoken';

export class TokenGenerator {
  constructor(private tokenSecret: string, private options?: SignOptions) {}

  sign(payload: any, signOptions?: SignOptions) {
    const jwtSignOptions = Object.assign({}, signOptions, this.options);
    return jwt.sign(payload, this.tokenSecret, jwtSignOptions);
  }

  refresh(token: string, signOptions?: SignOptions, refreshOptions?: VerifyOptions) {
    const payload = jwt.verify(token, this.tokenSecret, refreshOptions) as JwtPayload;
    delete payload.iat;
    delete payload.exp;
    delete payload.nbf;
    delete payload.jti;
    const jwtSignOptions = Object.assign({}, signOptions, this.options);
    return jwt.sign(payload, this.tokenSecret, jwtSignOptions);
  }
}
