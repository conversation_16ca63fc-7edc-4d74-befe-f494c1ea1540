import { tokenRepository } from '../repositories';
import { UserDataAccessTokenType, UserWithClientType } from '../types';
import { TokenGenerator } from './tokenGenerator';

const tokenGenerator = new TokenGenerator(process.env.TOKEN_SECRET!);

const getAccessTokenData = (user: UserWithClientType): UserDataAccessTokenType => ({
  id: user.id,
  clientId: user.clientId,
  clientName: user.client.name,
  role: user.role,
  username: user.username,
});

const generateAccessToken = async (userData: UserDataAccessTokenType) => {
  const accessToken = tokenGenerator.sign(userData, { expiresIn: '15 minute' });

  return accessToken;
};

const generateRefreshToken = async (userData: UserDataAccessTokenType) => {
  const token = tokenGenerator.sign(userData, { expiresIn: '5 day' });

  await tokenRepository.deleteToken({ userId: userData.id });
  await tokenRepository.createToken({ accessToken: token, userId: userData.id });

  return token;
};

const refreshAccessToken = async (token: string) => {
  const accessToken = tokenGenerator.refresh(token, { expiresIn: '5 day' });

  await tokenRepository.updateToken({ accessToken: token }, { accessToken });

  return accessToken;
};

export { getAccessTokenData, generateAccessToken, generateRefreshToken, refreshAccessToken };
