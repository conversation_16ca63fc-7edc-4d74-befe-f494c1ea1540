import url from 'url';
import axios from 'axios';
import { decode } from 'jsonwebtoken';

import { InternalServerError } from '../utils/ErrorHandler';
import { extractClientFromRequest } from '../utils/clientUtils';
import { Client } from '../enums';

function getRedirectUri(hostname: string): string {
  const baseRedirectUri = process.env.AZURE_REDIRECT_URI!;
  const parts = hostname.split('.');

  const customSubdomain = parts.length > 3 ? parts[0] : null;

  if (!customSubdomain) {
    return baseRedirectUri;
  }

  const redirect = new URL(baseRedirectUri);
  redirect.hostname = `${customSubdomain}.${redirect.hostname}`;
  return redirect.toString();
}

/**
 * Gets Azure access token by using the `code` provided to the FE in the query string.
 * Access token (JWT) is decoded and email is returned to the the controller to check
 * the user with that email is in the database.
 */
const azureAuth = async (code: string, requestUrl?: string) => {
  const customClient = extractClientFromRequest(requestUrl);
  let redirectUri = process.env.AZURE_REDIRECT_URI!;

  if (requestUrl) {
    try {
      const parsedUrl = new URL(requestUrl);
      redirectUri = getRedirectUri(parsedUrl.hostname);
    } catch (err) {
      console.log('Invalid requestUrl passed to azureAuth:', requestUrl);
    }
  }

  const clientId = customClient === Client.MARS ? process.env.AZURE_MARS_CLIENT_ID! : process.env.AZURE_CLIENT_ID!;

  const clientSecret =
    customClient === Client.MARS ? process.env.AZURE_MARS_CLIENT_SECRET! : process.env.AZURE_CLIENT_SECRET!;

  const scope = customClient === Client.MARS ? process.env.AZURE_MARS_API_SCOPE! : process.env.AZURE_API_SCOPE!;

  const data = {
    client_id: clientId,
    client_secret: clientSecret,
    redirect_uri: redirectUri,
    scope,
    grant_type: 'authorization_code',
    code,
  };
  const params = new url.URLSearchParams(data);
  const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };

  try {
    const response = await axios.post(process.env.AZURE_AUTH_ENDPOINT!, params, { headers });

    const decodedToken = decode(response.data.access_token);
    if (!decodedToken || typeof decodedToken === 'string') {
      console.log({ msg: "!decodedToken || typeof decodedToken === 'string'", date: new Date() });
      throw new InternalServerError('Token decode failed.');
    }

    return (
      decodedToken?.email?.toLowerCase() ??
      decodedToken?.preferred_username?.toLowerCase() ??
      decodedToken?.unique_name?.toLowerCase() ??
      decodedToken?.upn?.toLowerCase()
    );
  } catch (err: any) {
    console.log({ azureAuthError: err, errorData: err?.response?.data, date: new Date() });
  }
};

export default azureAuth;
