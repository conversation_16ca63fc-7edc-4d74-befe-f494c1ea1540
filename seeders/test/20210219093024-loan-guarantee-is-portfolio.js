'use strict';
const { Op } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.sequelize.query(
          'UPDATE "Loans" SET "isPortfolio" = true WHERE report->>\'finalInterestRate\' IS NOT NULL',
          { transaction },
        ),
        queryInterface.sequelize.query(
          'UPDATE "Guarantees" SET "isPortfolio" = true WHERE report->>\'finalInterestRate\' IS NOT NULL',
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.sequelize.query(
          'UPDATE "Loans" SET "isPortfolio" = false WHERE report->>\'finalInterestRate\' IS NOT NULL',
          { transaction },
        ),
        queryInterface.sequelize.query(
          'UPDATE "Guarantees" SET "isPortfolio" = false WHERE report->>\'finalInterestRate\' IS NOT NULL',
          { transaction },
        ),
      ]);
    });
  },
};
