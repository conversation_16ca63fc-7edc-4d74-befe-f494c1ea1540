'use strict';
const { estimateProbabilityOfDefaultAdj } = require('../../utils/companyUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const companyQueryResponse = await queryInterface.sequelize.query('SELECT * from "Companies";', { transaction });
      const companyQueryRes = companyQueryResponse[0];
      for (let i = 0, len = companyQueryRes.length; i < len; i++) {
        const { id, creditRating } = companyQueryRes[i];
        let probabilityOfDefaultAdj;
        if (creditRating?.rating && creditRating?.ratingAdj && creditRating?.probabilityOfDefault != null) {
          probabilityOfDefaultAdj = estimateProbabilityOfDefaultAdj(
            creditRating?.probabilityOfDefault,
            creditRating?.rating,
            creditRating?.ratingAdj,
          );
        } else {
          probabilityOfDefaultAdj = null;
        }
        await queryInterface.bulkUpdate(
          'Companies',
          { creditRating: { ...creditRating, probabilityOfDefaultAdj } },
          { id },
          { transaction },
        );
      }

      const loanQueryResponse = await queryInterface.sequelize.query('SELECT * from "Loans";', { transaction });
      const loanQueryRes = loanQueryResponse[0];
      for (let i = 0, len = loanQueryRes.length; i < len; i++) {
        const loan = loanQueryRes[i];
        await queryInterface.bulkUpdate(
          'Loans',
          {
            lender: {
              ...loan.lender,
              creditRating: {
                ...loan.lender.creditRating,
                probabilityOfDefaultAdj: null,
              },
            },
            borrower: {
              ...loan.borrower,
              creditRating: {
                ...loan.borrower.creditRating,
                probabilityOfDefaultAdj: null,
              },
            },
          },
          { id: loan.id },
          { transaction },
        );
      }

      await Promise.all([
        queryInterface.sequelize.query(
          'UPDATE "CompanyAuditTrails" SET "creditRating"="creditRating"::jsonb || \'{"probabilityOfDefault": null}\' WHERE "creditRating"::json->>\'probabilityOfDefault\' IS NULL',
          { transaction },
        ),
        queryInterface.sequelize.query(
          'UPDATE "CompanyAuditTrails" SET "creditRating"="creditRating"::jsonb || \'{"probabilityOfDefaultAdj": null}\' WHERE "creditRating"::json->>\'probabilityOfDefaultAdj\' IS NULL',
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
