'use strict';
const { Op, literal } = require('sequelize');

const companyRepository = require('../../repositories/companyRepository');
const loanUtils = require('../../utils/loanUtils/loanUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const loansQueryResponse = await queryInterface.sequelize.query(
        'SELECT * from "Loans", "LoanFiles" WHERE "Loans".id = "LoanFiles"."loanId" AND "Loans"."editable"=true;',
        { transaction },
      );

      const loansQueryRes = loansQueryResponse[0];
      for (let i = 0, len = loansQueryRes.length; i < len; i++) {
        const res = loansQueryRes[i];
        let templateData;
        if (res.pricingApproach.includes('implicit')) {
          const [lenderAssessment, borrowerAssessment, parentCompanyRating] = await Promise.all([
            companyRepository.getCompany({ id: res.lender.id }, ['assessment']),
            companyRepository.getCompany({ id: res.borrower.id }, ['assessment']),
            companyRepository.getCompany({ id: { [Op.eq]: literal('"parentCompanyId"') }, clientId: res.clientId }, [
              'creditRating',
            ]),
          ]);
          templateData = loanUtils.createTemplateDataImplicit(
            res,
            lenderAssessment.assessment,
            borrowerAssessment.assessment,
            parentCompanyRating.creditRating,
          );
        } else {
          templateData = loanUtils.createTemplateData(res);
        }

        const [TPReportFile, agreementFile] = await loanUtils.generateReportFiles(
          res,
          res.id,
          templateData,
          res.clientId,
        );

        await Promise.all([
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              file: TPReportFile.file,
            },
            {
              loanId: res.loanId,
              label: 'TP Report',
              isGenerated: true,
            },
            { transaction },
          ),
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              file: agreementFile.file,
            },
            {
              loanId: res.loanId,
              label: 'Agreement',
              isGenerated: true,
            },
            { transaction },
          ),
        ]);
      }
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
