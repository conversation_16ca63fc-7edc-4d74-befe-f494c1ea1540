'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.sequelize.query(
          `UPDATE "Guarantees" SET report = 
        jsonb_set(jsonb_set(jsonb_set(report::jsonb, '{lowerBound}', concat('"', split_part(report->>'lowerBound', ' ', '1'), '"')::jsonb, false)::jsonb, 
        '{midPoint}', concat('"', split_part(report->>'midPoint', ' ', '1'), '"')::jsonb, false), 
        '{upperBound}', concat('"', split_part(report->>'upperBound', ' ', '1'), '"')::jsonb, false)`,
          { transaction },
        ),
        queryInterface.sequelize.query(
          `UPDATE "Loans" SET report = 
        jsonb_set(jsonb_set(jsonb_set(report::jsonb, '{lowerBound}', concat('"', split_part(report->>'lowerBound', ' ', '1'), '"')::jsonb, false)::jsonb, 
        '{midPoint}', concat('"', split_part(report->>'midPoint', ' ', '1'), '"')::jsonb, false), 
        '{upperBound}', concat('"', split_part(report->>'upperBound', ' ', '1'), '"')::jsonb, false)`,
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
