'use strict';
const bcrypt = require('bcrypt');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      return Promise.all([
        queryInterface.sequelize.query(
          `UPDATE "Users" SET 
            password = '${await bcrypt.hash(process.env.SEED_DEMO_PASSWORD, 10)}' WHERE username = 'nord-staging'`,
          { transaction },
        ),
        queryInterface.bulkDelete('Tokens', {}, { transaction }),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
