'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction((transaction) => {
      return Promise.all([
        queryInterface.bulkUpdate(
          'Loans',
          {
            pricingApproach: 'stand-alone',
          },
          {
            pricingApproach: null,
          },
          { transaction },
        ),
        queryInterface.bulkUpdate(
          'Guarantees',
          {
            pricingApproach: 'implicit',
          },
          {
            pricingApproach: null,
          },
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction((transaction) => {
      return Promise.all([
        queryInterface.bulkUpdate(
          'Loans',
          {
            pricingApproach: null,
          },
          {},
          { transaction },
        ),
        queryInterface.bulkUpdate(
          'Guarantees',
          {
            pricingApproach: null,
          },
          {},
          { transaction },
        ),
      ]);
    });
  },
};
