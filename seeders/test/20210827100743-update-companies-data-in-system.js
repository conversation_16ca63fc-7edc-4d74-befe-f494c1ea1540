'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        'UPDATE "Companies" SET "creditRating"="creditRating"::jsonb || \'{"probabilityOfDefault": null}\' WHERE "creditRating"::json->>\'probabilityOfDefault\' IS NULL',
        { transaction },
      );

      const loanQueryResponse = await queryInterface.sequelize.query('SELECT * from "Loans";', { transaction });
      const loanQueryRes = loanQueryResponse[0];
      for (let i = 0, len = loanQueryRes.length; i < len; i++) {
        const loan = loanQueryRes[i];
        await queryInterface.bulkUpdate(
          'Loans',
          {
            lender: {
              ...loan.lender,
              creditRating: {
                ...loan.lender.creditRating,
                ratingAdj: loan.lender.creditRating.ratingAdj ?? null,
                probabilityOfDefault: null,
              },
            },
            borrower: {
              ...loan.borrower,
              creditRating: {
                ...loan.borrower.creditRating,
                ratingAdj: loan.borrower.creditRating.ratingAdj ?? null,
                probabilityOfDefault: null,
              },
            },
          },
          { id: loan.id },
          { transaction },
        );
      }

      const guaranteeQueryResponse = await queryInterface.sequelize.query('SELECT * from "Guarantees";', {
        transaction,
      });
      const guaranteeQueryRes = guaranteeQueryResponse[0];
      for (let i = 0, len = guaranteeQueryRes.length; i < len; i++) {
        const guarantee = guaranteeQueryRes[i];
        await queryInterface.bulkUpdate(
          'Guarantees',
          {
            guarantor: {
              ...guarantee.guarantor,
              creditRating: {
                ...guarantee.guarantor.creditRating,
                ratingAdj: guarantee.guarantor.creditRating.ratingAdj ?? null,
                probabilityOfDefault: null,
              },
            },
            principal: {
              ...guarantee.principal,
              creditRating: {
                ...guarantee.principal.creditRating,
                ratingAdj: guarantee.principal.creditRating.ratingAdj ?? null,
                probabilityOfDefault: null,
              },
            },
          },
          { id: guarantee.id },
          { transaction },
        );
      }
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
