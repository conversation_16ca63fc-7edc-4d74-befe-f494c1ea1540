'use strict';
const { groupByThreeCharacters } = require('../../utils/strings');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query(
        'SELECT "Loans"."borrower" AS "borrower", ' +
          '"Loans"."amount" AS "amount", ' +
          '"Loans"."currency" AS "currency", ' +
          '"LoanFiles"."label" as "label",' +
          '"LoanFiles"."id" AS "id" FROM "Loans" INNER JOIN "LoanFiles" ' +
          'ON "Loans".id="LoanFiles"."loanId"' +
          'WHERE "LoanFiles"."isGenerated"=true;',
        { transaction },
      );

      const queryRes = queryResponse[0];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const res = queryRes[i];

        await queryInterface.bulkUpdate(
          'LoanFiles',
          {
            name: `${res.borrower.name} ${res.currency} ${groupByThreeCharacters(res.amount)} ${
              res.label === 'TP Report' ? 'Loan Report' : 'Loan Agreement'
            }`,
          },
          {
            id: res.id,
          },
          { transaction },
        );
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query(
        'SELECT "Loans"."borrower" AS "borrower", ' +
          '"Loans"."amount" AS "amount", ' +
          '"Loans"."currency" AS "currency", ' +
          '"LoanFiles"."label" as "label",' +
          '"LoanFiles"."id" AS "id"' +
          ' FROM "Loans" INNER JOIN "LoanFiles" ' +
          'ON "Loans"."id"="LoanFiles"."loanId"' +
          'WHERE "LoanFiles"."isGenerated"=true;',
        { transaction },
      );

      const queryRes = queryResponse[0];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const res = queryRes[i];

        await queryInterface.bulkUpdate(
          'LoanFiles',
          {
            name: `${res.borrower.name} ${res.currency} ${res.amount} ${
              res.label === 'TP Report' ? 'Loan Report' : 'Loan Agreement'
            }`,
          },
          {
            id: res.id,
          },
          { transaction },
        );
      }
    });
  },
};
