'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const companyIds = await queryInterface.sequelize.query('SELECT id from "Companies" LIMIT 2;', { transaction });

      const firstCompanyId = companyIds[0][0].id;
      const secondCompanyId = companyIds[0][1].id;
      const guarantees = [];
      for (let i = 1; i < 11; i++) {
        guarantees.push({
          guarantorId: firstCompanyId,
          principalId: secondCompanyId,
          issueDate: new Date(),
          terminationDate: new Date(2022),
          currency: 'USD',
          amount: 50000000,
          report: JSON.stringify({
            upperBound: 50,
            midPoint: 75,
            lowerBound: 100,
          }),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      await queryInterface.bulkInsert('Guarantees', guarantees, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('Guarantees', null, {});
  },
};
