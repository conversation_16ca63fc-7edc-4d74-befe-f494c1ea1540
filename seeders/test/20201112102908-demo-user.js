'use strict';
require('dotenv').config();
const bcrypt = require('bcrypt');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const client = await queryInterface.sequelize.query('SELECT id from "Clients" LIMIT 1;', { transaction });

      await queryInterface.bulkInsert(
        'Users',
        [
          {
            clientId: client[0][0].id,
            username: 'nord-staging',
            password: await bcrypt.hash(process.env.SEED_DEMO_PASSWORD, 10),
            email: '<EMAIL>',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        { transaction },
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('Users', null, {});
  },
};
