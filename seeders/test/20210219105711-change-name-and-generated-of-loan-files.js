'use strict';
const { generateReportFiles } = require('../../utils/loanUtils/loanUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query(
        'SELECT * from "Loans", "LoanFiles" WHERE "Loans".id = "LoanFiles"."loanId";',
        { transaction },
      );

      const queryRes = queryResponse[0];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const res = queryRes[i];
        const [TPReportFile, agreementFile] = await generateReportFiles(res);

        await Promise.all([
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              name: `${res.borrower.name} ${res.currency} ${res.amount} Loan Report`,
              file: TPReportFile.file,
              isGenerated: true,
            },
            {
              loanId: res.loanId,
              label: 'TP Report',
            },
            { transaction },
          ),
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              name: `${res.borrower.name} ${res.currency} ${res.amount} Loan Agreement`,
              file: agreementFile.file,
              isGenerated: true,
            },
            {
              loanId: res.loanId,
              label: 'Agreement',
            },
            { transaction },
          ),
        ]);
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryRes = await queryInterface.sequelize.query(
        'SELECT "Loans".id, "Loans".borrower, "Loans".currency, "Loans".amount from "Loans", "LoanFiles" WHERE "Loans".id = "LoanFiles"."loanId";',
        { transaction },
      );

      const loans = queryRes[0];
      for (let i = 0, len = loans.length; i < len; i++) {
        const loan = loans[i];

        await Promise.all([
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              name: 'TP Report',
              isGenerated: false,
            },
            {
              loanId: loan.id,
              label: 'TP Report',
            },
            { transaction },
          ),
          queryInterface.bulkUpdate(
            'LoanFiles',
            {
              name: 'Agreement',
              isGenerated: false,
            },
            {
              loanId: loan.id,
              label: 'Agreement',
            },
            { transaction },
          ),
        ]);
      }
    });
  },
};
