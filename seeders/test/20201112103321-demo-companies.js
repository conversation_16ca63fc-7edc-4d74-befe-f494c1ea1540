'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const client = await queryInterface.sequelize.query('SELECT id from "Clients" LIMIT 1;', { transaction });
      const companies = [];
      const company = {
        clientId: client[0][0].id,
        parentCompanyId: null,
        industry: 'Energy',
        country: 'Norway',
        assessment: JSON.stringify({
          answers: {
            ringFencing: false,
            question1: true,
            question2: false,
            question3: false,
            question4: true,
            question5: false,
            question6: false,
            question7: false,
            question8: false,
            question9: false,
            question10: true,
          },
          name: 'Weakly Integral',
        }),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      for (let i = 1; i < 8; i++) {
        companies.push({
          ...company,
          name: 'Demo company' + i,
          note: 'Demo company' + i,
          creditRating: JSON.stringify({
            rating: 'AA+/Aa1',
            ratingAdj: 'AA+/Aa1',
          }),
        });
      }

      await queryInterface.bulkInsert('Companies', companies, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('Companies', null, {});
  },
};
