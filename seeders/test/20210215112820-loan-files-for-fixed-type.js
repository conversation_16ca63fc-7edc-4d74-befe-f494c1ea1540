'use strict';
const { generateReportFiles } = require('../../utils/loanUtils/loanUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryRes = await queryInterface.sequelize.query(
        "SELECT * from \"Loans\" WHERE \"rateType\"->>'type' = 'fixed' AND status = 'Final';",
        { transaction },
      );

      const loans = queryRes[0];
      const loanFiles = [];
      for (let i = 0, len = loans.length; i < len; i++) {
        const loan = loans[i];
        const [TPReportFile, agreementFile] = await generateReportFiles(loan);

        loanFiles.push({
          ...TPReportFile,
          loanId: loan.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        loanFiles.push({
          ...agreementFile,
          loanId: loan.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      await queryInterface.bulkInsert('LoanFiles', loanFiles, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('LoanFiles', {});
  },
};
