'use strict';
const { Op, col } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
    UPDATE "Loans" SET "pricingApproach"=(CASE WHEN "pricingApproach" LIKE '%stand-alone%' THEN 'stand-alone non-standard' ELSE 'implicit non-standard' END)
    WHERE "pricingApproach" LIKE '%non-standard%';
    `);
  },

  down: async (queryInterface, Sequelize) => {},
};
