'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const companyIds = await queryInterface.sequelize.query('SELECT id from "Companies" LIMIT 2;', { transaction });

      const firstCompanyId = companyIds[0][0].id;
      const secondCompanyId = companyIds[0][1].id;
      const loans = [];
      for (let i = 1; i < 11; i++) {
        loans.push({
          lenderId: firstCompanyId,
          borrowerId: secondCompanyId,
          issueDate: new Date(),
          maturityDate: new Date(2022),
          currency: 'USD',
          amount: 10000000 + i,
          paymentFrequency: 'Annual',
          dayCount: 'ACT/ACT',
          seniority: 'Subordinated',
          rateType: JSON.stringify({
            type: 'fixed',
          }),
          report: JSON.stringify({
            upperBound: 50,
            midPoint: 75,
            lowerBound: 100,
          }),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
      await queryInterface.bulkInsert('Loans', loans, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('Loans', null, {});
  },
};
