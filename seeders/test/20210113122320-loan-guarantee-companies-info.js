'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const [client, companyIds] = await Promise.all([
        queryInterface.sequelize.query('SELECT id from "Clients" LIMIT 1;', { transaction }),
        queryInterface.sequelize.query('SELECT * from "Companies" LIMIT 2;', { transaction }),
      ]);

      const clientId = client[0][0].id;
      const firstCompany = companyIds[0][0];
      const secondCompany = companyIds[0][1];

      return Promise.all([
        queryInterface.bulkUpdate(
          'Loans',
          {
            lender: {
              id: firstCompany.id,
              name: firstCompany.name,
              creditRating: firstCompany.creditRating,
              country: firstCompany.country,
              industry: firstCompany.industry,
            },
            borrower: {
              id: secondCompany.id,
              name: secondCompany.name,
              creditRating: secondCompany.creditRating,
              country: secondCompany.country,
              industry: secondCompany.industry,
            },
            clientId,
          },
          {},
          { transaction },
        ),
        queryInterface.bulkUpdate(
          'Guarantees',
          {
            guarantor: {
              id: secondCompany.id,
              name: secondCompany.name,
              creditRating: secondCompany.creditRating,
              country: secondCompany.country,
              industry: secondCompany.industry,
            },
            principal: {
              id: firstCompany.id,
              name: firstCompany.name,
              creditRating: firstCompany.creditRating,
              country: firstCompany.country,
              industry: firstCompany.industry,
            },
            clientId,
          },
          {},
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction((transaction) => {
      return Promise.all([
        queryInterface.bulkUpdate(
          'Loans',
          {
            clientId: null,
            lender: null,
            borrower: null,
          },
          {},
          { transaction },
        ),
        queryInterface.bulkUpdate(
          'Guarantees',
          {
            clientId: null,
            guarantor: null,
            principal: null,
          },
          {},
          { transaction },
        ),
      ]);
    });
  },
};
