'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const [loansRes, guaranteesRes] = await Promise.all([
        queryInterface.sequelize.query('SELECT id, lender, borrower FROM "Loans"', { transaction }),
        queryInterface.sequelize.query('SELECT id, guarantor, principal FROM "Guarantees"', { transaction }),
      ]);

      const loans = loansRes[0];
      for (let i = 0, len = loans.length; i < len; i++) {
        const loan = loans[i];
        const [lender, borrower] = await Promise.all([
          queryInterface.sequelize.query(`SELECT id from "Companies" WHERE id = ${loan.lender.id}`, { transaction }),
          queryInterface.sequelize.query(`SELECT id from "Companies" WHERE id = ${loan.borrower.id}`, { transaction }),
        ]);

        if (Object.keys(lender[0]).length && Object.keys(borrower[0]).length) {
          await queryInterface.bulkUpdate('Loans', { editable: true }, { id: loan.id }, { transaction });
        } else {
          await queryInterface.bulkUpdate('Loans', { editable: false }, { id: loan.id }, { transaction });
        }
      }

      const guarantees = guaranteesRes[0];
      for (let i = 0, len = guarantees.length; i < len; i++) {
        const guarantee = guarantees[i];

        const [guarantor, principal] = await Promise.all([
          queryInterface.sequelize.query(`SELECT id from "Companies" WHERE id = ${guarantee.guarantor.id}`, {
            transaction,
          }),
          queryInterface.sequelize.query(`SELECT id from "Companies" WHERE id = ${guarantee.principal.id}`, {
            transaction,
          }),
        ]);

        if (Object.keys(guarantor[0]).length && Object.keys(principal[0]).length) {
          await queryInterface.bulkUpdate('Guarantees', { editable: true }, { id: guarantee.id }, { transaction });
        } else {
          await queryInterface.bulkUpdate('Guarantees', { editable: false }, { id: guarantee.id }, { transaction });
        }
      }
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
