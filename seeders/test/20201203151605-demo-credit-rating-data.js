'use strict';
const fs = require('fs');
const { promisify } = require('util');
const aReadFile = promisify(fs.readFile);

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const pdf = await aReadFile('./static/NORWEGIAN AIR SHUTTLE ASA.pdf');
    await queryInterface.bulkUpdate(
      'CreditRatings',
      {
        name: 'NORWEGIAN AIR SHUTTLE ASA',
        fiscalYear: '1970-01-01',
        creditRating: { rating: 'CCC+/Caa1' },
        pdf,
      },
      {},
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkUpdate(
      'CreditRatings',
      {
        name: null,
        fiscalYear: null,
        creditRating: null,
        pdf: null,
      },
      {},
    );
  },
};
