'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const client = await queryInterface.sequelize.query('SELECT id from "Clients" LIMIT 1;');

    await queryInterface.bulkInsert(
      'CreditRatings',
      [
        {
          id: 116,
          clientId: client[0][0].id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 118,
          clientId: client[0][0].id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 100,
          clientId: client[0][0].id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 105,
          clientId: client[0][0].id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 107,
          clientId: client[0][0].id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {},
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('CreditRatings', null, {});
  },
};
