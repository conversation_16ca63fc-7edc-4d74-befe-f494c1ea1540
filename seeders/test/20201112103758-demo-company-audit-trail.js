'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const company = await queryInterface.sequelize.query('SELECT id from "Companies" LIMIT 1;', { transaction });
      const companyId = company[0][0].id;

      const companyAuditTrails = [];
      for (let i = 0; i < 5; i++) {
        companyAuditTrails.push({
          companyId,
          parentCompanyId: company[0][0].id,
          name: 'Old company name' + i,
          industry: 'Energy',
          country: 'Croatia',
          note: 'Demo company' + i,
          creditRating: JSON.stringify({
            rating: 'AAA+/Aa1',
            ratingAdj: 'AAA+/Aa1',
          }),
          assessment: JSON.stringify({
            answers: {
              ringFencing: false,
              question1: true,
              question2: false,
              question3: false,
              question4: true,
              question5: false,
              question6: false,
              question7: false,
              question8: false,
              question9: false,
              question10: true,
            },
            name: 'Weakly Integral',
          }),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
      await queryInterface.bulkInsert('CompanyAuditTrails', companyAuditTrails, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('CompanyAuditTrails', null, {});
  },
};
