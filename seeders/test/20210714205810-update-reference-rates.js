'use strict';
const {
  paymentFrequencyToReferenceRateMaturityMapper,
  currencyToReferenceRateMapper,
} = require('../../utils/loanUtils');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const queryResponse = await queryInterface.sequelize.query(
        'SELECT "id", "currency", "paymentFrequency", "rateType" from "Loans" WHERE "rateType"->>\'type\'=\'float\';',
        { transaction },
      );

      const queryRes = queryResponse[0];
      for (let i = 0, len = queryRes.length; i < len; i++) {
        const res = queryRes[i];

        await queryInterface.bulkUpdate(
          'Loans',
          {
            rateType: {
              type: 'float',
              referenceRate: currencyToReferenceRateMapper[res.currency],
              referenceRateMaturity: paymentFrequencyToReferenceRateMaturityMapper[res.paymentFrequency],
            },
          },
          {
            id: res.id,
          },
        );
      }
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
