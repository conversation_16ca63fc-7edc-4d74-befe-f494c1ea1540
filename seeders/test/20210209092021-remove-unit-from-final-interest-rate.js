'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await Promise.all([
        queryInterface.sequelize.query(
          `UPDATE "Loans" SET report = 
          jsonb_set(report::jsonb, '{finalInterestRate}', concat('"', split_part(report->>'finalInterestRate', ' ', '1'), '"')::jsonb, false)`,
          { transaction },
        ),
        queryInterface.sequelize.query(
          `UPDATE "Guarantees" SET report = 
          jsonb_set(report::jsonb, '{finalInterestRate}', concat('"', split_part(report->>'finalInterestRate', ' ', '1'), '"')::jsonb, false)`,
          { transaction },
        ),
      ]);
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
