'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        'UPDATE "Companies" SET "creditRating"="creditRating"::jsonb || \'{"probabilityOfDefault": null}\' WHERE "creditRating"::json->>\'probabilityOfDefault\' IS NULL',
        { transaction },
      );

      const loanQueryResponse = await queryInterface.sequelize.query('SELECT * from "Loans";', { transaction });
      const loanQueryRes = loanQueryResponse[0];
      for (let i = 0, len = loanQueryRes.length; i < len; i++) {
        const loan = loanQueryRes[i];
        await queryInterface.bulkUpdate(
          'Loans',
          {
            lender: {
              ...loan.lender,
              creditRating: {
                ...loan.lender.creditRating,
                probabilityOfDefault: null,
              },
            },
            borrower: {
              ...loan.borrower,
              creditRating: {
                ...loan.borrower.creditRating,
                probabilityOfDefault: null,
              },
            },
          },
          { id: loan.id },
          { transaction },
        );
      }
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
