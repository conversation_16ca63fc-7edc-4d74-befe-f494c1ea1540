'use strict';

module.exports = (sequelize, DataTypes) => {
  const CompanyAuditTrail = sequelize.define(
    'CompanyAuditTrail',
    {
      companyId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      parentCompanyId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      industry: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      country: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      note: DataTypes.TEXT,
      creditRating: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      assessment: DataTypes.JSON,
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      defaultScope: {
        attributes: {
          exclude: ['companyId'],
        },
      },
    },
  );

  CompanyAuditTrail.associate = function (models) {
    CompanyAuditTrail.belongsTo(models.Company, {
      foreignKey: 'companyId',
      targetKey: 'id',
    });
  };

  return CompanyAuditTrail;
};
