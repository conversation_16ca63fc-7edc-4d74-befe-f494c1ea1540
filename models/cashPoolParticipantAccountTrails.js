'use strict';

module.exports = (sequelize, DataTypes) => {
  const ParticipantAccountTrails = sequelize.define(
    'ParticipantAccountTrails',
    {
      participantAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cashPoolBatchId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      balance: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      adjustedCreditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedDebitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedCreditInterestReceived: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedDebitInterestPaid: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      netInterestBenefit: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
    },
    {},
  );

  ParticipantAccountTrails.associate = function (models) {
    ParticipantAccountTrails.belongsTo(models.CashPoolParticipantAccounts, {
      foreignKey: 'participantAccountId',
      targetKey: 'id',
      as: 'account',
    });
    ParticipantAccountTrails.belongsTo(models.CashPoolBatch, {
      foreignKey: 'cashPoolBatchId',
      targetKey: 'id',
      as: 'batch',
    });
  };

  return ParticipantAccountTrails;
};
