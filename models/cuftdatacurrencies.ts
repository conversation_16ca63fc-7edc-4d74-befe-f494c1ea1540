'use strict';
import { DataTypes } from 'sequelize';
import { cuftDataEnums } from '../enums';
type DataTypes = typeof DataTypes;

const CuftDataCurrencies = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataCurrencies = sequelize.define('CuftDataCurrencies', {
    cuftDataId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    currency: {
      type: DataTypes.ENUM(...Object.values(cuftDataEnums.CurrencyToCuftCurrencyEnum)),
      allowNull: false,
    },
  });

  CuftDataCurrencies.associate = function (models: any) {
    CuftDataCurrencies.belongsTo(models.CuftData, {
      foreignKey: 'cuftDataId',
    });
  };

  return CuftDataCurrencies;
};

export = CuftDataCurrencies;
