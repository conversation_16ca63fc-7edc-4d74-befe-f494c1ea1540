'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

module.exports = (sequelize: any, DataTypes: DataTypes) => {
  const CashPoolStatementDataFile = sequelize.define(
    'CashPoolStatementDataFile',
    {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      startDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      endDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      createdByUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {},
  );

  CashPoolStatementDataFile.associate = function (models: any) {
    CashPoolStatementDataFile.belongsTo(models.User, {
      foreignKey: 'createdByUserId',
      targetKey: 'id',
      as: 'createdByUser',
    });
    CashPoolStatementDataFile.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
      as: 'cashPool',
    });
  };

  return CashPoolStatementDataFile;
};
