module.exports = (sequelize, DataTypes) => {
  const Receiving_Users_Notifications = sequelize.define('Receiving_Users_Notifications', {
    receivingUserId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: 'receivingUsersNotificationsUnique',
    },
    notificationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: 'receivingUsersNotificationsUnique',
    },
    isHandled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  });

  Receiving_Users_Notifications.associate = function (models) {
    Receiving_Users_Notifications.belongsTo(models.Notification, {
      foreignKey: 'notificationId',
      targetKey: 'id',
      as: 'notification',
    });
    Receiving_Users_Notifications.belongsTo(models.User, {
      foreignKey: 'receivingUserId',
      targetKey: 'id',
      as: 'user',
    });
  };

  return Receiving_Users_Notifications;
};
