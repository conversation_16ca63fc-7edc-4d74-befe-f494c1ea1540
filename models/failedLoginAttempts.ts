'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const FailedLoginAttempt = (sequelize: any, DataTypes: DataTypes) => {
  const FailedLoginAttempt = sequelize.define('FailedLoginAttempt', {
    username: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: 'usernameCreatedAtUnique',
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: 'emailCreatedAtUnique',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      unique: ['usernameCreatedAtUnique', 'emailCreatedAtUnique'],
    },
  });

  return FailedLoginAttempt;
};

export = FailedLoginAttempt;
