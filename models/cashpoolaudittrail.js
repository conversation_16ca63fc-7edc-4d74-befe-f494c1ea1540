'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPoolAuditTrail = sequelize.define(
    'CashPoolAuditTrail',
    {
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      overnightRate: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      operatingCost: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      operatingCostMarkup: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      riskAnalysisAnswers: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      assessment: {
        type: DataTypes.ENUM('Low', 'Medium', 'High'),
        allowNull: false,
      },
      totalRisk: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      participants: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {},
  );

  CashPoolAuditTrail.associate = function (models) {
    CashPoolAuditTrail.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
    });
    CashPoolAuditTrail.hasMany(models.TopCurrencyAccountAuditTrail, {
      foreignKey: 'cashPoolAuditTrailId',
      sourceKey: 'id',
      as: 'topCurrencyAccounts',
    });
  };

  return CashPoolAuditTrail;
};
