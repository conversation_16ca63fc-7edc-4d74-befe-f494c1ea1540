'use strict';

module.exports = (sequelize, DataTypes) => {
  const Feature = sequelize.define(
    'Feature',
    {
      name: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      fullName: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      path: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: true,
      },
      isModule: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      note: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {},
  );

  Feature.associate = function (models) {
    Feature.hasMany(models.Client_Feature, {
      foreignKey: 'featureId',
    });
  };

  return Feature;
};
