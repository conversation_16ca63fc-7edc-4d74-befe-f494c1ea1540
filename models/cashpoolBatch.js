'use strict';
const { cashPoolEnums } = require('../enums');

module.exports = (sequelize, DataTypes) => {
  const CashPoolBatch = sequelize.define(
    'CashPoolBatch',
    {
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      createdByUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(...Object.values(cashPoolEnums.batchStatus)),
        defaultValue: cashPoolEnums.batchStatus.UNPOOLED,
        allowNull: false,
      },
      startDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      endDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      totalInterestPayableToLeader: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      totalInterestReceivableFromLeader: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      grossBenefit: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      accountsSnapshot: {
        type: DataTypes.JSON,
        allowNull: false,
      },
    },
    {},
  );

  CashPoolBatch.associate = function (models) {
    CashPoolBatch.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
      as: 'cashPool',
    });
    CashPoolBatch.belongsTo(models.User, {
      foreignKey: 'createdByUserId',
      targetKey: 'id',
      as: 'createdByUser',
    });
    CashPoolBatch.hasOne(models.CashPoolBatchFile, {
      foreignKey: 'cashPoolBatchId',
      targetKey: 'id',
      as: 'cashPoolBatchFile',
    });
    CashPoolBatch.hasMany(models.CashPoolBatch_ParticipantPayments, {
      foreignKey: 'cashPoolBatchId',
      sourceKey: 'id',
      as: 'payments',
    });
    CashPoolBatch.hasMany(models.ParticipantAccountTrails, {
      foreignKey: 'cashPoolBatchId',
      sourceKey: 'id',
      as: 'batchParticipantAccounts',
    });
    CashPoolBatch.hasMany(models.CashPoolLeaderBenefit, {
      foreignKey: 'cashPoolBatchId',
      sourceKey: 'id',
      as: 'leaderBenefits',
    });
  };

  return CashPoolBatch;
};
