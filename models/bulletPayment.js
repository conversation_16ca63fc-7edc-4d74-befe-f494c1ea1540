'use strict';

module.exports = (sequelize, DataTypes) => {
  const BulletPayment = sequelize.define(
    'BulletPayment',
    {
      paymentId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      interestPayment: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
    },
    {},
  );

  BulletPayment.associate = function (models) {
    BulletPayment.belongsTo(models.Payment, {
      foreignKey: 'paymentId',
      as: 'bulletPayment',
    });
  };

  return BulletPayment;
};
