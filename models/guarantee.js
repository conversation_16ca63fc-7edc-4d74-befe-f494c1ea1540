'use strict';
const { reportEnums } = require('../enums');

module.exports = (sequelize, DataTypes) => {
  const Guarantee = sequelize.define(
    'Guarantee',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      issueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      originalIssueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      guarantor: {
        type: DataTypes.JSON,
      },
      principal: {
        type: DataTypes.JSON,
      },
      terminationDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      amount: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      report: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      pricingApproach: {
        type: DataTypes.STRING,
      },
      pricingMethodology: {
        type: DataTypes.ENUM(...Object.values(reportEnums.pricingMethodologyEnum)),
        defaultValue: reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH,
        allowNull: false,
      },
      editable: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isPortfolio: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      calculationLog: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      paymentFrequency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      seniority: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      isThirdParty: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      externalId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      finalizedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      movedToAnalysesDate: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
      totalInterest: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      defaultScope: {
        attributes: {
          exclude: ['clientId', 'calculationLog'],
        },
      },
      paranoid: true,
    },
  );

  Guarantee.associate = function (models) {
    Guarantee.belongsTo(models.Client, {
      foreignKey: 'clientId',
      targetKey: 'id',
    });

    Guarantee.hasMany(models.GuaranteeFile, {
      as: 'files',
      foreignKey: 'guaranteeId',
    });

    Guarantee.hasMany(models.Payment, {
      as: 'payments',
      foreignKey: 'guaranteeId',
    });
  };

  return Guarantee;
};
