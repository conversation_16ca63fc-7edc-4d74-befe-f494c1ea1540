'use strict';

module.exports = (sequelize, DataTypes) => {
  const TopCurrencyAccountAuditTrail = sequelize.define(
    'TopCurrencyAccountAuditTrail',
    {
      cashPoolAuditTrailId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      interestType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      overnightRate: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      participants: {
        type: DataTypes.JSON,
        allowNull: false,
      },
    },
    {},
  );

  TopCurrencyAccountAuditTrail.associate = function (models) {
    TopCurrencyAccountAuditTrail.belongsTo(models.CashPoolAuditTrail, {
      foreignKey: 'cashPoolAuditTrailId',
      targetKey: 'id',
    });
  };

  return TopCurrencyAccountAuditTrail;
};
