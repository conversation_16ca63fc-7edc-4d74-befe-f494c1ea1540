'use strict';
import { DataTypes } from 'sequelize';
import { cuftDataEnums } from '../enums';
type DataTypes = typeof DataTypes;

const CuftData = (sequelize: any, DataTypes: DataTypes) => {
  const CuftData = sequelize.define('CuftData', {
    cuftDataFileId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    filingCompanyName: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    cuftBorrowerName: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    cuftBorrowerCountry: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    primarySic: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    allCurrencies: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    moodyPrincipalObligorCreditRating: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    spyPrincipalObligorCreditRating: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    cuftTrancheExecutionDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    cuftTrancheMaturityDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    cuftTrancheTenor: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    cuftTrancheAssetClass: {
      type: DataTypes.ENUM(...Object.values(cuftDataEnums.TrancheAssetClassEnum)),
      allowNull: false,
    },
    cuftTrancheType: {
      type: DataTypes.ENUM(cuftDataEnums.TrancheTypeEnum.Revolver, cuftDataEnums.TrancheTypeEnum.Term),
      allowNull: false,
    },
    cuftTranchePrimaryReferenceRate: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    trancheOrderID: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    reviewCtrlrID: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    exhibitLink: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    deliveryDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  });

  CuftData.associate = function (models: any) {
    CuftData.belongsTo(models.CuftDataFile, {
      foreignKey: 'cuftDataFileId',
      sourceKey: 'id',
      as: 'cuftFiles',
    });

    CuftData.hasMany(models.CuftDataCurrencies, {
      foreignKey: 'cuftDataId',
      sourceKey: 'id',
      as: 'currencies',
    });

    CuftData.hasMany(models.CuftDataCreditRatings, {
      foreignKey: 'cuftDataId',
      sourceKey: 'id',
      as: 'creditRatings',
    });

    CuftData.hasMany(models.CuftDataLeadArrangers, {
      foreignKey: 'cuftDataId',
      sourceKey: 'id',
      as: 'leadArrangers',
    });

    CuftData.hasMany(models.CuftDataGuarantorNames, {
      foreignKey: 'cuftDataId',
      sourceKey: 'id',
      as: 'guarantorNames',
    });

    CuftData.hasMany(models.CuftDataSecondaryBorrowers, {
      foreignKey: 'cuftDataId',
      sourceKey: 'id',
      as: 'secondaryBorrowers',
    });
  };

  return CuftData;
};

export = CuftData;
