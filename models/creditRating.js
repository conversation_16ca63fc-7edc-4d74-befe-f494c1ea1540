'use strict';

module.exports = (sequelize, DataTypes) => {
  const CreditRating = sequelize.define(
    'CreditRating',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      isPortfolio: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      editable: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      company: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      closingDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      creditRating: {
        type: DataTypes.JSON,
      },
      probabilityOfDefault: {
        type: DataTypes.REAL,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Draft', 'Final'),
        allowNull: false,
        defaultValue: 'Draft',
      },
      note: DataTypes.TEXT,
      isTemplateUploaded: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      finalizedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      defaultScope: {
        attributes: {
          exclude: ['clientId'],
        },
      },
      paranoid: true,
    },
  );

  CreditRating.associate = function (models) {
    CreditRating.belongsTo(models.Client, {
      foreignKey: 'clientId',
    });

    CreditRating.hasOne(models.CreditRatingAttribute, {
      as: 'attributes',
      foreignKey: 'creditRatingId',
      sourceKey: 'id',
    });

    CreditRating.hasMany(models.CreditRatingFile, {
      as: 'files',
      foreignKey: 'creditRatingId',
    });
  };

  return CreditRating;
};
