'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

export = (sequelize: any, DataTypes: DataTypes) => {
  const BackToBackLoan = sequelize.define(
    'BackToBackLoan',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      ultimateLender: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      ultimateBorrower: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      riskTakerId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      issueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      originalIssueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      maturityDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      amount: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      paymentFrequency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      seniority: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      rateType: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      report: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      pricingApproach: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      editable: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      type: {
        type: DataTypes.ENUM('Bullet', 'Balloon'),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isPortfolio: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      capm: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      capmOverride: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      capmRecommendation: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      expectedLoss: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      expectedLossOverride: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      expectedLossRecommendation: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      overrideToggles: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      standardRemuneration: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      calculationLog: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      isThirdParty: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      totalInterest: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      movedToAnalysesDate: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
      externalId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      finalizedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      defaultScope: {
        attributes: {
          exclude: ['clientId', 'calculationLog'],
        },
      },
      paranoid: true,
    },
  );

  BackToBackLoan.associate = function (models: any) {
    BackToBackLoan.belongsTo(models.Client, {
      foreignKey: 'clientId',
    });

    BackToBackLoan.hasMany(models.BackToBackLoanFile, {
      as: 'files',
      foreignKey: 'loanId',
    });

    BackToBackLoan.hasMany(models.BackToBackLoanLeg, {
      as: 'legs',
      foreignKey: 'loanId',
    });
  };

  return BackToBackLoan;
};
