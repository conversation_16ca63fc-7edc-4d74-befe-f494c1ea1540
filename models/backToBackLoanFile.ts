'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

export = (sequelize: any, DataTypes: DataTypes) => {
  const BackToBackLoanFile = sequelize.define(
    'BackToBackLoanFile',
    {
      loanId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      label: {
        type: DataTypes.ENUM('TP Report', 'Agreement', 'Credit Rating', 'Other'),
        defaultValue: 'Other',
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isGenerated: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
    },
    {},
  );

  BackToBackLoanFile.associate = function (models: any) {
    BackToBackLoanFile.belongsTo(models.BackToBackLoan, {
      foreignKey: 'loanId',
    });
  };

  return BackToBackLoanFile;
};
