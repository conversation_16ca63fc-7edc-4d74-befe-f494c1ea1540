'use strict';

import cls from 'cls-hooked';
import fs from 'fs';
import path from 'path';
import { Sequelize, DataTypes } from 'sequelize';
import configs from '../config/config';
import logging from '../utils/logger/sql';

const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const config: any = configs[env];
const db: any = {};

const namespace = cls.createNamespace('nord');
Sequelize.useCLS(namespace);

const sequelize = new Sequelize({ ...config, logging });

fs.readdirSync(__dirname)
  .filter((file) => file.indexOf('.') !== 0 && file !== basename && ['.js', '.ts'].includes(file.slice(-3)))
  .forEach((file) => {
    const model = require(path.join(__dirname, file))(sequelize, DataTypes);
    db[model.name] = model;
  });

Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

export = db;
