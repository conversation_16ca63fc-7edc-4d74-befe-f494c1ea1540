'use strict';

module.exports = (sequelize, DataTypes) => {
  const Payment = sequelize.define(
    'Payment',
    {
      loanId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      guaranteeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      interestRatePerInterestRepaymentFrequency: {
        type: DataTypes.DECIMAL,
        allowNull: false,
      },
      paymentNumber: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      paymentAmount: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      isPaid: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      paymentDueDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      isPrincipalPayment: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      referenceRate: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      interestCalculationDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {},
  );

  Payment.associate = function (models) {
    Payment.belongsTo(models.Loan, {
      foreignKey: 'loanId',
      as: 'loan',
    });
    Payment.belongsTo(models.Guarantee, {
      foreignKey: 'guaranteeId',
      as: 'guarantee',
    });
    Payment.hasOne(models.BulletPayment, {
      foreignKey: 'paymentId',
      as: 'bulletPayment',
    });
    Payment.hasOne(models.BalloonPayment, {
      foreignKey: 'paymentId',
      as: 'balloonPayment',
    });
  };

  return Payment;
};
