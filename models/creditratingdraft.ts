'use strict';

import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CreditRatingDraft = (sequelize: any, DataTypes: DataTypes) => {
  const CreditRatingDraft = sequelize.define('CreditRatingDraft', {
    clientId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    company: {
      type: DataTypes.JSON,
    },
    closingDate: {
      type: DataTypes.DATE,
    },
    isTemplateUploaded: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  });

  CreditRatingDraft.associate = function (models: any) {
    CreditRatingDraft.belongsTo(models.Client, {
      foreignKey: 'clientId',
    });

    CreditRatingDraft.hasOne(models.CreditRatingAttributesDraft, {
      as: 'attributes',
      foreignKey: 'creditRatingDraftId',
      sourceKey: 'id',
    });
  };

  return CreditRatingDraft;
};

export = CreditRatingDraft;
