'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataLeadArrangers = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataLeadArrangers = sequelize.define('CuftDataLeadArrangers', {
    cuftDataId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });

  CuftDataLeadArrangers.associate = function (models: any) {
    CuftDataLeadArrangers.belongsTo(models.CuftData, {
      foreignKey: 'cuftDataId',
    });
  };

  return CuftDataLeadArrangers;
};

export = CuftDataLeadArrangers;
