'use strict';

module.exports = (sequelize, DataTypes) => {
  const Cash_Pool_Participants = sequelize.define('Cash_Pool_Participants', {
    companyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: 'cashPoolCompanyParticipantUnique',
    },
    cashPoolId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: 'cashPoolCompanyParticipantUnique',
    },
    isLeader: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    uniqueId: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  });

  Cash_Pool_Participants.associate = function (models) {
    Cash_Pool_Participants.belongsTo(models.Company, {
      foreignKey: 'companyId',
      targetKey: 'id',
      as: 'company',
    });
    Cash_Pool_Participants.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
      as: 'cashPool',
    });
    Cash_Pool_Participants.hasMany(models.CashPoolParticipantAccounts, {
      foreignKey: 'cashPoolParticipantId',
      sourceKey: 'id',
      as: 'accounts',
    });
    Cash_Pool_Participants.hasMany(models.CashPoolBatch_ParticipantPayments, {
      foreignKey: 'creditorId',
      sourceKey: 'id',
    });
    Cash_Pool_Participants.hasMany(models.CashPoolBatch_ParticipantPayments, {
      foreignKey: 'debtorId',
      sourceKey: 'id',
    });
  };

  return Cash_Pool_Participants;
};
