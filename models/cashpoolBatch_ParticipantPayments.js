'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPoolBatch_ParticipantPayments = sequelize.define(
    'CashPoolBatch_ParticipantPayments',
    {
      cashPoolBatchId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cashPoolParticipantAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      creditorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      debtorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      interestPayable: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      interestReceivable: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      isPaid: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {},
  );

  CashPoolBatch_ParticipantPayments.associate = function (models) {
    CashPoolBatch_ParticipantPayments.belongsTo(models.CashPoolBatch, {
      foreignKey: 'cashPoolBatchId',
      targetKey: 'id',
      as: 'batch',
    });
    CashPoolBatch_ParticipantPayments.belongsTo(models.CashPoolParticipantAccounts, {
      foreignKey: 'cashPoolParticipantAccountId',
      targetKey: 'id',
      as: 'participantAccount',
    });
    CashPoolBatch_ParticipantPayments.belongsTo(models.Cash_Pool_Participants, {
      foreignKey: 'creditorId',
      targetKey: 'id',
      as: 'creditor',
    });
    CashPoolBatch_ParticipantPayments.belongsTo(models.Cash_Pool_Participants, {
      foreignKey: 'debtorId',
      targetKey: 'id',
      as: 'debtor',
    });
    CashPoolBatch_ParticipantPayments.hasOne(models.CashPoolStatementData, {
      foreignKey: 'cashPoolPaymentId',
      targetKey: 'id',
      as: 'statementData',
    });
  };

  return CashPoolBatch_ParticipantPayments;
};
