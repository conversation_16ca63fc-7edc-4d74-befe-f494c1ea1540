'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataFile = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataFile = sequelize.define('CuftDataFile', {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    extension: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    mimeType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });

  CuftDataFile.associate = function (models: any) {
    CuftDataFile.hasMany(models.CuftData, {
      foreignKey: 'cuftDataFileId',
    });
  };

  return CuftDataFile;
};

export = CuftDataFile;
