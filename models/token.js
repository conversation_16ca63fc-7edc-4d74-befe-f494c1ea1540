'use strict';
module.exports = (sequelize, DataTypes) => {
  const Token = sequelize.define(
    'Token',
    {
      accessToken: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      lastActivity: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {},
  );

  Token.associate = function (models) {
    Token.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'token',
      targetKey: 'id',
    });
  };

  return Token;
};
