'use strict';
import { DataTypes } from 'sequelize';

type DataTypes = typeof DataTypes;

const Country = (sequelize: any, DataTypes: DataTypes) => {
  const Country = sequelize.define('Country', {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isoCode: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    region: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    cuftCode: {
      type: DataTypes.STRING,
      defaultValue: 'UNKNOWN',
      allowNull: false,
    },
    flagEmoji: {
      type: DataTypes.STRING,
      defaultValue: '',
      allowNull: false,
    },
  });

  Country.associate = function (models: any) {
    Country.hasMany(models.WHTOrigin, { foreignKey: 'countryId' });
    Country.hasMany(models.WHTRecipient, { foreignKey: 'countryId' });
    Country.hasMany(models.EquityRiskPremiums, { foreignKey: 'countryId' });
  };

  return Country;
};

export = Country;
