'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

module.exports = (sequelize: any, DataTypes: DataTypes) => {
  const TemplateFile = sequelize.define(
    'TemplateFile',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: 'clientCountryCompanyLabelUnique',
      },
      country: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: 'clientCountryCompanyLabelUnique',
      },
      companyId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        unique: 'clientCountryCompanyLabelUnique',
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      label: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: 'clientCountryCompanyLabelUnique',
      },
      type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {},
  );

  TemplateFile.associate = function (models: any) {
    TemplateFile.belongsTo(models.Client, {
      foreignKey: 'clientId',
    });
    TemplateFile.belongsTo(models.Company, {
      as: 'company',
      foreignKey: 'companyId',
    });
  };

  return TemplateFile;
};
