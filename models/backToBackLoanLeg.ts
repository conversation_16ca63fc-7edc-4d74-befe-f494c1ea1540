'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

export = (sequelize: any, DataTypes: DataTypes) => {
  const BackToBackLoanLeg = sequelize.define('BackToBackLoanLeg', {
    loanId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    lender: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    borrower: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    report: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    calculationLog: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    ordinal: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  });

  BackToBackLoanLeg.associate = function (models: any) {
    BackToBackLoanLeg.belongsTo(models.BackToBackLoan, {
      foreignKey: 'loanId',
    });
  };

  return BackToBackLoanLeg;
};
