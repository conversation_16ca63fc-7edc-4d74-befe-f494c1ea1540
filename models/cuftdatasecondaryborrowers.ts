'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataSecondaryBorrowers = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataSecondaryBorrowers = sequelize.define('CuftDataSecondaryBorrowers', {
    cuftDataId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });

  CuftDataSecondaryBorrowers.associate = function (models: any) {
    CuftDataSecondaryBorrowers.belongsTo(models.CuftData, {
      foreignKey: 'cuftDataId',
    });
  };

  return CuftDataSecondaryBorrowers;
};

export = CuftDataSecondaryBorrowers;
