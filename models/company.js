'use strict';
const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define(
    'Company',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      parentCompanyId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      industry: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      country: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      note: DataTypes.TEXT,
      creditRating: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      assessment: DataTypes.JSON,
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      hooks: {
        beforeUpdate: ({ dataValues, _previousDataValues }) => {
          const { id, ...values } = _previousDataValues;

          const companyAuditTrail = { companyId: id, createdBy: dataValues.updatedBy, ...values };

          return sequelize.models.CompanyAuditTrail.create(companyAuditTrail);
        },
        beforeDestroy(instance) {
          const { id } = instance;

          return Promise.all([
            sequelize.models.Loan.update(
              {
                editable: false,
              },
              {
                where: {
                  [Op.or]: [{ lender: { id } }, { borrower: { id } }],
                },
              },
            ),
            sequelize.models.Guarantee.update(
              {
                editable: false,
              },
              {
                where: {
                  [Op.or]: [{ guarantor: { id } }, { principal: { id } }],
                },
              },
            ),
            sequelize.models.CreditRating.update(
              {
                editable: false,
              },
              {
                where: {
                  company: { id },
                },
              },
            ),
          ]);
        },
      },
      defaultScope: {
        attributes: {
          exclude: ['clientId'],
        },
      },
    },
  );

  Company.associate = function (models) {
    Company.hasMany(models.Company, {
      foreignKey: 'parentCompanyId',
      targetKey: 'id',
    });

    Company.belongsTo(models.Client, {
      foreignKey: 'clientId',
      targetKey: 'id',
    });

    Company.hasMany(models.CompanyAuditTrail, {
      foreignKey: 'companyId',
      sourceKey: 'id',
    });

    Company.hasMany(models.Cash_Pool_Participants, {
      foreignKey: 'companyId',
      sourceKey: 'id',
    });

    Company.hasMany(models.CashPool, {
      foreignKey: 'leaderId',
      sourceKey: 'id',
    });
  };

  return Company;
};
