'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataExportCounters = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataExportCounters = sequelize.define('CuftDataExportCounters', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  });

  CuftDataExportCounters.associate = function (models: any) {
    CuftDataExportCounters.belongsTo(models.User, {
      foreignKey: 'userId',
      targetKey: 'id',
    });
  };

  return CuftDataExportCounters;
};

export = CuftDataExportCounters;
