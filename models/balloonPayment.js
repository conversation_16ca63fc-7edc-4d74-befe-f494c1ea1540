'use strict';

module.exports = (sequelize, DataTypes) => {
  const BalloonPayment = sequelize.define(
    'BalloonPayment',
    {
      paymentId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      compoundedInterest: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      additionalInterest: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      compoundingPeriodEndDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {},
  );

  BalloonPayment.associate = function (models) {
    BalloonPayment.belongsTo(models.Payment, {
      foreignKey: 'paymentId',
      as: 'balloonPayment',
    });
  };

  return BalloonPayment;
};
