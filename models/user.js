'use strict';
const bcrypt = require('bcrypt');

const { rolesEnum, dateFormats, decimalPoints, timezones } = require('../enums');
const saltRounds = 10;

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define(
    'User',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      username: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: false,
      },
      fullName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      role: {
        type: DataTypes.ENUM(rolesEnum.SUPERADMIN, rolesEnum.ADMIN, rolesEnum.USER),
        defaultValue: rolesEnum.USER,
        allowNull: false,
      },
      areNotificationsMuted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      dateFormat: {
        type: DataTypes.ENUM(...Object.values(dateFormats)),
        allowNull: false,
        defaultValue: dateFormats['YYYY-MM-DD'],
      },
      timezone: {
        type: DataTypes.ENUM(...Object.values(timezones)),
        allowNull: false,
        defaultValue: timezones['Etc/GMT'],
      },
      decimalPoint: {
        type: DataTypes.ENUM(...Object.values(decimalPoints)),
        allowNull: false,
        defaultValue: decimalPoints.DOT,
      },
    },
    {
      hooks: {
        beforeCreate: async (user) => {
          if (user.password) {
            user.password = await bcrypt.hash(user.password, saltRounds);
          }
        },
      },
    },
  );

  User.prototype.validatePassword = function (password) {
    return bcrypt.compare(password, this.password);
  };

  User.associate = function (models) {
    User.belongsTo(models.Client, {
      foreignKey: 'clientId',
      as: 'client',
      targetKey: 'id',
    });
    User.hasMany(models.Notification, {
      foreignKey: 'createdByUserId',
      sourceKey: 'id',
    });
    User.hasMany(models.Receiving_Users_Notifications, {
      foreignKey: 'receivingUserId',
      sourceKey: 'id',
    });
    User.hasMany(models.Token, {
      foreignKey: 'userId',
      as: 'tokens',
      sourceKey: 'id',
    });
    User.hasMany(models.CashPoolBatch, {
      foreignKey: 'createdByUserId',
      sourceKey: 'id',
    });
    User.hasMany(models.CuftDataSavedSearches, {
      foreignKey: 'userId',
      sourceKey: 'id',
      as: 'cuftDataSavedSearches',
    });
    User.hasMany(models.CuftDataExportCounters, {
      foreignKey: 'userId',
      sourceKey: 'id',
      as: 'cuftDataExportCounters',
    });
    User.hasMany(models.SocialLogin, {
      foreignKey: 'userId',
      sourceKey: 'id',
      as: 'socialLogins',
    });
  };

  return User;
};
