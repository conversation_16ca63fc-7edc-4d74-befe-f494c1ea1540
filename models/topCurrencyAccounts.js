'use strict';

module.exports = (sequelize, DataTypes) => {
  const TopCurrencyAccounts = sequelize.define(
    'TopCurrencyAccounts',
    {
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      interestType: {
        type: DataTypes.ENUM('fixed', 'float'),
        allowNull: false,
      },
      overnightRate: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
    },
    {},
  );

  TopCurrencyAccounts.associate = function (models) {
    TopCurrencyAccounts.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
    });
    TopCurrencyAccounts.hasMany(models.CashPoolParticipantAccounts, {
      foreignKey: 'topCurrencyAccountId',
      as: 'accounts',
    });
  };

  return TopCurrencyAccounts;
};
