'use strict';

module.exports = (sequelize, DataTypes) => {
  return sequelize.define('ProviderData', {
    issueDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    curveName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    region: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    industryGroup: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    seniority: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    rating: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fixed3M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float3M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield3M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount3M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed6M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float6M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield6M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount6M: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed1Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float1Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield1Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount1Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed2Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float2Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield2Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount2Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed3Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float3Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield3Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount3Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed5Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float5Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield5Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount5Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed7Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float7Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield7Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount7Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed10Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float10Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield10Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount10Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed15Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float15Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield15Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount15Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed20Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float20Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield20Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount20Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed25Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float25Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield25Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount25Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    fixed30Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    float30Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    yield30Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
    discount30Y: {
      type: DataTypes.REAL,
      allowNull: true,
    },
  });
};
