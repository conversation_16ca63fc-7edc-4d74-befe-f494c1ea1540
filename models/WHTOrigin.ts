'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const WHTOrigin = (sequelize: any, DataTypes: DataTypes) => {
  const WHTOrigin = sequelize.define(
    'WHTOrigin',
    {
      countryId: {
        type: DataTypes.NUMBER,
        allowNull: false,
      },
      dividend: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      interest: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      royalty: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {},
  );

  WHTOrigin.associate = function (models: any) {
    WHTOrigin.hasMany(models.WHTRecipient, {
      foreignKey: 'originId',
      sourceKey: 'id',
      as: 'recipients',
    });
    WHTOrigin.belongsTo(models.Country, {
      foreignKey: 'countryId',
      as: 'originCountry',
    });
  };

  return WHTOrigin;
};

export = WHTOrigin;
