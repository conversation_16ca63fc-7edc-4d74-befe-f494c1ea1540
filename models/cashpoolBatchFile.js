'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPoolBatchFile = sequelize.define(
    'CashPoolBatchFile',
    {
      cashPoolBatchId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {},
  );

  CashPoolBatchFile.associate = function (models) {
    CashPoolBatchFile.belongsTo(models.CashPoolBatch, {
      foreignKey: 'cashPoolBatchId',
      as: 'batch',
    });
  };

  return CashPoolBatchFile;
};
