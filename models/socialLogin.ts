'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

export = (sequelize: any, DataTypes: DataTypes) => {
  const SocialLogin = sequelize.define('SocialLogin', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    provider: {
      type: DataTypes.ENUM('azure', 'google'),
      allowNull: false,
    },
    credential: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });

  SocialLogin.associate = function (models: any) {
    SocialLogin.belongsTo(models.User, {
      foreignKey: 'userId',
      targetKey: 'id',
      as: 'user',
    });
  };

  return SocialLogin;
};
