'use strict';
import { DataTypes } from 'sequelize';

type DataTypes = typeof DataTypes;

const EquityRiskPremiums = (sequelize: any, DataTypes: DataTypes) => {
  const EquityRiskPremiums = sequelize.define(
    'EquityRiskPremiums',
    {
      countryId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      moodyRating: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      ratingBasedDefaultSpread: {
        type: DataTypes.DECIMAL,
        allowNull: false,
      },
      totalEquityRiskPremium: {
        type: DataTypes.DECIMAL,
        allowNull: false,
      },
      countryRiskPremium: {
        type: DataTypes.DECIMAL,
        allowNull: false,
      },
      sovereignCds: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      totalEquityRiskPremium2: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      countryRiskPremium3: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
    },
    { freezeTableName: true },
  );

  EquityRiskPremiums.associate = function (models: any) {
    EquityRiskPremiums.belongsTo(models.Country, {
      foreignKey: 'countryId',
      as: 'country',
    });
  };

  return EquityRiskPremiums;
};

export = EquityRiskPremiums;
