'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataGuarantorNames = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataGuarantorNames = sequelize.define('CuftDataGuarantorNames', {
    cuftDataId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });

  CuftDataGuarantorNames.associate = function (models: any) {
    CuftDataGuarantorNames.belongsTo(models.CuftData, {
      foreignKey: 'cuftDataId',
    });
  };

  return CuftDataGuarantorNames;
};

export = CuftDataGuarantorNames;
