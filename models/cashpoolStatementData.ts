'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CashPoolStatementData = (sequelize: any, DataTypes: DataTypes) => {
  const CashPoolStatementData = sequelize.define(
    'CashPoolStatementData',
    {
      cashPoolAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cashPoolPaymentId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      transactionReferenceNumber: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      statementNumber: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      balanceChange: {
        type: DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      statementDate: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      comment: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      cashPoolBatchId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {},
  );

  CashPoolStatementData.associate = function (models: any) {
    CashPoolStatementData.belongsTo(models.CashPoolParticipantAccounts, {
      foreignKey: 'cashPoolAccountId',
      as: 'account',
    });
    CashPoolStatementData.belongsTo(models.CashPoolBatch_ParticipantPayments, {
      foreignKey: 'cashPoolPaymentId',
      as: 'payment',
    });
  };

  return CashPoolStatementData;
};

export = CashPoolStatementData;
