'use strict';
import { DataTypes } from 'sequelize';
import { creditRatingEnums } from '../enums';
type DataTypes = typeof DataTypes;

const CuftDataCreditRatings = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataCreditRatings = sequelize.define('CuftDataCreditRatings', {
    cuftDataId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    creditRatingName: {
      type: DataTypes.ENUM(...Object.values(creditRatingEnums.CreditRatingValueEnum)),
      allowNull: false,
    },
    creditRatingValue: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
  });

  CuftDataCreditRatings.associate = function (models: any) {
    CuftDataCreditRatings.belongsTo(models.CuftData, {
      foreignKey: 'cuftDataId',
    });
  };

  return CuftDataCreditRatings;
};

export = CuftDataCreditRatings;
