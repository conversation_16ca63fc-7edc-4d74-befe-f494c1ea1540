'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPoolFile = sequelize.define(
    'CashPoolFile',
    {
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      label: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isGenerated: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {},
  );

  CashPoolFile.associate = function (models) {
    CashPoolFile.belongsTo(models.CashPool, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
      as: 'cashPool',
    });
  };

  return CashPoolFile;
};
