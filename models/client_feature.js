'use strict';

module.exports = (sequelize, DataTypes) => {
  const Client_Feature = sequelize.define(
    'Client_Feature',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      featureId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      isEnabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      values: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {},
  );

  Client_Feature.associate = function (models) {
    Client_Feature.belongsTo(models.Client, {
      foreignKey: 'clientId',
      as: 'client',
      targetKey: 'id',
    });

    Client_Feature.belongsTo(models.Feature, {
      foreignKey: 'featureId',
      as: 'feature',
      targetKey: 'id',
    });
  };

  return Client_Feature;
};
