'use strict';

const TableColumn = (sequelize: any, DataTypes: any) => {
  const TableColumn = sequelize.define(
    'TableColumn',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      loan: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      guarantee: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {},
  );

  TableColumn.associate = function (models: any) {
    TableColumn.belongsTo(models.Client, { foreignKey: 'clientId' });
  };

  return TableColumn;
};

export = TableColumn;
