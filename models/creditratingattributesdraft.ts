'use strict';

import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CreditRatingAttributesDraft = (sequelize: any, DataTypes: DataTypes) => {
  const CreditRatingAttributesDraft = sequelize.define('CreditRatingAttributesDraft', {
    creditRatingDraftId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    naceSector: {
      type: DataTypes.STRING,
    },
    naceSectorGroup: {
      type: DataTypes.STRING,
    },
    option: {
      type: DataTypes.ENUM('Consolidated', 'Unconsolidated'),
    },
    months: {
      type: DataTypes.INTEGER,
    },
    currency: {
      type: DataTypes.STRING,
    },
    exchangeRate: {
      type: DataTypes.FLOAT,
    },
    fixedAssets: {
      type: DataTypes.FLOAT,
    },
    intangibleFixedAssets: {
      type: DataTypes.FLOAT,
    },
    tangibleFixedAssets: {
      type: DataTypes.FLOAT,
    },
    otherFixedAssets: {
      type: DataTypes.FLOAT,
    },
    currentAssets: {
      type: DataTypes.FLOAT,
    },
    stocks: {
      type: DataTypes.FLOAT,
    },
    debtors: {
      type: DataTypes.FLOAT,
    },
    otherCurrentAssets: {
      type: DataTypes.FLOAT,
    },
    cashAndCashEquivalent: {
      type: DataTypes.FLOAT,
    },
    totalAssets: {
      type: DataTypes.FLOAT,
    },
    shareholdersFunds: {
      type: DataTypes.FLOAT,
    },
    capital: {
      type: DataTypes.FLOAT,
    },
    otherShareholdersFunds: {
      type: DataTypes.FLOAT,
    },
    treasuryShares: {
      type: DataTypes.FLOAT,
    },
    nonCurrentLiabilities: {
      type: DataTypes.FLOAT,
    },
    longTermDebt: {
      type: DataTypes.FLOAT,
    },
    otherNonCurrentLiabilities: {
      type: DataTypes.FLOAT,
    },
    provisions: {
      type: DataTypes.FLOAT,
    },
    currentLiabilities: {
      type: DataTypes.FLOAT,
    },
    loans: {
      type: DataTypes.FLOAT,
    },
    creditors: {
      type: DataTypes.FLOAT,
    },
    otherCurrentLiabilities: {
      type: DataTypes.FLOAT,
    },
    totalShareFundsAndLiabilities: {
      type: DataTypes.FLOAT,
    },
    operatingRevenueTurnover: {
      type: DataTypes.FLOAT,
    },
    sales: {
      type: DataTypes.FLOAT,
    },
    materialCosts: {
      type: DataTypes.FLOAT,
    },
    costOfEmployees: {
      type: DataTypes.FLOAT,
    },
    EBITDA: {
      type: DataTypes.FLOAT,
    },
    depreciation: {
      type: DataTypes.FLOAT,
    },
    EBIT: {
      type: DataTypes.FLOAT,
    },
    financialRevenue: {
      type: DataTypes.FLOAT,
    },
    financialExpenses: {
      type: DataTypes.FLOAT,
    },
    financialPL: {
      type: DataTypes.FLOAT,
    },
    PLBeforeTax: {
      type: DataTypes.FLOAT,
    },
    taxation: {
      type: DataTypes.FLOAT,
    },
    PLAfterTax: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherRevenue: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherExpenses: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherPL: {
      type: DataTypes.FLOAT,
    },
    PLForPeriod: {
      type: DataTypes.FLOAT,
    },
    costOfGoodSold: {
      type: DataTypes.FLOAT,
    },
    otherOperatingExpenses: {
      type: DataTypes.FLOAT,
    },
    grossProfit: {
      type: DataTypes.FLOAT,
    },
    overriddenStatus: {
      type: DataTypes.JSON,
    },
  });

  CreditRatingAttributesDraft.associate = function (models: any) {
    CreditRatingAttributesDraft.belongsTo(models.CreditRatingDraft, {
      foreignKey: 'creditRatingDraftId',
    });
  };

  return CreditRatingAttributesDraft;
};

export = CreditRatingAttributesDraft;
