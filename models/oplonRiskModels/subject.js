const { countryToISOMapping } = require('../../utils/creditRatingUtils');

module.exports = function (data) {
  const { company, attributes } = data;
  return {
    subject_type: 'WorldOCompany',
    user_email: process.env.OPLON_RISK_EMAIL,
    name: company.name,
    standard_status_code: 'ACTIVE',
    standard_legal_form_code: 'PRIVATE_LIMITED_COMPANY',
    nace_sector: attributes.naceSector,
    registration_date: '',
    address: '',
    country_iso_code: countryToISOMapping[company.country],
    city: '',
  };
};
