module.exports = function (data) {
  const { closingDate, attributes } = data;
  return {
    user_email: process.env.OPLON_RISK_EMAIL,
    accounts: [
      {
        closing_date: new Date(closingDate),
        is_consolidated: attributes.option === 'Consolidated',
        number_of_months: attributes.months,
        currency: attributes.currency || '',
        exchange_rate: attributes.exchangeRate || '',
        financial_data: {
          balance_sheet: {
            total_assets: attributes.totalAssets,
            shareholders_funds: attributes.shareholdersFunds,
            fixed_assets: attributes.fixedAssets,
            intangible_fixed_assets: attributes.intangibleFixedAssets,
            tangible_fixed_assets: attributes.tangibleFixedAssets,
            other_fixed_assets: attributes.otherFixedAssets,
            current_assets: attributes.currentAssets,
            stocks: attributes.stocks,
            debtors: attributes.debtors,
            other_current_assets: attributes.otherCurrentAssets,
            cash_cash_equivalent: attributes.cashAndCashEquivalent,
            capital: attributes.capital,
            other_shareholders_funds: attributes.otherShareholdersFunds,
            non_current_liabilities: attributes.nonCurrentLiabilities,
            other_non_current_liabilities: attributes.otherNonCurrentLiabilities,
            provisions: attributes.provisions,
            current_liabilities: attributes.currentLiabilities,
            loans: attributes.loans,
            creditors: attributes.creditors,
            other_current_liabilities: attributes.otherCurrentLiabilities,
            total_shareholders_funds_liabilities: attributes.totalShareFundsAndLiabilities,
            long_term_debt: attributes.longTermDebt,
            working_capital: '',
            export_turnover: '',
            net_current_assets: '',
            treasury_shares: attributes.treasuryShares,
          },
          income_statement: {
            operating_revenue_or_turnover: attributes.operatingRevenueTurnover,
            pl_for_period: attributes.PLForPeriod,
            sales: attributes.sales,
            material_costs: attributes.materialCosts,
            costs_of_employees: attributes.costOfEmployees,
            ebitda: attributes.EBITDA,
            financial_pl: attributes.financialPL,
            financial_revenue: attributes.financialRevenue,
            financial_expenses: attributes.financialExpenses,
            interest_paid: attributes.interestPaid,
            pl_before_tax: attributes.PLBeforeTax,
            extraordinary_and_other_pl: attributes.extrAndOtherPL,
            extraordinary_and_other_revenue: attributes.extrAndOtherRevenue,
            extraordinary_and_other_expenses: attributes.extrAndOtherExpenses,
            taxation: attributes.taxation,
            pl_after_tax: attributes.PLAfterTax,
            cash_flow: '',
            added_value: '',
            costs_of_good_sold: attributes.costOfGoodSold,
            gross_profit: attributes.grossProfit,
            other_operating_expenses: attributes.otherOperatingExpenses,
            research_development_expenses: '',
            enterprise_value: '',
            number_of_employees: '',
            ebit: attributes.EBIT,
            depreciation: attributes.depreciation,
          },
        },
      },
    ],
  };
};
