'use strict';
import { DataTypes } from 'sequelize';

import { betaUtils } from '../utils/betaUtils';

type DataTypes = typeof DataTypes;

const Beta = (sequelize: any, DataTypes: DataTypes) => {
  const Beta = sequelize.define('Betas', {
    industry: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    region: {
      type: DataTypes.ENUM(...Object.values(betaUtils.regions)),
      allowNull: false,
    },
    numberOfFirms: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    beta: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    deRatio: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    effectiveTaxRate: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    unleveredBeta: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    cashFirmValue: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    unleveredBetaCorrectedForCash: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    hiloRisk: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    standardDeviationOfEquity: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    standardDeviationInOperatingIncomeLast10Years: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
  });

  return Beta;
};

export = Beta;
