'use strict';

const Notification = (sequelize: any, DataTypes: any) => {
  const Notification = sequelize.define(
    'Notification',
    {
      createdByUserId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      url: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      action: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
      },
    },
    {},
  );

  Notification.associate = function (models: any) {
    Notification.belongsTo(models.User, {
      foreignKey: 'createdByUserId',
      as: 'createdByUser',
      targetKey: 'id',
    });
    Notification.hasMany(models.Receiving_Users_Notifications, {
      foreignKey: 'notificationId',
      sourceKey: 'id',
    });
  };

  return Notification;
};

export = Notification;
