'use strict';
const { cashPoolEnums } = require('../enums');
const { PHYSICAL, NOTIONAL, NORDIC } = cashPoolEnums.cashPoolTypes;

module.exports = (sequelize, DataTypes) => {
  const CashPool = sequelize.define(
    'CashPool',
    {
      leaderId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      externalId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      country: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(PHYSICAL, NOTIONAL, NORDIC),
        allowNull: false,
      },
      currencies: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      operatingCost: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      operatingCostMarkup: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      riskAnalysisAnswers: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      assessment: {
        type: DataTypes.ENUM('Low', 'Medium', 'High'),
        allowNull: false,
      },
      totalRisk: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      grossBenefit: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      interestType: {
        type: DataTypes.ENUM('fixed', 'float'),
        allowNull: true,
      },
      overnightRate: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      estimateRatesCalculationLog: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {},
  );

  CashPool.associate = function (models) {
    CashPool.belongsTo(models.Company, {
      foreignKey: 'leaderId',
      as: 'leader',
    });
    CashPool.belongsTo(models.Client, {
      foreignKey: 'clientId',
    });
    CashPool.hasMany(models.Cash_Pool_Participants, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'participants',
    });
    CashPool.hasMany(models.CashPoolFile, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'files',
    });
    CashPool.hasMany(models.CashPoolLeaderBenefit, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'leaderBenefits',
    });
    CashPool.hasMany(models.CashPoolBatch, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'batches',
    });
    CashPool.hasMany(models.TopCurrencyAccounts, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'topCurrencyAccounts',
    });
    CashPool.hasMany(models.CashPoolAuditTrail, {
      foreignKey: 'cashPoolId',
      sourceKey: 'id',
      as: 'auditTrails',
    });
  };

  return CashPool;
};
