'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CashPoolLeaderBenefit = (sequelize: any, DataTypes: DataTypes) => {
  const CashPoolLeaderBenefit = sequelize.define(
    'CashPoolLeaderBenefit',
    {
      cashPoolId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      cashPoolBatchId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      leaderBenefit: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {},
  );

  CashPoolLeaderBenefit.associate = function (models: any) {
    CashPoolLeaderBenefit.belongsTo(models.Company, {
      foreignKey: 'cashPoolId',
      targetKey: 'id',
      as: 'cashPool',
    });

    CashPoolLeaderBenefit.belongsTo(models.CashPoolBatch, {
      foreignKey: 'cashPoolBatchId',
      targetKey: 'id',
      as: 'batch',
    });
  };

  return CashPoolLeaderBenefit;
};

export = CashPoolLeaderBenefit;
