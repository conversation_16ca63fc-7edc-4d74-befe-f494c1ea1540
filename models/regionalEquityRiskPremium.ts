'use strict';
import { DataTypes } from 'sequelize';
import { regionAbbreviations } from '../utils/providerDataUtils/constants';

type DataTypes = typeof DataTypes;

const RegionalEquityRiskPremiums = (sequelize: any, DataTypes: DataTypes) => {
  const RegionalEquityRiskPremiums = sequelize.define(
    'RegionalEquityRiskPremiums',
    {
      region: {
        type: DataTypes.ENUM(...Object.keys(regionAbbreviations)),
        allowNull: false,
      },
      equityRiskPremium: {
        type: DataTypes.DECIMAL,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
    },
    { freezeTableName: true },
  );

  return RegionalEquityRiskPremiums;
};

export = RegionalEquityRiskPremiums;
