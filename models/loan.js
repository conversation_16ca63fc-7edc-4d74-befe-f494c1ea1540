'use strict';

module.exports = (sequelize, DataTypes) => {
  const Loan = sequelize.define(
    'Loan',
    {
      clientId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      issueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      originalIssueDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      lender: {
        type: DataTypes.JSON,
      },
      borrower: {
        type: DataTypes.JSON,
      },
      maturityDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      amount: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      paymentFrequency: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      seniority: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      rateType: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      report: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      pricingApproach: {
        type: DataTypes.STRING,
      },
      editable: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      type: {
        type: DataTypes.ENUM('Bullet', 'Balloon'),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isPortfolio: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      calculationLog: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      isThirdParty: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      dayCount: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isApproachCalculated: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      externalId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      finalizedBy: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      movedToAnalysesDate: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
      totalInterest: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      defaultScope: {
        attributes: {
          exclude: ['clientId', 'calculationLog'],
        },
      },
      paranoid: true,
    },
  );

  Loan.associate = function (models) {
    Loan.belongsTo(models.Client, { foreignKey: 'clientId' });
    Loan.hasMany(models.LoanFile, { as: 'files', foreignKey: 'loanId' });
    Loan.hasMany(models.Payment, { as: 'payments', foreignKey: 'loanId' });
  };

  return Loan;
};
