'use strict';

module.exports = (sequelize, DataTypes) => {
  const CreditRatingFile = sequelize.define(
    'CreditRatingFile',
    {
      creditRatingId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      label: {
        type: DataTypes.ENUM('Credit Rating', 'Other'),
        defaultValue: 'Other',
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isGenerated: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {},
  );

  CreditRatingFile.associate = function (models) {
    CreditRatingFile.belongsTo(models.CreditRating, {
      foreignKey: 'creditRatingId',
    });
  };

  return CreditRatingFile;
};
