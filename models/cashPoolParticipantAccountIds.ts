'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const ParticipantAccountIds = (sequelize: any, DataTypes: DataTypes) => {
  const ParticipantAccountIds = sequelize.define(
    'ParticipantAccountIds',
    {
      cashPoolAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      externalId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {},
  );

  ParticipantAccountIds.associate = function (models: any) {
    ParticipantAccountIds.belongsTo(models.CashPoolParticipantAccounts, {
      foreignKey: 'cashPoolAccountId',
      as: 'account',
    });
  };

  return ParticipantAccountIds;
};

export = ParticipantAccountIds;
