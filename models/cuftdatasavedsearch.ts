'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const CuftDataSavedSearches = (sequelize: any, DataTypes: DataTypes) => {
  const CuftDataSavedSearches = sequelize.define('CuftDataSavedSearches', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    search: {
      type: DataTypes.JSON,
      allowNull: false,
    },
  });

  CuftDataSavedSearches.associate = function (models: any) {
    CuftDataSavedSearches.belongsTo(models.User, {
      foreignKey: 'userId',
      targetKey: 'id',
    });
  };

  return CuftDataSavedSearches;
};

export = CuftDataSavedSearches;
