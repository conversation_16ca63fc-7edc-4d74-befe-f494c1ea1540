'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const ParticipantAccountExcludedIds = (sequelize: any, DataTypes: DataTypes) => {
  const ParticipantAccountExcludedIds = sequelize.define(
    'ParticipantAccountExcludedIds',
    {
      cashPoolAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      excludedId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {},
  );

  ParticipantAccountExcludedIds.associate = function (models: any) {
    ParticipantAccountExcludedIds.belongsTo(models.CashPoolParticipantAccounts, {
      foreignKey: 'cashPoolAccountId',
      as: 'account',
    });
  };

  return ParticipantAccountExcludedIds;
};

export = ParticipantAccountExcludedIds;
