'use strict';

module.exports = (sequelize, DataTypes) => {
  const CreditRatingAttribute = sequelize.define('CreditRatingAttribute', {
    creditRatingId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    naceSector: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    option: {
      type: DataTypes.ENUM('Consolidated', 'Unconsolidated'),
      allowNull: false,
    },
    months: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
    },
    exchangeRate: {
      type: DataTypes.FLOAT,
    },
    fixedAssets: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    intangibleFixedAssets: {
      type: DataTypes.FLOAT,
    },
    tangibleFixedAssets: {
      type: DataTypes.FLOAT,
    },
    otherFixedAssets: {
      type: DataTypes.FLOAT,
    },
    currentAssets: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    stocks: {
      type: DataTypes.FLOAT,
    },
    debtors: {
      type: DataTypes.FLOAT,
    },
    otherCurrentAssets: {
      type: DataTypes.FLOAT,
    },
    cashAndCashEquivalent: {
      type: DataTypes.FLOAT,
    },
    totalAssets: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    shareholdersFunds: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    capital: {
      type: DataTypes.FLOAT,
    },
    otherShareholdersFunds: {
      type: DataTypes.FLOAT,
    },
    treasuryShares: {
      type: DataTypes.FLOAT,
    },
    nonCurrentLiabilities: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    longTermDebt: {
      type: DataTypes.FLOAT,
    },
    otherNonCurrentLiabilities: {
      type: DataTypes.FLOAT,
    },
    provisions: {
      type: DataTypes.FLOAT,
    },
    currentLiabilities: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    loans: {
      type: DataTypes.FLOAT,
    },
    creditors: {
      type: DataTypes.FLOAT,
    },
    otherCurrentLiabilities: {
      type: DataTypes.FLOAT,
    },
    totalShareFundsAndLiabilities: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    operatingRevenueTurnover: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    sales: {
      type: DataTypes.FLOAT,
    },
    materialCosts: {
      type: DataTypes.FLOAT,
    },
    costOfEmployees: {
      type: DataTypes.FLOAT,
    },
    EBITDA: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    depreciation: {
      type: DataTypes.FLOAT,
    },
    EBIT: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    financialRevenue: {
      type: DataTypes.FLOAT,
    },
    financialExpenses: {
      type: DataTypes.FLOAT,
    },
    financialPL: {
      type: DataTypes.FLOAT,
    },
    PLBeforeTax: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    taxation: {
      type: DataTypes.FLOAT,
    },
    PLAfterTax: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherRevenue: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherExpenses: {
      type: DataTypes.FLOAT,
    },
    extrAndOtherPL: {
      type: DataTypes.FLOAT,
    },
    PLForPeriod: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    costOfGoodSold: {
      type: DataTypes.FLOAT,
    },
    otherOperatingExpenses: {
      type: DataTypes.FLOAT,
    },
    grossProfit: {
      type: DataTypes.FLOAT,
    },
    overriddenStatus: {
      type: DataTypes.JSON,
    },
  });

  CreditRatingAttribute.associate = function (models) {
    CreditRatingAttribute.belongsTo(models.CreditRating, {
      as: 'attributes',
      foreignKey: 'creditRatingId',
      targetKey: 'id',
    });
  };

  return CreditRatingAttribute;
};
