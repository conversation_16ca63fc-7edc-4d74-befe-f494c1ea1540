'use strict';

module.exports = (sequelize, DataTypes) => {
  const LoanFile = sequelize.define(
    'LoanFile',
    {
      loanId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      label: {
        type: DataTypes.ENUM('TP Report', 'Agreement', 'Credit Rating', 'Other'),
        defaultValue: 'Other',
        allowNull: false,
      },
      extension: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Final', 'Draft'),
        defaultValue: 'Draft',
        allowNull: false,
      },
      mimeType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isGenerated: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {},
  );

  LoanFile.associate = function (models) {
    LoanFile.belongsTo(models.Loan, {
      foreignKey: 'loanId',
    });
  };

  return LoanFile;
};
