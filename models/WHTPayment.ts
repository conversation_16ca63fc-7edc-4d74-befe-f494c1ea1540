'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const WHTPayment = (sequelize: any, DataTypes: DataTypes) => {
  const WHTPayment = sequelize.define(
    'WHTPayment',
    {
      loanId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      paymentId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      paymentNumber: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      isPaid: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      paymentAmount: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
    },
    {},
  );

  WHTPayment.associate = function (models: any) {
    WHTPayment.belongsTo(models.Loan, {
      foreignKey: 'loanId',
      as: 'loan',
    });
    WHTPayment.belongsTo(models.Payment, {
      foreignKey: 'paymentId',
      as: 'loanPayment',
    });
  };

  return WHTPayment;
};

export = WHTPayment;
