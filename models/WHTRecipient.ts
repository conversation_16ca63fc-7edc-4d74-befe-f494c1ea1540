'use strict';
import { DataTypes } from 'sequelize';
type DataTypes = typeof DataTypes;

const WHTRecipient = (sequelize: any, DataTypes: DataTypes) => {
  const WHTRecipient = sequelize.define(
    'WHTRecipient',
    {
      originId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      countryId: {
        type: DataTypes.NUMBER,
        allowNull: false,
      },
      dividend: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      interest: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      royalty: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      interestExplanation: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {},
  );

  WHTRecipient.associate = function (models: any) {
    WHTRecipient.belongsTo(models.WHTOrigin, {
      foreignKey: 'originId',
      as: 'origin',
    });
    WHTRecipient.belongsTo(models.Country, {
      foreignKey: 'countryId',
      as: 'recipientCountry',
    });
  };

  return WHTRecipient;
};

export = WHTRecipient;
