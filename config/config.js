require('dotenv').config();

module.exports = {
  development: {
    database: process.env.DEV_DATABASE,
    username: process.env.DEV_DATABASE_USERNAME,
    password: process.env.DEV_DATABASE_PASSWORD,
    host: process.env.DEV_DATABASE_HOST,
    port: process.env.DEV_DATABASE_PORT,
    dialect: 'postgres',
    seederStorage: 'sequelize',
  },
  test: {
    database: process.env.TEST_DATABASE,
    username: process.env.TEST_DATABASE_USERNAME,
    password: process.env.TEST_DATABASE_PASSWORD,
    host: process.env.TEST_DATABASE_HOST,
    port: process.env.TEST_DATABASE_PORT,
    dialect: 'postgres',
    seederStorage: 'sequelize',
  },
  production: {
    database: process.env.PROD_DATABASE,
    username: process.env.PROD_DATABASE_USERNAME,
    password: process.env.PROD_DATABASE_PASSWORD,
    host: process.env.PROD_DATABASE_HOST,
    port: process.env.PROD_DATABASE_PORT,
    dialectOptions: {
      ssl: true,
    },
    dialect: 'postgres',
    seederStorage: 'sequelize',
  },
  ftp: {
    dnsHost: process.env.FTP_DNS_HOST,
    host: process.env.FTP_HOST,
    username: process.env.FTP_USERNAME,
    password: process.env.FTP_PASSWORD,
  },
  csp: {
    connectSrcHosts: process.env.CSP_CONNECT_SRC_HOSTS,
    imgSrcHosts: process.env.CSP_IMG_SRC_HOSTS,
    scriptSrcElemHosts: process.env.CSP_SCRIPT_SRC_ELEM_HOSTS,
  },
  contact: {
    toEmail: process.env.TO_EMAIL,
    fromEmail: process.env.FROM_EMAIL,
    recaptchaSecret: process.env.RECAPTCHA_SECRET,
    recaptchaVerifyUrl: 'https://www.google.com/recaptcha/api/siteverify',
  },
};
