const express = require('express');
const router = express.Router();

const creditRatingController = require('../controllers/creditRatingController');
const creditRatingDraftRouter = require('./creditRatingDraftRouter');
const { rolesEnum } = require('../enums');
const { compression, permissions } = require('../middlewares');
const creditRatingSchemas = require('../schemas/creditRatingSchemas');
const validator = require('../schemas/validator');

router.use('/draft', creditRatingDraftRouter);
router.get('/', creditRatingController.getCreditRatings);
router.get('/deleted', creditRatingController.getDeletedCreditRatings);
router.get('/template', compression, creditRatingController.getTemplate);
router.get('/:id', creditRatingController.getCreditRating);
router.get('/template/import', compression, creditRatingController.getImportTemplate);
router.delete('/:id', permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]), creditRatingController.deleteCreditRating);
router.patch('/:id', permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]), creditRatingController.restoreCreditRating);
router.post('/', validator(creditRatingSchemas.creditRatingSchema), creditRatingController.postCreditRating);
router.post('/bulk', validator(creditRatingSchemas.creditRatingsSchema), creditRatingController.postCreditRatings);
router.post(
  '/import',
  validator(creditRatingSchemas.importedCreditRatingSchema),
  creditRatingController.postImportedCreditRating,
);
router.post(
  '/import/mass',
  validator(creditRatingSchemas.importedCreditRatingsSchema),
  creditRatingController.postImportedCreditRatings,
);
router.put('/:id', validator(creditRatingSchemas.creditRatingSchema), creditRatingController.putCreditRating);
router.put(
  '/imported/:id',
  validator(creditRatingSchemas.importedCreditRatingSchema),
  creditRatingController.putImportedCreditRating,
);
router.put(
  '/:id/note',
  validator(creditRatingSchemas.updateCreditRatingNoteSchema),
  creditRatingController.putCreditRatingNote,
);
router.put(
  '/:id/status',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(creditRatingSchemas.updateCreditRatingStatusSchema),
  creditRatingController.putCreditRatingStatus,
);
router.put(
  '/:id/portfolio',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(creditRatingSchemas.updateCreditRatingIsPortfolioSchema),
  creditRatingController.putCreditRatingIsPortfolio,
);

module.exports = router;
