import express from 'express';

import cashPoolPaymentController from '../controllers/cashPoolPaymentController';
import cashPoolSchemas from '../schemas/cashPoolSchemas';
import validator from '../schemas/validator';

const router = express.Router({ mergeParams: true });

router.get('/all', cashPoolPaymentController.getCashPoolPayments);
router.get('/export-excel', cashPoolPaymentController.exportCashPoolPayments);
router.patch(
  '/:paymentId',
  validator(cashPoolSchemas.markCashPoolPaymentAsPaidSchema),
  cashPoolPaymentController.markCashPoolPaymentAsPaid,
);

export default router;
