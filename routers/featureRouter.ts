import express from 'express';
const router = express.Router();

import featureController from '../controllers/featureController';
import { rolesEnum } from '../enums';
import { permissions } from '../middlewares';
import { updateClientFeaturesSchema } from '../schemas/featureSchemas';
import schemaValidator from '../schemas/validator';

router.get('/', featureController.getClientFeatures);
router.get('/used-number', featureController.getClientFeatureUsedNumbers);
router.get(
  '/client/:clientId',
  permissions([rolesEnum.SUPERADMIN]),
  featureController.getClientFeaturesOfSpecificClient,
);
router.patch(
  '/client/:clientId',
  permissions([rolesEnum.SUPERADMIN]),
  schemaValidator(updateClientFeaturesSchema),
  featureController.updateEntireClientFeatures,
);

export = router;
