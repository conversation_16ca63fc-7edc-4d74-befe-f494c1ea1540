import express from 'express';

import creditRatingDraftController from '../controllers/creditRatingDraftController';
import creditRatingSchemas from '../schemas/creditRatingSchemas';
import schemaValidator from '../schemas/validator';

const router = express.Router();

router.get('/', creditRatingDraftController.getCreditRatingDrafts);
router.post(
  '/',
  schemaValidator(creditRatingSchemas.creditRatingDraftSchema),
  creditRatingDraftController.postCreditRatingDraft,
);
router.delete('/:id', creditRatingDraftController.deleteCreditRatingDraft);

export = router;
