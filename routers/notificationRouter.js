const express = require('express');
const router = express.Router();

const notificationController = require('../controllers/notificationController');
const { createNotificationSchema, updateNotificationSchema } = require('../schemas/notificationSchemas');
const validator = require('../schemas/validator');

router.get('/', notificationController.getNotifications);
router.get('/unhandled', notificationController.getUnhandledNotifications);
router.post('/', validator(createNotificationSchema), notificationController.createNotification);
router.delete('/:id', notificationController.deleteNotification);
router.patch('/:id/isHandled', validator(updateNotificationSchema), notificationController.updateNotificationIsHandled);

module.exports = router;
