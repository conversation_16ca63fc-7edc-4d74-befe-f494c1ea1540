import express from 'express';

import cuftDataController from '../controllers/cuftDataController';
import { createCuftDataSearchSchema } from '../schemas/cuftDataSchemas';
import schemaValidator from '../schemas/validator';

const router = express.Router();

router.get('/', cuftDataController.getCuftDataSavedSearch);
router.post('/', schemaValidator(createCuftDataSearchSchema), cuftDataController.createCuftDataSavedSearch);

export default router;
