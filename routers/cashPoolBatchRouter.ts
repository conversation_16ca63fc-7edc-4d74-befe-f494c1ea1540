import express from 'express';

import cashPoolBatchController from '../controllers/cashPoolBatchController';
import { compression, multerMiddleware } from '../middlewares';
import cashPoolPaymentRouter from './cashPoolPaymentRouter';
import validator from '../schemas/validator';
import cashPoolSchemas from '../schemas/cashPoolSchemas';

const router = express.Router({ mergeParams: true });

router.get('/', cashPoolBatchController.getBatches);
router.get('/:batchId/file', compression, cashPoolBatchController.downloadBatchFile);
router.get('/range', cashPoolBatchController.getBatchRange);
router.get('/payment-interest-dates', cashPoolBatchController.getPaymentInterestDates);
router.post('/file-upload', multerMiddleware, cashPoolBatchController.uploadBatchFile);
router.post('/:batchId/calculate-physical', cashPoolBatchController.runBatchPhysicalCashPool);
router.post('/:batchId/calculate-nordic', cashPoolBatchController.runBatchNordicCashPool);
router.post('/:batchId/calculate-notional', cashPoolBatchController.runBatchPhysicalCashPool);
router.post(
  '/create-from-sftp-data-physical',
  validator(cashPoolSchemas.createBatchFromSftpDataSchema),
  cashPoolBatchController.createPhysicalBatchFromSftpData,
);
router.post(
  '/create-from-sftp-data-nordic',
  validator(cashPoolSchemas.createBatchFromSftpDataSchema),
  cashPoolBatchController.createNordicBatchFromSftpData,
);

router.delete('/:batchId', cashPoolBatchController.deleteBatch);

router.use('/:batchId/payment', cashPoolPaymentRouter);

export = router;
