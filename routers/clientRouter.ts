import express from 'express';

import clientController from '../controllers/clientController';
import rolesEnum from '../enums/roles';
import { permissions } from '../middlewares';
import * as clientSchemas from '../schemas/clientSchemas';
import validator from '../schemas/validator';
import zodValidator from '../schemas/zodValidator';

const router = express.Router();

router.get('/:clientId', permissions([rolesEnum.SUPERADMIN]), clientController.getClient);
router.get('/', permissions([rolesEnum.SUPERADMIN]), clientController.getClients);
router.post(
  '/',
  permissions([rolesEnum.SUPERADMIN]),
  validator(clientSchemas.createClientSchema),
  clientController.createClient,
);
router.patch(
  '/',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  zodValidator(clientSchemas.updateClientValidator),
  clientController.updateClient,
);
router.delete('/:id', permissions([rolesEnum.SUPERADMIN]), clientController.deleteClient);

export default router;
