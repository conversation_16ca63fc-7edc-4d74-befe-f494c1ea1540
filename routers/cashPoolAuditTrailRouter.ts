import express from 'express';

import topCurrencyAccountAuditTrailRouter from './topCurrencyAccountRouterAuditTrail';
import cashPoolAuditTrailController from '../controllers/cashPoolAuditTrailController';
import { rolesEnum } from '../enums';
import { permissions } from '../middlewares';

const router = express.Router({ mergeParams: true });

router.get('/', cashPoolAuditTrailController.getCashPoolAuditTrailAll);
router.get('/:auditTrailId', cashPoolAuditTrailController.getCashPoolAuditTrail);
router.delete(
  '/:auditTrailId',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  cashPoolAuditTrailController.deleteCashPoolAuditTrail,
);

router.use('/:auditTrailId/top-currency-account', topCurrencyAccountAuditTrailRouter);

export default router;
