import express from 'express';

import companyAuditTrailController from '../controllers/companyAuditTrailController';
import { rolesEnum } from '../enums';
import { permissions } from '../middlewares';

const router = express.Router({ mergeParams: true });

router.get('/', companyAuditTrailController.getCompanyAuditTrailAll);
router.get('/:auditId', companyAuditTrailController.getCompanyAuditTrail);
router.delete(
  '/:auditId',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  companyAuditTrailController.deleteCompanyAuditTrail,
);

export default router;
