import express from 'express';

import cuftDataController from '../controllers/cuftDataController';
import { permissions } from '../middlewares';
import { rolesEnum } from '../enums';
import cuftDataFileRouter from './cuftDataFileRouter';
import cuftDataSavedSearchesRouter from './cuftDataSavedSearchesRouter';
import { getCuftDataSchema } from '../schemas/cuftDataSchemas';
import schemaValidator from '../schemas/validator';

const router = express.Router();

router.post('/', schemaValidator(getCuftDataSchema), cuftDataController.getCuftData);
router.post(
  '/available-filtering-options',
  schemaValidator(getCuftDataSchema),
  cuftDataController.getAvailableFilteringOptions,
);
router.post('/export', schemaValidator(getCuftDataSchema), cuftDataController.exportCuftData);

router.use('/file', permissions([rolesEnum.SUPERADMIN]), cuftDataFileRouter);
router.use('/saved-search', cuftDataSavedSearchesRouter);

export default router;
