const express = require('express');
const router = express.Router({ mergeParams: true });

const guaranteePaymentController = require('../controllers/guaranteePaymentController');
const paymentSchemas = require('../schemas/paymentSchemas');
const validator = require('../schemas/validator');

router.get('/', guaranteePaymentController.getGuaranteePaymentsByGuaranteeId);
router.get('/all', guaranteePaymentController.getPayments);
router.get('/next', guaranteePaymentController.getNextGuaranteePayments);
router.get('/export-excel', guaranteePaymentController.exportGuaranteePaymentsExcel);
router.post(
  '/calculate-payments',
  validator(paymentSchemas.calculatePaymentsForAnalyses),
  guaranteePaymentController.calculatePaymentsForAnalyses,
);
router.patch(
  '/:paymentId',
  validator(paymentSchemas.markPaymentAsPaidSchema),
  guaranteePaymentController.markPaymentAsPaid,
);

module.exports = router;
