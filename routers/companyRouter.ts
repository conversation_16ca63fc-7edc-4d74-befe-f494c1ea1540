import express from 'express';

import companyController from '../controllers/companyController';
import { rolesEnum } from '../enums';
import { permissions } from '../middlewares';
import companySchemas from '../schemas/companySchemas';
import validator from '../schemas/validator';
import companyAuditTrailRouter from './companyAuditTrailRouter';

const router = express.Router();

router.get('/', companyController.getCompanies);
router.get('/template', companyController.getTemplate);
router.get('/:id', companyController.getCompany);
router.put('/:id', validator(companySchemas.updateCompanySchema), companyController.putCompany);
router.delete('/:id', permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]), companyController.deleteCompany);
router.post('/', validator(companySchemas.createCompanySchema), companyController.postCompany);
router.post('/bulk', validator(companySchemas.createCompaniesSchema), companyController.postCompanies);
router.post(
  '/implicit-support',
  validator(companySchemas.implicitSupportSchema),
  companyController.implicitSupport,
  companyController.adjustCreditRating,
);
router.post('/adjust-rating', validator(companySchemas.adjustCreditRatingSchema), companyController.adjustCreditRating);
router.post(
  '/adjust-pod',
  validator(companySchemas.adjustProbabilityOfDefaultSchema),
  companyController.adjustProbabilityOfDefault,
);
router.post('/export', companyController.exportCompanies);
router.use('/:id/audit', companyAuditTrailRouter);

export default router;
