import express from 'express';

import tableColumnController from '../controllers/tableColumnController';
import * as tableColumnSchemas from '../schemas/tableColumnSchemas';
import zodValidator from '../schemas/zodValidator';

const router = express.Router();

router.get('/', tableColumnController.getTableColumns);
router.patch('/', zodValidator(tableColumnSchemas.updateTableColumnSchema), tableColumnController.updateTableColumns);

export default router;
