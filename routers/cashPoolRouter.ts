import express from 'express';

import cashPoolController from '../controllers/cashPoolController';
import cashPoolSchemas from '../schemas/cashPoolSchemas';
import validator from '../schemas/validator';
import cashPoolAuditTrailRouter from './cashPoolAuditTrailRouter';
import cashPoolBatchRouter from './cashPoolBatchRouter';
import cashPoolParticipantTrailRouter from './cashPoolParticipantTrailRouter';
import topCurrencyAccountRouter from './topCurrencyAccountRouter';
import cashPoolStatementDataRouter from './cashPoolStatementDataRouter';
import { featureNames } from '../enums';
import { features } from '../middlewares';

const router = express.Router();

router.get('/', cashPoolController.getCashPools);
router.get('/:cashPoolId', cashPoolController.getCashPool);
router.get('/:cashPoolId/participants', cashPoolController.getCashPoolParticipants);
router.post('/export-all', cashPoolController.exportAll);
router.post(
  '/physical',
  features(featureNames.PHYSICAL_CASH_POOL),
  validator(cashPoolSchemas.createPhysicalCashPoolSchema),
  cashPoolController.createPhysicalCashPool,
);
router.post(
  '/nordic',
  features(featureNames.NORDIC_CASH_POOL),
  validator(cashPoolSchemas.createNordicCashPoolSchema),
  cashPoolController.createNordicCashPool,
);
router.post(
  '/notional',
  features(featureNames.NOTIONAL_CASH_POOL),
  validator(cashPoolSchemas.createNotionalCashPoolSchema),
  cashPoolController.createNotionalCashPool,
);
router.post(
  '/:cashPoolId/export-physical',
  validator(cashPoolSchemas.exportCashPoolSchema),
  cashPoolController.exportPhysicalCashPool,
);
router.post(
  '/:cashPoolId/export-nordic',
  validator(cashPoolSchemas.exportCashPoolSchema),
  cashPoolController.exportNordicCashPool,
);
router.post(
  '/estimate-participant-rates',
  validator(cashPoolSchemas.estimateParticipantRatesSchema),
  cashPoolController.estimateParticipantRates,
);
router.put(
  '/physical/:cashPoolId',
  validator(cashPoolSchemas.updatePhysicalCashPoolSchema),
  cashPoolController.updateEntirePhysicalCashPool,
);
router.put(
  '/nordic/:cashPoolId',
  validator(cashPoolSchemas.updateNordicCashPoolSchema),
  cashPoolController.updateEntireNordicCashPool,
);
router.put(
  '/notional/:cashPoolId',
  validator(cashPoolSchemas.updateNotionalCashPoolSchema),
  cashPoolController.updateEntireNotionalCashPool,
);
router.patch(
  '/:cashPoolId/note',
  validator(cashPoolSchemas.updateCashPoolNoteSchema),
  cashPoolController.updateCashPoolNote,
);
router.put(
  '/:cashPoolId/participants/:participantId/unique-id',
  validator(cashPoolSchemas.updateParticipantUniqueIdSchema),
  cashPoolController.updateParticipantUniqueId,
);
router.delete('/:cashPoolId', cashPoolController.deleteCashPool);

router.use('/:cashPoolId/audit-trail', cashPoolAuditTrailRouter);
router.use('/:cashPoolId/trail', cashPoolParticipantTrailRouter);
router.use('/:cashPoolId/batch', cashPoolBatchRouter);
router.use('/:cashPoolId/top-currency-account', topCurrencyAccountRouter);
router.use('/:cashPoolId/statement-data', cashPoolStatementDataRouter);

export default router;
