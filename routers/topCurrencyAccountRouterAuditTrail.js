const express = require('express');
const router = express.Router({ mergeParams: true });

const topCurrencyAccountAuditTrailController = require('../controllers/topCurrencyAccountAuditTrailController');

router.get('/', topCurrencyAccountAuditTrailController.getTopCurrencyAccountAuditTrailAll);
router.get('/:auditTrailTopCurrencyAccountId', topCurrencyAccountAuditTrailController.getTopCurrencyAccountAuditTrail);

module.exports = router;
