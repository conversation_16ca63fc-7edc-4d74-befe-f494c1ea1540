import express from 'express';

import userController from '../controllers/userController';
import rolesEnum from '../enums/roles';
import { authMethodMiddleware, permissions } from '../middlewares';
import * as userSchemas from '../schemas/userSchemas';
import zodValidator from '../schemas/zodValidator';

const router = express.Router();

router.get('/', authMethodMiddleware, permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]), userController.getUsers);
router.get('/me', authMethodMiddleware, userController.getMyInfo);
router.get('/admins', authMethodMiddleware, userController.getAdmins);
router.post('/', zodValidator(userSchemas.loginSchema), userController.login);
router.post('/logout', authMethodMiddleware, userController.logout);
router.post('/refresh-token', userController.refreshToken);
router.post(
  '/login-as-anyone',
  authMethodMiddleware,
  permissions([rolesEnum.SUPERADMIN]),
  zodValidator(userSchemas.loginAsAnyoneSchema),
  userController.loginAsAnyone,
);
router.post(
  '/request-password-reset',
  zodValidator(userSchemas.requestPasswordResetSchema),
  userController.requestPasswordReset,
);
router.patch('/reset-password', zodValidator(userSchemas.resetPasswordSchema), userController.resetPassword);
router.post(
  '/new',
  authMethodMiddleware,
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  zodValidator(userSchemas.createUserSchema),
  userController.createUser,
);
router.patch(
  '/promote/:id',
  authMethodMiddleware,
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  userController.promoteToAdmin,
);
router.patch('/demote/:id', authMethodMiddleware, permissions([rolesEnum.SUPERADMIN]), userController.demoteToUser);
router.patch(
  '/settings',
  authMethodMiddleware,
  zodValidator(userSchemas.updateSettingsSchema),
  userController.updateSettings,
);
router.patch(
  '/mute-notifications',
  authMethodMiddleware,
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  zodValidator(userSchemas.updateNotificationsMutedSchema),
  userController.updateNotificationAreMuted,
);
router.delete('/:id', authMethodMiddleware, permissions([rolesEnum.SUPERADMIN]), userController.deleteUser);

export default router;
