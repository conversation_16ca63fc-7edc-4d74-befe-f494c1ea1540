import express from 'express';

import { permissions } from '../middlewares';
import rolesEnum from '../enums/roles';
import b2bLoanController from '../controllers/b2bLoanController';
import schemaValidator from '../schemas/validator';
import * as b2bLoanSchemas from '../schemas/b2bLoanSchemas';
import * as loanSchemas from '../schemas/loanSchemas';
const router = express.Router({ mergeParams: true });

router.get('/', b2bLoanController.getLoans);
router.get('/deleted', b2bLoanController.getDeletedLoans);
router.get('/:id', b2bLoanController.getLoan);
router.post('/', schemaValidator(b2bLoanSchemas.createB2BLoanSchema), b2bLoanController.createLoan);
router.post(
  '/equity-risk-premium',
  schemaValidator(b2bLoanSchemas.getEquityRiskPremiumSchema),
  b2bLoanController.getEquityRiskPremium,
);
router.post('/beta', schemaValidator(b2bLoanSchemas.getBetaSchema), b2bLoanController.getBeta);
router.post('/:id/payment/calculate-payments', b2bLoanController.calculatePaymentsForAnalyses);
router.post('/:id/agreement', b2bLoanController.generateAgreements);
router.post('/:id/tp-report', b2bLoanController.generateTpReport);
router.put('/:id', schemaValidator(b2bLoanSchemas.createB2BLoanSchema), b2bLoanController.putLoan);
router.put('/:id/rates', schemaValidator(b2bLoanSchemas.updateB2BLoanRatesSchema), b2bLoanController.putLoanRates);
router.put('/:id/leg-rates', b2bLoanController.putLegRates);
router.put(
  '/:id/status',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  schemaValidator(loanSchemas.updateLoanStatusSchema),
  b2bLoanController.putLoanStatus,
);
router.put(
  '/:id/portfolio',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  schemaValidator(loanSchemas.updateLoanIsPortfolioSchema),
  b2bLoanController.putLoanIsPortfolio,
);
router.put('/:id/note', schemaValidator(loanSchemas.updateLoanNoteSchema), b2bLoanController.putLoanNote);
router.patch('/:id', b2bLoanController.restoreLoan);
router.delete('/:id', b2bLoanController.deleteLoan);

export default router;
