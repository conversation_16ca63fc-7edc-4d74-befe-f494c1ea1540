import express from 'express';

import cashPoolParticipantTrailController from '../controllers/cashPoolParticipantTrailController';
import cashPoolSchemas from '../schemas/cashPoolSchemas';
import validator from '../schemas/validator';

const router = express.Router({ mergeParams: true });

router.post(
  '/',
  validator(cashPoolSchemas.getCashPoolTrailsSchema),
  cashPoolParticipantTrailController.getCashPoolParticipantTrails,
);

router.post(
  '/structural-positions',
  validator(cashPoolSchemas.getStructuralPositionsSchema),
  cashPoolParticipantTrailController.getCashPoolStructuralPositions,
);

router.post(
  '/balance-date',
  validator(cashPoolSchemas.getCashPoolTrailsSchema),
  cashPoolParticipantTrailController.getCashPoolParticipantTrailsBalanceAndDate,
);

export default router;
