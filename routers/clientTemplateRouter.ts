import express from 'express';

import clientTemplateController from '../controllers/clientTemplateController';
import { compression, multerMiddleware } from '../middlewares';
import { uploadClientTemplateFileSchema } from '../schemas/templateSchemas';
import validator from '../schemas/validator';

const router = express.Router();

router.get('/template/label', clientTemplateController.getAllClientTemplateFileLabels);
router.get('/standard-template/:label', compression, clientTemplateController.getStandardTemplateByLabel);
router.get('/template/:templateId', compression, clientTemplateController.getTemplateFile);
router.get('/template', clientTemplateController.getAllClientTemplateFiles);
router.post(
  '/template',
  multerMiddleware,
  validator(uploadClientTemplateFileSchema),
  clientTemplateController.uploadTemplateFile,
);
router.delete('/template/:templateId', clientTemplateController.deleteTemplateFile);

export default router;
