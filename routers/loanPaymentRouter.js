const express = require('express');
const router = express.Router({ mergeParams: true });

const loanPaymentController = require('../controllers/loanPaymentController');
const paymentSchemas = require('../schemas/paymentSchemas');
const validator = require('../schemas/validator');

router.get('/', loanPaymentController.getLoanPaymentsByLoanId);
router.get('/all', loanPaymentController.getPayments);
router.get('/wht/all', loanPaymentController.getWHTPayments);
router.get('/compound-payments', loanPaymentController.getBalloonCompoundPeriodPayments);
router.get('/next', loanPaymentController.getNextLoanPayments);
router.get('/:paymentId/interest-calculation-date', loanPaymentController.getInterestCalculationDate);
router.get('/export-excel', loanPaymentController.exportLoanPaymentsExcel);
router.post(
  '/calculate-payments',
  validator(paymentSchemas.calculatePaymentsForAnalyses),
  loanPaymentController.calculatePaymentsForAnalyses,
);
router.patch('/:paymentId', validator(paymentSchemas.markPaymentAsPaidSchema), loanPaymentController.markPaymentAsPaid);
router.patch(
  '/wht/:paymentId',
  validator(paymentSchemas.markPaymentAsPaidSchema),
  loanPaymentController.markWHTPaymentAsPaid,
);
router.post('/principal', validator(paymentSchemas.payPrincipalSchema), loanPaymentController.payPrincipal);

module.exports = router;
