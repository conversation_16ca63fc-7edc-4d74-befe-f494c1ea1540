import express from 'express';

import cuftDataFileController from '../controllers/cuftDataFileController';
import { compression, multerMiddleware } from '../middlewares';

const router = express.Router({ mergeParams: true });

router.get('/:cuftFileId', compression, cuftDataFileController.getCuftDataFile);
router.get('/', cuftDataFileController.getAllCuftDataFiles);
router.post('/', multerMiddleware, cuftDataFileController.uploadCuftDataFile);
router.delete('/:cuftFileId', cuftDataFileController.deleteCuftDataFile);

export default router;
