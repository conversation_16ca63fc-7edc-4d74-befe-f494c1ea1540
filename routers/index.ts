import azureStorageRouter from './azureStorageRouter';
import oauthRouter from './oauthRouter';
import userRouter from './userRouter';
import clientTemplateRouter from './clientTemplateRouter';
import clientRouter from './clientRouter';
import contactRouter from './contactRouter';
import setupDataRouter from './setupDataRouter';
import companyRouter from './companyRouter';
import cashPoolRouter from './cashPoolRouter';
import cuftDataRouter from './cuftDataRouter';
import featureRouter from './featureRouter';
import loanRouter from './loanRouter';
import b2bLoanRouter from './b2bLoanRouter';
import guaranteeRouter from './guaranteeRouter';
import creditRatingRouter from './creditRatingRouter';
import notificationRouter from './notificationRouter';
import whtRouter from './whtRouter';
import tableColumnRouter from './tableColumnRouter';

export {
  azureStorageRouter,
  cashPoolRouter,
  companyRouter,
  clientTemplateRouter,
  clientRouter,
  contactRouter,
  creditRatingRouter,
  cuftDataRouter,
  featureRouter,
  guaranteeRouter,
  notificationRouter,
  oauthRouter,
  userRouter,
  setupDataRouter,
  loanRouter,
  b2bLoanRouter,
  whtRouter,
  tableColumnRouter,
};
