const express = require('express');
const router = express.Router();

const loanController = require('../controllers/loanController');
const { rolesEnum, featureNames } = require('../enums');
const { features, permissions, compression } = require('../middlewares');
const loanSchemas = require('../schemas/loanSchemas');
const validator = require('../schemas/validator');
const loanPaymentRouter = require('./loanPaymentRouter');

router.get('/', loanController.getLoans);
router.get('/deleted', loanController.getDeletedLoans);
router.get('/:id', loanController.getLoan);
router.get('/template/import', compression, loanController.getImportTemplate);
router.get('/template/upload', compression, loanController.getUploadTemplate);
router.put('/imported/:id', validator(loanSchemas.importedLoanSchema), loanController.putImportedLoan);
router.put('/:id', validator(loanSchemas.loanSchema), loanController.putLoan);
router.put(
  '/:id/save-updated',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(loanSchemas.updateLoanWithUpdatedDataPoints),
  loanController.putLoanSaveUpdated,
);
router.put('/:id/note', validator(loanSchemas.updateLoanNoteSchema), loanController.putLoanNote);
router.put(
  '/:id/status',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(loanSchemas.updateLoanStatusSchema),
  loanController.putLoanStatus,
);
router.put(
  '/:id/portfolio',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(loanSchemas.updateLoanIsPortfolioSchema),
  loanController.putLoanIsPortfolio,
);
router.put('/:id/rates', validator(loanSchemas.updateLoanRates), loanController.putLoanRates);
router.post('/:id/tp-report', loanController.generateTpReport);
router.post('/', validator(loanSchemas.loanSchema), loanController.postLoan);
router.post('/mass', validator(loanSchemas.loansSchema), loanController.postLoans);
router.post('/import', validator(loanSchemas.importedLoanSchema), loanController.postImportedLoan);
router.post('/import/mass', validator(loanSchemas.importedLoansSchema), loanController.postImportedLoans);
router.post('/:id/agreement', loanController.generateAgreement);
router.post(
  '/:id/algorithm',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(loanSchemas.loanSchema),
  loanController.runAlgorithm,
);
router.delete('/:id', loanController.deleteLoan);
router.patch('/:id', loanController.restoreLoan);
router.use('/:id/payment', features(featureNames.PAYMENT), loanPaymentRouter);

module.exports = router;
