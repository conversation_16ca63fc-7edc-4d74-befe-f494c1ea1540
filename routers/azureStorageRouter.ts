import express from 'express';

import azureStorageController from '../controllers/azureStorageController';
import { compression, multerMiddleware } from '../middlewares';
import reportFileSchema from '../schemas/reportFileSchema';
import validator from '../schemas/validator';

const router = express.Router();

/** Used for loan, guarantee, credit rating and (soon) cash pool files */
router.get('/:reportType/file/:fileId', compression, azureStorageController.getFile);
router.post('/:reportType/:reportId', multerMiddleware, azureStorageController.uploadFile);
router.delete('/:reportType/:reportId/file/:fileId', azureStorageController.deleteFile);
router.put('/:reportType/file/:fileId', validator(reportFileSchema), azureStorageController.updateFileMetadataDb);

export default router;
