const express = require('express');
const router = express.Router();

const guaranteeController = require('../controllers/guaranteeController');
const { rolesEnum, featureNames } = require('../enums');
const { features, permissions, compression } = require('../middlewares');
const guaranteeSchemas = require('../schemas/guaranteeSchemas');
const validator = require('../schemas/validator');
const guaranteePaymentRouter = require('./guaranteePaymentRouter');

router.get('/', guaranteeController.getGuarantees);
router.get('/deleted', guaranteeController.getDeletedGuarantees);
router.get('/:id', guaranteeController.getGuarantee);
router.get('/template/import', compression, guaranteeController.getImportTemplate);
router.get('/template/upload', compression, guaranteeController.getUploadTemplate);
router.put('/:id', validator(guaranteeSchemas.guaranteeSchema), guaranteeController.putGuarantee);
router.put(
  '/:id/save-updated',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(guaranteeSchemas.updateGuaranteeWithUpdatedDataPoints),
  guaranteeController.putGuaranteeSaveUpdated,
);
router.put(
  '/imported/:id',
  validator(guaranteeSchemas.importedGuaranteeSchema),
  guaranteeController.putImportedGuarantee,
);
router.put('/:id/note', validator(guaranteeSchemas.updateGuaranteeNoteSchema), guaranteeController.putGuaranteeNote);
router.put(
  '/:id/status',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(guaranteeSchemas.updateGuaranteeStatusSchema),
  guaranteeController.putGuaranteeStatus,
);
router.put(
  '/:id/portfolio',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(guaranteeSchemas.updateGuaranteeIsPortfolioSchema),
  guaranteeController.putGuaranteeIsPortfolio,
);
router.put('/:id/rates', validator(guaranteeSchemas.updateGuaranteeRates), guaranteeController.putGuaranteeRates);
router.post('/:id/tp-report', guaranteeController.generateTpReport);
router.post('/', validator(guaranteeSchemas.guaranteeSchema), guaranteeController.postGuarantee);
router.post('/mass', validator(guaranteeSchemas.guaranteesSchema), guaranteeController.postGuarantees);
router.post('/import', validator(guaranteeSchemas.importedGuaranteeSchema), guaranteeController.postImportedGuarantee);
router.post(
  '/import/mass',
  validator(guaranteeSchemas.importedGuaranteesSchema),
  guaranteeController.postImportedGuarantees,
);
router.post('/:id/agreement', guaranteeController.generateAgreement);
router.post(
  '/:id/algorithm',
  permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN]),
  validator(guaranteeSchemas.guaranteeSchema),
  guaranteeController.runAlgorithm,
);
router.delete('/:id', guaranteeController.deleteGuarantee);
router.patch('/:id', guaranteeController.restoreGuarantee);
router.use('/:id/payment', features(featureNames.PAYMENT), guaranteePaymentRouter);

module.exports = router;
