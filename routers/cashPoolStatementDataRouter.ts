import express from 'express';

import cashPoolStatementDataController from '../controllers/cashPoolStatementDataController';
import * as statementDataSchemas from '../schemas/cashPoolSchemas/statementDataSchemas';
import zodValidator from '../schemas/zodValidator';
import { multerMiddleware } from '../middlewares';

const router = express.Router({ mergeParams: true });

router.get('/', cashPoolStatementDataController.getStatementData);
router.post(
  '/',
  zodValidator(statementDataSchemas.addStatementDataSchema),
  cashPoolStatementDataController.addStatementData,
);
router.get('/export', cashPoolStatementDataController.exportStatementData);
router.put(
  '/:id',
  zodValidator(statementDataSchemas.editStatementDataSchema),
  cashPoolStatementDataController.editStatementData,
);
router.delete('/:id', cashPoolStatementDataController.deleteStatementData);
router.post('/file-upload', multerMiddleware, cashPoolStatementDataController.uploadStatementDataFile);
router.get('/files', cashPoolStatementDataController.getStatementDataFiles);
router.delete('/files/:id', cashPoolStatementDataController.deleteStatementDataFile);
router.delete('/', cashPoolStatementDataController.massDeleteStatementData);
router.get('/file/:id', cashPoolStatementDataController.downloadStatementDataFile);

export default router;
