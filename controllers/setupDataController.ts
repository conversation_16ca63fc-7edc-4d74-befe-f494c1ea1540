import { Request, Response } from 'express';

import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { getCountries as getCountriesSingleton } from '../singletons';

async function getCountries(_: Request, res: Response): Promise<void> {
  const { countriesByName } = await getCountriesSingleton();

  res.json(countriesByName);
}

export default { getCountries: asyncControllerWrapper(getCountries) };
