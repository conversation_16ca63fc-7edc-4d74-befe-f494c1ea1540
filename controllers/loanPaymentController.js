const _ = require('lodash');
const moment = require('moment');
const { Op } = require('sequelize');

const { sequelize, BalloonPayment } = require('../models');
const loanRepository = require('../repositories/loanRepository');
const paymentRepository = require('../repositories/paymentRepository');
const whtPaymentRepository = require('../repositories/whtPaymentRepository');
const { getPayments: getSolverPayments } = require('../services');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { jsonToSheet } = require('../utils/documents');
const { BadRequestError, NotFoundError } = require('../utils/ErrorHandler');
const {
  getFilterBy,
  getOrderBy,
  getPaymentAfterPrincipalPayment,
  paymentFrequencyEnum,
  getRemainingPrincipalAmount,
  getInterestPerYear,
  getBalloonPaymentAmountAndInterestPayment,
  getBulletPaymentAmountAndInterestPayment,
  getFloatBalloonPartialPeriodPercentage,
  getLoanPaymentsSheetData,
  calculateWhtInterestRateBasedOnApproach,
} = require('../utils/payments');
const { checkIsImported } = require('../utils/reportUtils');

const orderByPaymentDueDate = [['paymentDueDate', 'ASC']];
const orderByPaymentId = [['paymentId', 'ASC']];
const orderByCompoundingPeriodEndDate = [
  [{ model: BalloonPayment, as: 'balloonPayment' }, 'compoundingPeriodEndDate', 'ASC'],
];

async function getLoanPaymentsByLoanId(req, res) {
  const { clientId } = req.user;
  const loanId = req.params.id;
  const { limit, isPaid } = req.query;

  const where = { loanId };
  if (isPaid) {
    where.isPaid = isPaid;
  }
  const loan = await loanRepository.getLoan(loanId, clientId);

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  const orderBy = loan.type === 'Bullet' ? orderByPaymentDueDate : orderByCompoundingPeriodEndDate;

  const allPaymentsOfLoan = await paymentRepository.getPayments({
    where: { loanId },
    loanFilter: { clientId },
    order: orderBy,
  });

  const totalNumberOfPayments = allPaymentsOfLoan.length;

  const loanPayments = await paymentRepository.getPayments({
    where,
    order: orderBy,
    loanFilter: { clientId },
    limit,
  });

  // ordinal not needed since paymentNumber is already passed, but needs changes on frontend to work without ordinal
  const loanPaymentsWithOrdinal = loanPayments.map((payment) => ({
    ...payment.dataValues,
    ordinal: payment.paymentNumber,
    totalNumberOfPayments,
  }));

  res.json(loanPaymentsWithOrdinal);
}

async function getBalloonCompoundPeriodPayments(req, res) {
  const { clientId } = req.user;
  const loanId = req.params.id;

  const loan = await loanRepository.getLoan(loanId, clientId);

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  const loanCompoundPayments = await paymentRepository.getPayments({
    where: { loanId },
    order: orderByCompoundingPeriodEndDate,
    loanFilter: { clientId },
  });
  const numberOfCompoundPayments = loanCompoundPayments.length;

  /**
   * Adds totalNumberOfPayments needed in frontend table on /payments/:id/compound-periods
   * Sets nextPaymentToBePaid for first unpaid payment
   * Sets lastPaidPayment for last paid which is the payment before the next
   * since these payments have to be made in order
   */
  let isNextPaymentToBePaid = true;
  for (let i = 0; i < numberOfCompoundPayments; i++) {
    loanCompoundPayments[i].dataValues.numberOfCompoundPayments = numberOfCompoundPayments;
    if (!loanCompoundPayments[i].dataValues.isPaid && isNextPaymentToBePaid) {
      loanCompoundPayments[i].dataValues.nextPaymentToBePaid = true;
      if (i > 0) loanCompoundPayments[i - 1].dataValues.lastPaidPayment = true;
      isNextPaymentToBePaid = false;
    }
  }

  res.json(loanCompoundPayments);
}

async function getPayments(req, res) {
  // same logic is used for exporting to excel (exportLoanPaymentsExcel)
  const { clientId } = req.user;
  const { offset = 0, limit = 10 } = req.query;
  const order = getOrderBy(req.query?.sort, 'loan') || orderByPaymentDueDate;
  const [paymentFilter, loanFilter] = getFilterBy(req.query, { isLoan: true });

  const totalNumberOfAllPayments = await paymentRepository.getTotalPaymentsCount({
    where: { loanId: { [Op.ne]: null }, paymentDueDate: { [Op.ne]: null }, ...paymentFilter },
    loanFilter: { ...loanFilter, status: 'Final', clientId },
  });

  const payments = await paymentRepository.getPayments({
    where: {
      paymentDueDate: { [Op.ne]: null },
      loanId: { [Op.ne]: null },
      ...paymentFilter,
    },
    loanFilter: { ...loanFilter, status: 'Final', clientId },
    order,
    limit,
    offset: offset * limit,
  });

  const loanIdsOfPayments = _.uniq(payments.map((payment) => payment.loanId));
  const totalCount = await paymentRepository.getPaymentsCount(
    { loanId: loanIdsOfPayments, paymentDueDate: { [Op.ne]: null } },
    'Payment.loanId',
  );
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of payments) {
      if (count.loanId === payment.loanId) {
        payment.totalNumberOfPayments = payment.balloonPayment ? 1 : count.totalNumberOfPayments;
      }
    }
  }

  return res.json({ payments, totalNumberOfPayments: totalNumberOfAllPayments });
}

async function getNextLoanPayments(req, res) {
  const { clientId } = req.user;
  const { limit = 3 } = req.query;

  /* payments only available for loans in portfolio */
  const loans = await loanRepository.getLoans({ where: { clientId, isPortfolio: true } });
  const loanIds = loans.map((loan) => loan.id);

  const loanPayments = await paymentRepository.getPayments({
    where: { loanId: loanIds, isPaid: false, paymentDueDate: { [Op.ne]: null } },
    order: orderByPaymentDueDate,
    loanFilter: { status: 'Final', clientId },
    limit,
  });

  const loanIdsOfPayments = loanPayments.map((loanPayment) => loanPayment.loanId);
  const totalCount = await paymentRepository.getPaymentsCount({ loanId: loanIdsOfPayments }, 'Payment.loanId');
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of loanPayments) {
      if (count.loanId === payment.loanId) {
        /* balloon payments always have only one payment */
        payment.totalNumberOfPayments = payment.balloonPayment ? 1 : count.totalNumberOfPayments;
        payment.paidPayments = count.paidPayments;
      }
    }
  }

  res.json(loanPayments);
}

async function calculatePaymentsForAnalyses(req, res) {
  const { id: loanId } = req.params;
  const { finalInterestRate, basisPoints, estimationOfReferenceRate, whtInterestRate, approach, isWhtEnabled } =
    req.body;
  const { clientId } = req.user;

  const loan = await loanRepository.getLoan(loanId, clientId, null);

  if (!loan) {
    throw new NotFoundError('Loan');
  }
  if (checkIsImported(loan.report)) {
    throw new BadRequestError('Loan must not be imported');
  }
  if (loan.rateType.type === 'fixed' && !finalInterestRate) {
    throw new BadRequestError('For fixed payments Interest Rate has to be provided');
  }
  if (loan.rateType.type === 'float' && basisPoints === 0 && estimationOfReferenceRate === 0) {
    throw new BadRequestError(
      'For float payments either Basis Points or Estimation of Reference Rate have to be provided',
    );
  }

  const [totalInterest, payments] = await getSolverPayments({
    report: loan,
    finalInterestRate,
    reportIdKey: 'loanId',
    basisPoints,
    estimationOfReferenceRate,
  });

  const interestPerYear = {};
  payments.forEach((payment) => {
    const { year, interest } = getInterestPerYear({ payment, type: loan.type });
    if (!interestPerYear[year]) interestPerYear[year] = interest;
    else interestPerYear[year] = interestPerYear[year] + interest;
  });

  const principalRepayment = payments[payments.length - 1].paymentAmount;

  if (isWhtEnabled) {
    const [whtInterestPerYear, newInterestPerYearDependentOnWht] = calculateWhtInterestRateBasedOnApproach(
      approach,
      whtInterestRate,
      interestPerYear,
    );

    res.json({
      totalInterest,
      interestPerYear: newInterestPerYearDependentOnWht,
      whtInterestPerYear,
      principalRepayment,
      isWhtEnabled,
    });
  } else {
    res.json({
      totalInterest,
      interestPerYear,
      whtInterestPerYear: null,
      principalRepayment,
      isWhtEnabled,
    });
  }
}

async function markPaymentAsPaid(req, res) {
  const { clientId } = req.user;
  const { id: loanId, paymentId } = req.params;
  const { isPaid, estimationOfReferenceRate, interestCalculationDate } = req.body;

  await sequelize.transaction(async () => {
    const loan = await loanRepository.getLoan(loanId, clientId);

    if (!loan) {
      throw new NotFoundError('Loan');
    }

    const paymentToBeMarked = await paymentRepository.getPayment({ where: { id: paymentId }, type: loan.type });
    const isFirst = paymentToBeMarked.paymentNumber === 1;
    const previousPayment = await paymentRepository.getPayment({
      where: { loanId, paymentNumber: paymentToBeMarked.paymentNumber - 1 },
      type: loan.type,
    });
    if (!isFirst && !previousPayment.isPaid) {
      throw new BadRequestError('Previous payment needs to be marked as paid first');
    }

    if (loan.rateType.type === 'fixed') {
      const [affectedRows, updatedLoanPayment] = await paymentRepository.updatePayment(paymentId, { isPaid }, true);

      if (affectedRows === 0) {
        throw new NotFoundError('Loan payment');
      }

      return res.json(updatedLoanPayment);
    }

    /**
     * For float rate types the interest rate is unknown until the payment is made.
     * We get it from basis points and the reference rate (estimationOfReferenceRate).
     * For bullet rate type we calculate the actual interest payment with getPaymentAmountAndInterestPayment.
     * We then update the BulletPayments (interestPayment) and Payments (paymentAmount) tables.
     * interestPayment and paymentAmount are the same for all payments except the last.
     * Last paymentAmount is the interestPayment + principal amount.
     * Also on every call we update the totalInterest inside the Loans table.
     * On marking as unpaid everything is set to null and the interestPayment is subtracted from totalInterest
     */
    if (loan.rateType.type === 'float' && loan.type === 'Bullet') {
      /* MARKING AS UNPAID */
      if (!isPaid) {
        const updatePayment = paymentRepository.updatePayment(paymentId, {
          isPaid,
          paymentAmount: null,
          referenceRate: null,
          interestCalculationDate: null,
        });
        const updateSpecificPayment = paymentRepository.updateSpecificPayment({
          type: loan.type,
          paymentId,
          attributesToUpdate: { interestPayment: null },
        });
        const newTotalInterest = loan.totalInterest - paymentToBeMarked.bulletPayment.interestPayment;
        const updateLoan = loanRepository.updateLoanWithoutUpdatedBy({
          id: loanId,
          attributesToUpdate: { totalInterest: newTotalInterest },
        });
        const allPromises = [updatePayment, updateSpecificPayment, updateLoan];
        if (loan.report.isWhtEnabled) {
          const whtPayment = await whtPaymentRepository.getWHTPayment({ paymentId });
          const updateWHTPayment = whtPaymentRepository.updatePayment(
            whtPayment.id,
            { paymentAmount: null, isPaid },
            false,
          );
          allPromises.push(updateWHTPayment);
        }
        await Promise.all(allPromises);
        return res.status(204).send();
      }

      /* MARKING AS PAID */
      const numberOfPayments = await paymentRepository.getTotalPaymentsCount({
        where: { loanId },
        loanFilter: { clientId },
      });

      /* Previous payment could have been Principal payment which means this payment must be calculated with that in mind
      We need to take into account that the principal amount has changed between previous payment and this one
      Consider the following example with principal amount being 100000. The previous payment was on 1.1.2022. and the next payment is 1.6.2022.
      The user then makes a principal payment of 20000 on 1.5.2022. That means the payment amount is calculated with principal amount being full amount
      up until 1.5. and full amount - principal paid (80000) from 1.5. to 1.6.
      This is same as partialPayment in payPrincipal, but in that case we know in advance what the payment amount should be
      and here we have to check if the previous was a principal payment and act accordingly */
      const secondPreviousPayment = await paymentRepository.getPayment({
        where: { loanId, paymentNumber: paymentToBeMarked.paymentNumber - 2 },
        type: loan.type,
      });

      const previousPrincipalPayments = await paymentRepository.getPayments({
        where: { loanId, isPrincipalPayment: true, paymentDueDate: { [Op.lt]: paymentToBeMarked.paymentDueDate } },
        loanFilter: { clientId },
      });

      const isLastPayment = paymentToBeMarked.paymentNumber === numberOfPayments;
      const remainingPrincipalAmount = getRemainingPrincipalAmount(loan.amount, previousPrincipalPayments);
      const [paymentAmount, interestPayment, whtPaymentAmount] = getBulletPaymentAmountAndInterestPayment({
        isPreviousPaymentPrincipalPayment: previousPayment?.isPrincipalPayment,
        loan,
        estimationOfReferenceRate,
        remainingPrincipalAmount,
        previousPayment,
        secondPreviousPayment,
        paymentToBeMarked,
        isLastPayment,
      });

      const newTotalInterest = Number(loan.totalInterest || 0) + interestPayment;
      const updateLoan = loanRepository.updateLoanWithoutUpdatedBy({
        id: loanId,
        attributesToUpdate: { totalInterest: newTotalInterest },
      });

      const updatePayment = paymentRepository.updatePayment(paymentId, {
        isPaid,
        paymentAmount,
        referenceRate: estimationOfReferenceRate,
        interestCalculationDate,
      });

      const updateSpecificPayment = paymentRepository.updateSpecificPayment({
        type: loan.type,
        paymentId,
        attributesToUpdate: { interestPayment },
      });

      const allPromises = [updateLoan, updatePayment, updateSpecificPayment];

      if (loan.report.isWhtEnabled) {
        const whtPayment = await whtPaymentRepository.getWHTPayment({ paymentId });
        const updateWHTPayment = whtPaymentRepository.updatePayment(
          whtPayment.id,
          { paymentAmount: whtPaymentAmount },
          false,
        );
        allPromises.push(updateWHTPayment);
      }

      await Promise.all(allPromises);

      return res.status(204).send();
    }

    /**
     * Very similar to bullet float, except the next interest depends on the previous because it compounds. So we have to get
     * the previous compoundedInterest (it's zero if it's the first payment) and add to it additionalInterest which we calculate
     * to get the current compoundedInterest. additionalInterest in balloon can be compared to the interestPayment in bullet types,
     * the difference being in bullet the interestRate is always multiplied with principal amount, while in balloon the compounded
     * interest is added to the principal and then multiplied by interestRate.
     */
    if (loan.rateType.type === 'float' && loan.type === 'Balloon') {
      const previousPayment = await paymentRepository.getPayment({
        where: { loanId, paymentNumber: paymentToBeMarked.paymentNumber - 1 },
        type: loan.type,
      });
      const previousPaymentCompoundedInterest = Number(previousPayment?.balloonPayment?.compoundedInterest || 0); // Zero if it's the first payment because there is no compoundedInterest yet

      const numberOfPayments = await paymentRepository.getTotalPaymentsCount({
        where: { loanId },
        loanFilter: { clientId },
      });
      const isLastPayment = paymentToBeMarked.paymentNumber === numberOfPayments;
      /* MARKING AS UNPAID */
      if (!isPaid) {
        const updatePayment = paymentRepository.updatePayment(paymentId, {
          isPaid,
          paymentAmount: 0,
          referenceRate: null,
          interestCalculationDate: null,
        });
        const updateSpecificPayment = paymentRepository.updateSpecificPayment({
          type: loan.type,
          paymentId,
          attributesToUpdate: { additionalInterest: null, compoundedInterest: null },
        });
        const updateLoan = loanRepository.updateLoanWithoutUpdatedBy({
          id: loanId,
          attributesToUpdate: { totalInterest: previousPaymentCompoundedInterest },
        });
        const allPromises = [updatePayment, updateSpecificPayment, updateLoan];
        if (loan.report.isWhtEnabled && isLastPayment) {
          const whtPayment = await whtPaymentRepository.getWHTPayment({ paymentId });
          const updateWHTPayment = whtPaymentRepository.updatePayment(
            whtPayment.id,
            { paymentAmount: null, isPaid },
            false,
          );
          allPromises.push(updateWHTPayment);
        }
        await Promise.all(allPromises);
        return res.status(204).send();
      }

      /* MARKING AS PAID */
      const previousPaymentCompoundingPeriodEndDate = previousPayment?.balloonPayment.compoundingPeriodEndDate;
      const paymentToBeMarkedCompoundingPeriodEndDate = paymentToBeMarked?.balloonPayment.compoundingPeriodEndDate;

      // Last payment could be in the middle of period so it's necessary
      // to calculate interest with only that percentage of the period
      const percentageOfPeriod = getFloatBalloonPartialPeriodPercentage(
        isLastPayment,
        previousPaymentCompoundingPeriodEndDate,
        paymentToBeMarkedCompoundingPeriodEndDate,
        loan.paymentFrequency,
      );

      const [paymentAmount, additionalInterest, compoundedInterest, whtPaymentAmount] =
        getBalloonPaymentAmountAndInterestPayment({
          loan,
          estimationOfReferenceRate,
          isLastPayment,
          previousPaymentCompoundedInterest,
          percentageOfPeriod,
          rates: loan.report,
        });

      const updateLoan = loanRepository.updateLoanWithoutUpdatedBy({
        id: loanId,
        attributesToUpdate: { totalInterest: compoundedInterest },
      });

      const updatePayment = paymentRepository.updatePayment(paymentId, {
        isPaid,
        paymentAmount,
        referenceRate: estimationOfReferenceRate,
        interestCalculationDate,
      });

      const updateSpecificPayment = paymentRepository.updateSpecificPayment({
        type: loan.type,
        paymentId,
        attributesToUpdate: { additionalInterest, compoundedInterest },
      });

      const allPromises = [updateLoan, updatePayment, updateSpecificPayment];

      if (loan.report.isWhtEnabled && isLastPayment) {
        const whtPayment = await whtPaymentRepository.getWHTPayment({ paymentId });
        const updateWHTPayment = whtPaymentRepository.updatePayment(
          whtPayment.id,
          { paymentAmount: whtPaymentAmount },
          false,
        );
        allPromises.push(updateWHTPayment);
      }

      await Promise.all(allPromises);

      return res.status(204).send();
    }
  });
}

async function markWHTPaymentAsPaid(req, res) {
  const { clientId } = req.user;
  const { id: loanId, paymentId } = req.params;
  const { isPaid } = req.body;

  await sequelize.transaction(async () => {
    const loan = await loanRepository.getLoan(loanId, clientId);

    if (!loan) {
      throw new NotFoundError('Loan');
    }

    if (isPaid) {
      const whtPaymentToBeMarked = await whtPaymentRepository.getWHTPayment({ id: paymentId });
      if (!whtPaymentToBeMarked.paymentAmount)
        throw new BadRequestError('Trying to mark payment that does not have payment amount');
    }

    const [affectedRows, updatedWHTPayment] = await whtPaymentRepository.updatePayment(paymentId, { isPaid }, true);

    if (affectedRows === 0) {
      throw new NotFoundError('Loan payment');
    }

    return res.json(updatedWHTPayment);
  });
}

async function payPrincipal(req, res) {
  const { paymentAmount, paymentDate } = req.body;
  const { clientId } = req.user;
  const { id: loanId } = req.params;

  await sequelize.transaction(async () => {
    const { dataValues: loan } = await loanRepository.getLoan(loanId, clientId);

    if (!loan) {
      throw new NotFoundError('Loan');
    }
    if (!loan.isPortfolio || loan.status === 'Draft') {
      throw new BadRequestError('Loan has to be in portfolio and finalized');
    }

    const { amount: loanPrincipal, report, paymentFrequency, type, issueDate, maturityDate } = loan;

    if (moment(new Date(paymentDate)).isBefore(moment(new Date(issueDate)))) {
      throw new BadRequestError('Principal payment cannot be before the issue date');
    }

    if (moment(new Date(paymentDate)).isSameOrAfter(moment(new Date(maturityDate)))) {
      throw new BadRequestError('Principal payment cannot be after the maturity date');
    }

    const [mostRecentPaidPayment] = await paymentRepository.getPayments({
      where: { loanId, isPaid: true },
      order: [['paymentDueDate', 'DESC']],
      loanFilter: { clientId },
      limit: 1,
    });

    if (moment(new Date(paymentDate)).isBefore(moment(new Date(mostRecentPaidPayment?.paymentDueDate)))) {
      throw new BadRequestError('Cannot pay principal with payment date earlier than last paid payment');
    }

    const interestRatePerInterestRepaymentFrequency =
      report.finalInterestRate / 100 / paymentFrequencyEnum[paymentFrequency];

    const [[paymentAfterPrincipalPayment], previousPrincipalPayments] = await Promise.all([
      paymentRepository.getPayments({
        where: { loanId, paymentDueDate: { [Op.gt]: new Date(paymentDate) } },
        order: orderByPaymentDueDate,
        loanFilter: { clientId },
        limit: 1,
      }),
      paymentRepository.getPayments({
        where: { loanId, isPrincipalPayment: true },
        loanFilter: { clientId },
      }),
    ]);

    const nextPaymentNumber = paymentAfterPrincipalPayment?.paymentNumber;
    const nextPaymentDueDate = paymentAfterPrincipalPayment?.paymentDueDate;

    const [lastPaidPayment] = await paymentRepository.getPayments({
      where: { loanId, paymentNumber: nextPaymentNumber - 1 },
      loanFilter: { clientId },
    });

    const paymentAmountOfPartialPayment = getPaymentAfterPrincipalPayment(
      {
        amount: getRemainingPrincipalAmount(loanPrincipal, previousPrincipalPayments),
        interestRate: interestRatePerInterestRepaymentFrequency,
        paymentFrequency,
        issueDate,
      },
      paymentAmount,
      paymentDate,
      lastPaidPayment,
      nextPaymentDueDate,
      true,
    );

    const principalPayment = {
      loanId,
      interestRatePerInterestRepaymentFrequency,
      interestPayment: 0,
      isPaid: true,
      paymentDueDate: paymentDate,
      paymentAmount,
      paymentNumber: nextPaymentNumber,
      isPrincipalPayment: true,
    };

    const remainingPrincipal = getRemainingPrincipalAmount(loanPrincipal, previousPrincipalPayments, paymentAmount);

    if (remainingPrincipal < 0) {
      throw new BadRequestError('You cannot overpay the principal amount');
    }

    const [[_totalInterest, newPayments]] = await Promise.all([
      getSolverPayments({
        report: { ...loan, amount: remainingPrincipal },
        finalInterestRate: report.finalInterestRate,
        reportIdKey: 'loanId',
        basisPoints: report.finalInterestRate,
        estimationOfReferenceRate: null,
      }),
      paymentRepository.deletePayments({ loanId, paymentDueDate: { [Op.gt]: new Date(paymentDate) } }),
    ]);

    const partialPayment = {
      loanId,
      interestRatePerInterestRepaymentFrequency,
      interestPayment: loan.rateType?.type === 'float' ? null : paymentAmountOfPartialPayment,
      isPaid: false,
      paymentDueDate: nextPaymentDueDate,
      paymentAmount: loan.rateType?.type === 'float' ? null : paymentAmountOfPartialPayment,
      paymentNumber: nextPaymentNumber + 1,
    };

    const updatedNewPayments = newPayments
      .filter(
        (payment) =>
          moment(payment.paymentDueDate).isAfter(nextPaymentDueDate) &&
          (payment.interestPayment > 0 || payment.interestPayment === null),
      )
      .map((payment, index) => ({ ...payment, paymentNumber: nextPaymentNumber + 2 + index }));

    if (paymentAmountOfPartialPayment > 0) {
      await paymentRepository.createPayments([principalPayment, partialPayment, ...updatedNewPayments], type);
    } else {
      await paymentRepository.createPayments([principalPayment, ...updatedNewPayments], type);
    }

    const allPayments = await paymentRepository.getPayments({
      where: { loanId },
      order: orderByPaymentDueDate,
      loanFilter: { clientId },
    });

    const newTotalInterest = allPayments.reduce((prev, curr) => prev + Number(curr.bulletPayment.interestPayment), 0);

    await loanRepository.updateLoan(loanId, { totalInterest: newTotalInterest }, req.user);

    res.json();
  });
}

async function getInterestCalculationDate(req, res) {
  const { clientId } = req.user;
  const { id: loanId, paymentId } = req.params;

  const [payment] = await paymentRepository.getPayments({
    where: { loanId, id: { [Op.lt]: paymentId }, isPrincipalPayment: false },
    loanFilter: { clientId },
    order: [['id', 'DESC']],
    limit: 1,
  });

  if (payment.balloonPayment) {
    const interestCalculationDate = moment(payment.balloonPayment.compoundingPeriodEndDate).add(1, 'days');
    return res.json(interestCalculationDate);
  }

  const interestCalculationDate = moment(payment.paymentDueDate).add(1, 'days');
  res.json(interestCalculationDate);
}

async function exportLoanPaymentsExcel(req, res) {
  // same logic as in getPayments
  const { clientId } = req.user;
  const { columns } = req.query;
  const order = getOrderBy(req.query?.sort, 'loan') || orderByPaymentDueDate;
  const [paymentFilter, loanFilter] = getFilterBy(req.query, { isLoan: true });

  const payments = await paymentRepository.getPayments({
    where: {
      paymentDueDate: { [Op.ne]: null },
      loanId: { [Op.ne]: null },
      ...paymentFilter,
    },
    loanFilter: { ...loanFilter, status: 'Final', clientId },
    order,
  });

  const loanIdsOfPayments = _.uniq(payments.map((payment) => payment.loanId));
  const totalCount = await paymentRepository.getPaymentsCount(
    { loanId: loanIdsOfPayments, paymentDueDate: { [Op.ne]: null } },
    'Payment.loanId',
  );
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of payments) {
      if (count.loanId === payment.loanId) {
        payment.totalNumberOfPayments = payment.balloonPayment ? 1 : count.totalNumberOfPayments;
      }
    }
  }
  const sheetData = getLoanPaymentsSheetData(payments, columns.split(', '));
  const data = [{ sheetData, sheetName: 'Loan interest' }];
  const result = jsonToSheet(data);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Loan interest.xlsx"');
  res.send(result);
}

async function getWHTPayments(req, res) {
  const { clientId } = req.user;
  const { offset = 0, limit = 10 } = req.query;
  const order = getOrderBy(req.query?.sort, 'wht') || orderByPaymentId;
  const [paymentFilter, loanFilter] = getFilterBy(req.query, { isLoan: true, arePaymentsWht: true });

  await sequelize.transaction(async () => {
    const totalWHTPaymentsCount = await whtPaymentRepository.getTotalPaymentsCount({
      where: { loanId: { [Op.ne]: null }, ...paymentFilter },
      loanFilter: { ...loanFilter, status: 'Final', clientId },
    });

    const payments = await whtPaymentRepository.getWHTPayments({
      where: {
        loanId: { [Op.ne]: null },
        ...paymentFilter,
      },
      order,
      limit,
      offset: offset * limit,
      loanFilter: { ...loanFilter, status: 'Final', clientId },
    });

    const loanIdsOfPayments = _.uniq(payments.map((payment) => payment.loanId));
    const paymentsCount = await whtPaymentRepository.getPaymentsCount(
      { loanId: loanIdsOfPayments },
      'WHTPayment.loanId',
    );
    for (const { dataValues: count } of paymentsCount) {
      for (const { dataValues: payment } of payments) {
        if (count.loanId === payment.loanId) {
          payment.totalNumberOfPayments = count.totalNumberOfPayments;
        }
      }
    }

    return res.json({ payments, totalNumberOfPayments: totalWHTPaymentsCount });
  });
}

module.exports = {
  getLoanPaymentsByLoanId: asyncControllerWrapper(getLoanPaymentsByLoanId),
  getBalloonCompoundPeriodPayments: asyncControllerWrapper(getBalloonCompoundPeriodPayments),
  getPayments: asyncControllerWrapper(getPayments),
  getNextLoanPayments: asyncControllerWrapper(getNextLoanPayments),
  calculatePaymentsForAnalyses: asyncControllerWrapper(calculatePaymentsForAnalyses),
  markPaymentAsPaid: asyncControllerWrapper(markPaymentAsPaid),
  payPrincipal: asyncControllerWrapper(payPrincipal),
  getInterestCalculationDate: asyncControllerWrapper(getInterestCalculationDate),
  exportLoanPaymentsExcel: asyncControllerWrapper(exportLoanPaymentsExcel),
  getWHTPayments: asyncControllerWrapper(getWHTPayments),
  markWHTPaymentAsPaid: asyncControllerWrapper(markWHTPaymentAsPaid),
};
