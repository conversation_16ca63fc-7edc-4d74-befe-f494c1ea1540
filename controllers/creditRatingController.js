const { Buffer } = require('buffer');
const { Op } = require('sequelize');

const { featureNames, rolesEnum } = require('../enums');
const { sequelize } = require('../models');
const { creditRatingRepository, featureRepository } = require('../repositories');
const creditRatingService = require('../services/creditRatingService');
const azureService = require('../services/azureService');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { initializeGeneratedFileData } = require('../utils/creditRatingUtils');
const {
  ForbiddenError,
  NotFoundError,
  FinalizeUneditableError,
  MarkAsDraftUneditableError,
  UpdateFinalizedError,
} = require('../utils/ErrorHandler');
const { checkCreateLimits } = require('../utils/featureUtils');
const { runReportUpdateGuards } = require('../utils/reportUtils');

const orderByCreationDate = [['createdAt', 'DESC']];
const orderByDeleteDate = [['deletedAt', 'DESC']];

async function getCreditRatings(req, res) {
  const { limit, isPortfolio } = req.query;
  const creditRatings = await creditRatingRepository.getCreditRatings({
    where: {
      clientId: req.user.clientId,
      isPortfolio: isPortfolio === 'true',
    },
    order: orderByCreationDate,
    limit: isNaN(limit) ? null : parseInt(limit),
  });
  res.json(creditRatings);
}

async function getDeletedCreditRatings(req, res) {
  const creditRatings = await creditRatingRepository.getCreditRatings({
    where: { clientId: req.user.clientId, deletedAt: { [Op.ne]: null } },
    order: orderByDeleteDate,
    paranoid: false,
  });
  res.json(creditRatings);
}

async function getCreditRating(req, res) {
  const result = await creditRatingRepository.getCreditRating(req.params.id, req.user.clientId);

  if (!result) {
    throw new NotFoundError('Credit rating');
  }

  res.json(result);
}

async function deleteCreditRating(req, res) {
  const { role, clientId } = req.user;
  const force = req.query.force === 'true';
  const creditRatingId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const creditRating = await creditRatingRepository.getCreditRating(creditRatingId, req.user.clientId);

  if (!creditRating) throw new NotFoundError('Credit rating');
  const feature = await featureRepository.getClientFeatureByName({
    clientId,
    featureName: featureNames.CREDIT_RATING_NUMBER,
  });
  if (creditRating.isPortfolio && isUser) throw new ForbiddenError('Only admins can delete credit rating in portfolio');
  if (force && isUser) throw new ForbiddenError('Only admins can permanently delete credit ratings');
  if (feature.isEnabled && force) throw new ForbiddenError('Cannot permanently delete credit rating.');

  await sequelize.transaction(async () => {
    if (force) await Promise.all(creditRating.files.map((file) => azureService.deleteFile(`creditRating/${file.id}`)));

    await creditRatingRepository.deleteCreditRating(creditRatingId, clientId, force);
  });

  res.status(204).send();
}

async function restoreCreditRating(req, res) {
  const { role, clientId } = req.user;
  const creditRatingId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const creditRating = await creditRatingRepository.getCreditRating(creditRatingId, clientId);

  if (!creditRating) throw new NotFoundError('Credit Rating');
  if (isUser) throw new ForbiddenError('Only admins can restore a credit rating');

  await creditRatingRepository.restoreCreditRating(creditRatingId, clientId);

  res.status(204).send();
}

async function getTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.download('./static/credit_rating_template.xlsx');
}

async function postCreditRating(req, res) {
  const { clientId } = req.user;
  const creditRating = req.body;

  await sequelize.transaction(async () => {
    await checkCreateLimits({ clientId, featureName: featureNames.CREDIT_RATING_NUMBER, reportType: 'creditRating' });

    const createdCreditRating = await creditRatingService.createCreditRating(creditRating, req.user);
    res.json(createdCreditRating);
  });
}

async function postCreditRatings(req, res) {
  const { clientId } = req.user;
  const creditRatings = req.body;

  await sequelize.transaction(async () => {
    await checkCreateLimits({
      clientId,
      featureName: featureNames.CREDIT_RATING_NUMBER,
      reportType: 'creditRating',
      incrementCount: creditRatings.length,
    });

    const createdCreditRatings = await Promise.all(
      creditRatings.map((creditRating) => creditRatingService.createCreditRating(creditRating, req.user)),
    );
    res.json(createdCreditRatings);
  });
}

async function postImportedCreditRating(req, res) {
  const creditRating = req.body;

  creditRating.attributes = null;
  creditRating.files = [];

  res.json(await creditRatingRepository.createCreditRating(creditRating, req.user));
}

async function postImportedCreditRatings(req, res) {
  const creditRatings = req.body;

  await sequelize.transaction(async () => {
    const createCreditRatingPromises = creditRatings.map((creditRating) => {
      creditRating.attributes = null;
      creditRating.files = [];
      return creditRatingRepository.createCreditRating(creditRating, req.user);
    });

    await Promise.all(createCreditRatingPromises);
    res.status(204).send();
  });
}

async function putCreditRating(req, res) {
  const { id } = req.params;
  const { clientId } = req.user;
  const creditRating = req.body;

  const oldCreditRating = await creditRatingRepository.getCreditRating(id, clientId);

  runReportUpdateGuards(oldCreditRating, 'credit rating');

  const { rating, probabilityOfDefault, pdf } = await creditRatingService.createCreditRatingReport(
    creditRating,
    clientId,
  );
  creditRating.creditRating = { rating };
  creditRating.probabilityOfDefault = probabilityOfDefault;
  creditRating.files = [initializeGeneratedFileData(creditRating.company, pdf)];

  await sequelize.transaction(async () => {
    const result = await creditRatingRepository.updateCreditRatingAndFile(id, creditRating, req.user);

    await azureService.uploadFile(`creditRating/${result.fileId}`, pdf, Buffer.byteLength(pdf));
    res.status(204).send();
  });
}

async function putImportedCreditRating(req, res) {
  const id = req.params.id;
  const creditRating = req.body;

  const oldCreditRating = await creditRatingRepository.getCreditRating(id, req.user.clientId);

  runReportUpdateGuards(oldCreditRating, 'credit rating');

  res.json(await creditRatingRepository.updateCreditRating(id, creditRating, req.user));
}

async function putCreditRatingStatus(req, res) {
  const id = req.params.id;
  const { status } = req.body;

  const creditRating = (await creditRatingRepository.getCreditRating(id, req.user.clientId))?.dataValues;

  if (!creditRating) {
    throw new NotFoundError('Credit rating');
  }

  if (!creditRating.editable) {
    if (status === 'Draft') {
      throw new MarkAsDraftUneditableError('credit rating');
    }
    throw new FinalizeUneditableError('credit rating');
  }

  creditRating.status = status;
  if (status === 'Final') {
    creditRating.finalizedBy = req.user?.username;
  } else {
    creditRating.finalizedBy = null;
  }

  res.json(await creditRatingRepository.updateCreditRating(id, creditRating, req.user));
}

async function putCreditRatingNote(req, res) {
  const id = req.params.id;
  const { note } = req.body;

  const creditRating = (await creditRatingRepository.getCreditRating(id, req.user.clientId))?.dataValues;

  if (!creditRating) {
    throw new NotFoundError('Credit rating');
  }

  if (creditRating.status === 'Final') {
    throw new UpdateFinalizedError('credit rating');
  }

  creditRating.note = note;
  res.json(await creditRatingRepository.updateCreditRating(id, creditRating, req.user));
}

async function putCreditRatingIsPortfolio(req, res) {
  const id = req.params.id;
  const { isPortfolio } = req.body;

  const creditRating = (await creditRatingRepository.getCreditRating(id, req.user.clientId))?.dataValues;

  runReportUpdateGuards(creditRating, 'credit rating');

  creditRating.isPortfolio = isPortfolio;
  await creditRatingRepository.updateCreditRating(id, creditRating, req.user);
  res.status(204).send();
}

function getImportTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.ms-excel');
  res.download('./static/credit_rating_import_template.xlsx');
}

module.exports = {
  getCreditRatings: asyncControllerWrapper(getCreditRatings),
  getDeletedCreditRatings: asyncControllerWrapper(getDeletedCreditRatings),
  getCreditRating: asyncControllerWrapper(getCreditRating),
  getImportTemplate: asyncControllerWrapper(getImportTemplate),
  deleteCreditRating: asyncControllerWrapper(deleteCreditRating),
  restoreCreditRating: asyncControllerWrapper(restoreCreditRating),
  getTemplate: asyncControllerWrapper(getTemplate),
  postCreditRating: asyncControllerWrapper(postCreditRating),
  postCreditRatings: asyncControllerWrapper(postCreditRatings),
  postImportedCreditRating: asyncControllerWrapper(postImportedCreditRating),
  postImportedCreditRatings: asyncControllerWrapper(postImportedCreditRatings),
  putCreditRating: asyncControllerWrapper(putCreditRating),
  putImportedCreditRating: asyncControllerWrapper(putImportedCreditRating),
  putCreditRatingStatus: asyncControllerWrapper(putCreditRatingStatus),
  putCreditRatingNote: asyncControllerWrapper(putCreditRatingNote),
  putCreditRatingIsPortfolio: asyncControllerWrapper(putCreditRatingIsPortfolio),
};
