import { Request, Response } from 'express';

import azureAuth from '../auth/azureAuth';
import { getAccessTokenData, generateAccessToken, generateRefreshToken } from '../auth/authUtils';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { failedLoginAttemptRepository, userRepository } from '../repositories';
import { BadRequestError } from '../utils/ErrorHandler';

async function azureLogin(req: Request, res: Response): Promise<void> {
  const requestUrl = req.get('X-Request-Origin');

  const credential = await azureAuth(req.body.code, requestUrl);

  const user = await userRepository.getUserBySocialProvider({ credential });

  if (!user) {
    console.log({ msg: `no user: ${credential}`, date: new Date() });
    await failedLoginAttemptRepository.createFailedLoginAttempt({ email: credential, username: null });
    throw new BadRequestError('Unable to login with these credentials.');
  }

  const refreshToken = await generateRefreshToken(getAccessTokenData(user));
  const accessToken = await generateAccessToken(getAccessTokenData(user));

  res.cookie('refresh-token', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'development' ? false : true,
    sameSite: 'strict',
    maxAge: 5 * 24 * 60 * 60 * 1000,
  });

  res.json({ accessToken });
}

export default { azureLogin: asyncControllerWrapper(azureLogin) };
