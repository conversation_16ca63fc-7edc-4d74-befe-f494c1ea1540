import { Request, Response } from 'express';

import { cashPoolRepository, topCurrencyAccountRepository } from '../repositories';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { NotFoundError } from '../utils/ErrorHandler';

async function getCashPoolTopCurrencyAccounts(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const topCurrencyAccounts = await topCurrencyAccountRepository.getCashPoolTopCurrencyAccounts({ cashPoolId });
  return res.json(topCurrencyAccounts);
}

export default {
  getCashPoolTopCurrencyAccounts: asyncControllerWrapper(getCashPoolTopCurrencyAccounts),
};
