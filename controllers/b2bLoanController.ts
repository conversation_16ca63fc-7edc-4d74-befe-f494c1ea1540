import { Request, Response } from 'express';
import { Op, literal } from 'sequelize';
import _ from 'lodash';

import models from '../models';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import {
  equityRiskPremiumRepository,
  featureRepository,
  regionalEquityRiskPremiumRepository,
  betaRepository,
  b2bLoanRepository,
  b2bLoanLegRepository,
  b2bLoanFileRepository,
} from '../repositories';
import { rolesEnum, featureNames } from '../enums';
import * as repaymentService from '../services/repaymentService';
import { getCountries } from '../singletons';
import { betaUtils } from '../utils/betaUtils';
import * as numberUtils from '../utils/numbers';
import * as reportUtils from '../utils/reportUtils';
import paymentUtils from '../utils/payments';
import * as featureUtils from '../utils/featureUtils';
import * as loanUtils from '../utils/loanUtils';
import * as b2bLoanUtils from '../utils/b2bLoanUtils';
import * as azureService from '../services/azureService';
import {
  BadRequestError,
  ForbiddenError,
  NotFoundError,
  MarkAsDraftUneditableError,
  FinalizeUneditableError,
} from '../utils/ErrorHandler';
import { prepareB2BLoanForCreation } from '../utils/loanUtils/prepareLoanForCreation';
import { calculateLoanDataAlgorithm } from './algorithms/reportAlgorithms';
import { B2BLoanLegType, EmbeddedCompanyType } from '../types';

const { sequelize } = models;
const orderByCreationDate = [['createdAt', 'DESC']];
const orderByDeleteDate = [['deletedAt', 'DESC']];

async function getEquityRiskPremium(req: Request, res: Response) {
  const { countriesByName } = await getCountries();
  const { countryName, issueDate } = req.body;
  const date = new Date(issueDate);

  const country = countriesByName[countryName];
  if (!country) {
    throw new BadRequestError(`Country ${countryName} not found`);
  }
  const { id: countryId, region } = country;

  const equityRiskPremium = await equityRiskPremiumRepository.findOne({ countryId, date });
  if (equityRiskPremium != null) {
    return res.json({
      equityRiskPremium: numberUtils.roundToXDecimals(String(equityRiskPremium.dataValues.equityRiskPremium)),
    });
  }

  const regionalEquityRiskPremium = await regionalEquityRiskPremiumRepository.findOne({ region, date });
  if (regionalEquityRiskPremium != null) {
    return res.json({
      equityRiskPremium: numberUtils.roundToXDecimals(String(regionalEquityRiskPremium.equityRiskPremium)),
    });
  }

  throw new BadRequestError(`Equity risk premium not found for country ${countryName} and date ${issueDate}`);
}

async function getBeta(req: Request, res: Response) {
  const { industry, region, type, issueDate } = req.body;

  const beta = await betaRepository.findOne({ industry, region, date: issueDate });

  if (!beta) {
    throw new BadRequestError(`Beta not found for industry ${industry} and region ${region}`);
  }

  if (type === betaUtils.types.beta) {
    return res.json({ beta: numberUtils.roundToXDecimals(String(beta.beta)) });
  }
  if (type === betaUtils.types.unleveredBeta) {
    return res.json({ beta: numberUtils.roundToXDecimals(String(beta.unleveredBeta)) });
  }
  if (type === betaUtils.types.unleveredBetaCorrectedForCash) {
    return res.json({ beta: numberUtils.roundToXDecimals(String(beta.unleveredBetaCorrectedForCash)) });
  }

  throw new NotFoundError('Beta');
}

async function getLoans(req: Request, res: Response) {
  const { limit, isPortfolio } = req.query;
  const loans = await b2bLoanRepository.getLoans({
    where: { clientId: req.user?.clientId!, isPortfolio: isPortfolio === 'true' },
    order: orderByCreationDate,
    limit: isNaN(Number(limit)) ? null : parseInt(String(limit)),
  });
  res.json(loans);
}

async function getLoan(req: Request<{ id: number }>, res: Response) {
  const loan = await b2bLoanRepository.getLoan(req.params.id, req.user?.clientId!);

  if (!loan) {
    throw new NotFoundError('Back-to-back Loan');
  }

  res.json(loan);
}

async function createLoan(req: Request, res: Response) {
  const loan = req.body;

  await sequelize.transaction(async () => {
    const b2bLoanToCreate = await prepareB2BLoanForCreation(loan, req.user?.clientId!);
    const createdB2BLoan = await b2bLoanRepository.createLoan(b2bLoanToCreate, req.user!);

    const legs = loan.lenders.map((lender: EmbeddedCompanyType, index: number) => ({
      lender,
      borrower: loan.borrowers[index],
      ordinal: index + 1,
      loanId: createdB2BLoan.id,
    }));
    await b2bLoanLegRepository.bulkCreate(legs);

    return res.json(createdB2BLoan);
  });
}

async function putLoan(req: Request<{ id: number }>, res: Response) {
  const id = req.params.id;
  const loan = req.body;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    const [oldLoan, clientFeature] = await Promise.all([
      b2bLoanRepository.getLoan(id, clientId),
      featureRepository.getClientFeatureByName({ clientId, featureName: featureNames.BACK_TO_BACK_LOAN_NUMBER }),
    ]);

    if (!oldLoan) throw new NotFoundError('Back-to-back loan');

    reportUtils.runReportUpdateGuards(oldLoan, 'back-to-back loan');
    reportUtils.issueDateUpdateCheck(oldLoan, loan, clientFeature);
    reportUtils.checkCurrencyProviderDataAvailability(loan);

    const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

    const ultimateLender = loan.lenders[0];
    const ultimateBorrower = loan.borrowers[loan.borrowers.length - 1];

    const { report, calculationLog, pricingApproach } = await calculateLoanDataAlgorithm({
      ...{ ...loan, lender: ultimateLender, borrower: ultimateBorrower },
      clientId,
      isUsingRegionalProviderData,
      isApproachCalculated: true,
    });
    loan.report = { ...loan.report, ...report };
    loan.calculationLog = calculationLog;
    loan.pricingApproach = pricingApproach;

    // Initialize referenceRate
    if (loan.rateType.type === 'float') {
      loan.rateType = {
        ...loan.rateType,
        ...loanUtils.getReferenceRate(loan.currency, loan.paymentFrequency, loan.issueDate),
      };
    }

    await Promise.all(
      oldLoan.legs.map((legs) =>
        b2bLoanLegRepository.update({ id: legs.id, loanId: Number(oldLoan.id) }, { report: null }),
      ),
    );

    const updatedLoan = await b2bLoanRepository.updateLoan(id, loan, req.user!);

    await Promise.all(oldLoan.files.map((file) => b2bLoanFileRepository.deleteLoanFile(file.id, oldLoan.id)));
    await Promise.all(oldLoan.files.map((file) => azureService.deleteFile(`b2bLoan/${file.id}`)));

    res.json(updatedLoan);
  });
}

async function putLoanRates(
  req: Request<{ id: number }, {}, { finalInterestRate: number; legsRates: Array<B2BLoanLegType> }>,
  res: Response,
) {
  const { id } = req.params;
  const { finalInterestRate } = req.body;

  await sequelize.transaction(async () => {
    const loan = (await b2bLoanRepository.getLoan(id, req.user?.clientId!))?.dataValues;
    if (!loan) {
      throw new NotFoundError('Back-to-back Loan');
    }
    if (loan.isPortfolio) {
      throw new BadRequestError('Back-to-back Loan must be in analyses for rate changing');
    }

    const legInterestRates = b2bLoanUtils.calculateLegInterestRates(loan, finalInterestRate);

    await Promise.all(
      loan.legs.map((leg, i) =>
        b2bLoanLegRepository.update(
          { id: leg.id, loanId: leg.loanId },
          { report: literal(`COALESCE(report::jsonb, '{}') || '{"finalInterestRate": ${legInterestRates[i]}}'`) },
        ),
      ),
    );

    loan.report = { ...loan.report, ...req.body };
    await b2bLoanRepository.updateLoan(id, loan, req.user!);

    res.json(await b2bLoanRepository.getLoan(id, req.user?.clientId!));
  });
}

async function putLegRates(req: Request<{ id: number }>, res: Response) {
  const { id } = req.params;
  const { legs } = req.body;

  await Promise.all(
    legs.map((leg: any) => b2bLoanLegRepository.update({ id: leg.id, loanId: leg.loanId }, { report: leg.report })),
  );

  res.json(await b2bLoanRepository.getLoan(id, req.user?.clientId!));
}

// TODO matej - refactor this since it duplicate from loan payments
async function calculatePaymentsForAnalyses(req: Request<{ id: number }>, res: Response) {
  const { id: loanId } = req.params;
  const { finalInterestRate, basisPoints, estimationOfReferenceRate, whtInterestRate, approach, isWhtEnabled } =
    req.body;
  const clientId = req.user?.clientId!;

  const loan = await b2bLoanRepository.getLoan(loanId, clientId);

  if (!loan) {
    throw new NotFoundError('Loan');
  }
  if (reportUtils.checkIsImported(loan.report)) {
    throw new BadRequestError('Loan must not be imported');
  }
  if (loan.rateType.type === 'fixed' && !finalInterestRate) {
    throw new BadRequestError('For fixed interest, Interest Rate has to be provided');
  }
  if (loan.rateType.type === 'float' && basisPoints === 0 && estimationOfReferenceRate === 0) {
    throw new BadRequestError(
      'For float interest either Basis Points or Estimation of Reference Rate have to be provided',
    );
  }

  const [totalInterest, payments] = await repaymentService.getPayments({
    report: loan,
    finalInterestRate,
    reportIdKey: 'loanId',
    basisPoints,
    estimationOfReferenceRate,
  });

  const interestPerYear: Record<number, number> = {};
  payments.forEach((payment: any) => {
    const { year, interest } = paymentUtils.getInterestPerYear({ payment, type: loan.type });
    if (!interestPerYear[year]) interestPerYear[year] = interest;
    else interestPerYear[year] = interestPerYear[year] + interest;
  });

  const principalRepayment = payments[payments.length - 1].paymentAmount;

  if (isWhtEnabled) {
    const [whtInterestPerYear, newInterestPerYearDependentOnWht] = paymentUtils.calculateWhtInterestRateBasedOnApproach(
      approach,
      whtInterestRate,
      interestPerYear,
    );

    res.json({
      totalInterest,
      interestPerYear: newInterestPerYearDependentOnWht,
      whtInterestPerYear,
      principalRepayment,
      isWhtEnabled,
    });
  } else {
    res.json({
      totalInterest,
      interestPerYear,
      whtInterestPerYear: null,
      principalRepayment,
      isWhtEnabled,
    });
  }
}

async function deleteLoan(req: Request<{ id: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const role = req.user?.role!;
  const force = req.query.force === 'true';
  const loanId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const loan = await b2bLoanRepository.getLoan(loanId, clientId);

  if (!loan) throw new NotFoundError('Loan');
  const feature = await featureRepository.getClientFeatureByName({
    clientId,
    featureName: featureNames.BACK_TO_BACK_LOAN_NUMBER,
  });
  if (loan.isPortfolio && isUser) throw new ForbiddenError('Only admins can delete a loan in portfolio');
  if (force && isUser) throw new ForbiddenError('Only admins can permanently delete loans');
  if (feature.isEnabled && force) throw new ForbiddenError('Cannot permanently delete loan.');

  await sequelize.transaction(async () => {
    if (force) await Promise.all(loan.files.map((file) => azureService.deleteFile(`b2bLoan/${file.id}`)));

    await b2bLoanRepository.deleteLoan(loanId, clientId, force);
  });

  res.status(204).send();
}

async function restoreLoan(req: Request<{ id: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const role = req.user?.role!;
  const loanId = req.params.id;

  const loan = await b2bLoanRepository.getLoan(loanId, clientId);

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  if (role === rolesEnum.USER) {
    throw new ForbiddenError('Only admins can restore a loan');
  }

  await b2bLoanRepository.restoreLoan(loanId, clientId);

  res.status(204).send();
}

async function putLoanStatus(req: Request<{ id: number }>, res: Response) {
  const id = req.params.id;
  const { status } = req.body;

  const loan = (await b2bLoanRepository.getLoan(id, req.user?.clientId!))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Back-to-back loan');
  }

  if (!loan.editable) {
    if (status === 'Draft') {
      throw new MarkAsDraftUneditableError('back-to-back loan');
    }
    throw new FinalizeUneditableError('back-to-back loan');
  }

  loan.status = status;
  if (status === 'Final') {
    loan.finalizedBy = req.user?.username!;
  } else {
    loan.finalizedBy = null;
  }

  res.json(await b2bLoanRepository.updateLoan(id, loan, req.user!));
}

async function putLoanNote(req: Request<{ id: number }>, res: Response) {
  const id = req.params.id;
  const { note } = req.body;

  const loan = (await b2bLoanRepository.getLoan(id, req.user?.clientId!))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Back-to-back loan');
  }
  if (loan.status === 'Final') {
    throw new BadRequestError('Cannot update note of finalized loan.');
  }

  loan.note = note;
  res.json(await b2bLoanRepository.updateLoan(id, loan, req.user!));
}

async function putLoanIsPortfolio(req: Request<{ id: number }>, res: Response) {
  const { id } = req.params;
  const { isPortfolio } = req.body;

  const loan = (await b2bLoanRepository.getLoan(id, req.user?.clientId!))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Back-to-back loan');
  }

  reportUtils.runReportUpdateGuards(loan, 'back-to-back loan');

  await sequelize.transaction(async () => {
    if (!isPortfolio) {
      loan.movedToAnalysesDate = new Date();
    }

    loan.isPortfolio = isPortfolio;

    await b2bLoanRepository.updateLoan(id, loan, req.user!, false);
    return res.status(204).send();
  });
}

async function getDeletedLoans(req: Request, res: Response) {
  const loans = await b2bLoanRepository.getLoans({
    where: { clientId: req.user?.clientId!, deletedAt: { [Op.ne]: null } },
    order: orderByDeleteDate,
    paranoid: false,
  });
  res.json(loans);
}

async function generateAgreements(req: Request<{ id: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const loanId = req.params.id;

  await sequelize.transaction(async () => {
    const loan = (await b2bLoanRepository.getLoan(loanId, clientId, null))?.dataValues;

    if (!loan) {
      throw new NotFoundError('Back-to-back loan');
    }

    if (reportUtils.checkIsImported(loan.report)) {
      throw new BadRequestError("Can't generate agreement for imported loan");
    }

    if (loan.files.find((file) => file.label === 'Agreement' && file.isGenerated)) {
      throw new BadRequestError('Agreement already exists');
    }

    const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

    const createdAgreements = [];

    for (const leg of loan.legs) {
      const b2bLeg = await b2bLoanLegRepository.getOne({ id: leg.id, loanId });
      if (!b2bLeg) {
        throw new NotFoundError('Back-to-back loan leg');
      }

      const existingLegReport = b2bLeg.report;
      const loanWithLegLenderAndBorrower = { ...loan, lender: leg.lender, borrower: leg.borrower };

      const { report: algorithmPoints, calculationLog } = await calculateLoanDataAlgorithm({
        ...{ ...loanWithLegLenderAndBorrower, report: existingLegReport },
        clientId,
        isUsingRegionalProviderData,
        isApproachCalculated: true,
      });

      const legReport = { ...existingLegReport, ...algorithmPoints };

      await b2bLoanLegRepository.update({ id: leg.id, loanId }, { report: legReport, calculationLog });

      const templateData = await b2bLoanUtils.getTemplateData(
        { ...loanWithLegLenderAndBorrower, calculationLog, report: legReport },
        clientId,
        isUsingRegionalProviderData,
      );

      const [agreementFile, agreementMetadata] = await b2bLoanUtils.generateAgreement(
        { ...loanWithLegLenderAndBorrower, report: legReport },
        clientId,
        templateData,
      );

      const createdAgreement = await b2bLoanFileRepository.simpleCreateFile(agreementMetadata);
      createdAgreements.push(createdAgreement);
      const fileId = createdAgreement.id;
      await azureService.uploadFile(`b2bLoan/${fileId}`, agreementFile, Buffer.byteLength(agreementFile));
    }
    res.json(createdAgreements);
  });
}

async function generateTpReport(req: Request<{ id: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const loanId = req.params.id;

  await sequelize.transaction(async () => {
    const loan = (await b2bLoanRepository.getLoan(loanId, clientId, null))?.dataValues;

    if (!loan) {
      throw new NotFoundError('Back-to-back Loan');
    }

    if (loan.files.find((file) => file.label === 'TP Report' && file.isGenerated)) {
      throw new BadRequestError('TP report already exists');
    }

    const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

    const loanWithUltimateLenderAndBorrower = { ...loan, lender: loan.ultimateLender, borrower: loan.ultimateBorrower };

    const templateData = await b2bLoanUtils.getTemplateData(
      loanWithUltimateLenderAndBorrower,
      clientId,
      isUsingRegionalProviderData,
    );

    const [tpReportFile, tpReportMetadata] = await b2bLoanUtils.generateTpReport(
      loanWithUltimateLenderAndBorrower,
      clientId,
      templateData,
    );
    const createdTpReport = await b2bLoanFileRepository.simpleCreateFile(tpReportMetadata);

    const fileId = createdTpReport.id;
    await azureService.uploadFile(`b2bLoan/${fileId}`, tpReportFile, Buffer.byteLength(tpReportFile));
    res.json(createdTpReport);
  });
}

export default {
  getEquityRiskPremium: asyncControllerWrapper(getEquityRiskPremium),
  getBeta: asyncControllerWrapper(getBeta),
  getLoans: asyncControllerWrapper(getLoans),
  getLoan: asyncControllerWrapper(getLoan),
  createLoan: asyncControllerWrapper(createLoan),
  putLoan: asyncControllerWrapper(putLoan),
  putLegRates: asyncControllerWrapper(putLegRates),
  putLoanRates: asyncControllerWrapper(putLoanRates),
  calculatePaymentsForAnalyses: asyncControllerWrapper(calculatePaymentsForAnalyses),
  deleteLoan: asyncControllerWrapper(deleteLoan),
  restoreLoan: asyncControllerWrapper(restoreLoan),
  putLoanStatus: asyncControllerWrapper(putLoanStatus),
  putLoanNote: asyncControllerWrapper(putLoanNote),
  putLoanIsPortfolio: asyncControllerWrapper(putLoanIsPortfolio),
  getDeletedLoans: asyncControllerWrapper(getDeletedLoans),
  generateAgreements: asyncControllerWrapper(generateAgreements),
  generateTpReport: asyncControllerWrapper(generateTpReport),
};
