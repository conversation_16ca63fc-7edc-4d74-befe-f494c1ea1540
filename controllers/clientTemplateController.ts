import fs from 'fs';
import { Request, Response } from 'express';

import models from '../models';
import { clientTemplateRepository } from '../repositories';
import * as azureService from '../services/azureService';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import * as templateFilesUtils from '../utils/templateFilesUtils';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';
import { TemplateFileLabelsEnum } from '../enums/templateFiles';
import { FileContentTypeEnum } from '../enums/files';
import { TemplateFileTypeType, TemplateFileTypeValuesType } from '../types';
const { sequelize } = models;

const getTemplateFile = async (req: Request<{ templateId: number }>, res: Response) => {
  const { templateId } = req.params;

  const templateFile = await clientTemplateRepository.getTemplateFile({ where: { id: templateId } });

  if (!templateFile) {
    throw new NotFoundError('Template');
  }

  const fileBuffer = await azureService.getFile(`template/${templateId}`);

  res.set('Content-Type', templateFile.mimeType);
  res.set('Content-Disposition', `attachment; filename="${templateFile.name}${templateFile.extension}"`);
  res.send(fileBuffer);
};

const getStandardTemplateByLabel = async (req: Request<{ label: TemplateFileLabelsEnum }>, res: Response) => {
  const { label } = req.params;

  const filename = templateFilesUtils.getTemplateFilenameFromLabel(label);

  const matchedFilename = filename.match(/(.+?)(\.[^.]*$|$)/);
  if (!matchedFilename) {
    throw new NotFoundError('File');
  }

  const extensions = matchedFilename[2].replace('.', '').toUpperCase();

  if (!(extensions in FileContentTypeEnum)) {
    throw new NotFoundError('File');
  }

  res.set('Content-Type', FileContentTypeEnum[extensions as keyof typeof FileContentTypeEnum]);
  res.set('Content-Disposition', `attachment; filename="${filename}"`);
  fs.createReadStream(`./static/${filename}`).pipe(res);
};

const getAllClientTemplateFileLabels = async (req: Request, res: Response) => {
  const clientId = req.user?.clientId!;

  const distinctLabels = await clientTemplateRepository.getTemplateFilesLabels({ where: { clientId } });

  const labels = distinctLabels.map(({ label }) => label);

  res.json(labels);
};

const getAllClientTemplateFiles = async (req: Request, res: Response) => {
  const clientId = req.user?.clientId!;

  const templateFiles = await clientTemplateRepository.getTemplateFiles({ where: { clientId } });

  res.json(templateFiles);
};

const uploadTemplateFile = async (
  req: Request<
    {},
    {},
    { label: TemplateFileLabelsEnum; type: TemplateFileTypeType; country?: string; companyId?: string },
    {}
  >,
  res: Response,
) => {
  if (!req.file) {
    throw new BadRequestError('Upload file missing.');
  }

  const clientId = req.user?.clientId!;
  const { buffer, size } = req.file;
  const { label, type, country, companyId } = req.body;

  if (!Object.values(TemplateFileLabelsEnum).includes(label)) {
    throw new BadRequestError('Invalid label.');
  }

  const templateFileTypeValues: TemplateFileTypeValuesType = ['loan', 'b2bLoan', 'guarantee'];
  if (!templateFileTypeValues.includes(type)) {
    throw new BadRequestError('Invalid type.');
  }

  await sequelize.transaction(async () => {
    try {
      const templateFile = await clientTemplateRepository.createTemplateFile(req.file!, clientId, {
        label,
        type,
        country,
        companyId: companyId ? parseInt(companyId) : undefined,
      });

      const fileId = templateFile.id;
      await azureService.uploadFile(`template/${fileId}`, buffer, size);
      res.send();
    } catch (err: any) {
      // unique constraint error code
      if (Number(err?.original?.code) === 23505) {
        throw new BadRequestError('Label already used. To upload a new template first delete the existing one.');
      }
      throw err;
    }
  });
};

const deleteTemplateFile = async (req: Request<{ templateId: number }>, res: Response) => {
  const clientId = req.user?.clientId!;
  const { templateId } = req.params;

  await sequelize.transaction(async () => {
    await clientTemplateRepository.deleteTemplateFile(templateId, clientId);

    await azureService.deleteFile(`template/${templateId}`);
    res.status(204).send();
  });
};

export default {
  getTemplateFile: asyncControllerWrapper(getTemplateFile),
  getStandardTemplateByLabel: asyncControllerWrapper(getStandardTemplateByLabel),
  getAllClientTemplateFileLabels: asyncControllerWrapper(getAllClientTemplateFileLabels),
  uploadTemplateFile: asyncControllerWrapper(uploadTemplateFile),
  getAllClientTemplateFiles: asyncControllerWrapper(getAllClientTemplateFiles),
  deleteTemplateFile: asyncControllerWrapper(deleteTemplateFile),
};
