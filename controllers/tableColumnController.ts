import { Request, Response } from 'express';

import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { tableColumnRepository } from '../repositories';

async function getTableColumns(req: Request, res: Response) {
  const clientId = req.user?.clientId!;

  const tableColumns = await tableColumnRepository.getTableColumns(clientId);

  if (!tableColumns) return res.json({ loan: null, guarantee: null });

  res.json({ loan: tableColumns.loan, guarantee: tableColumns.guarantee });
}

async function updateTableColumns(req: Request, res: Response) {
  const clientId = req.user?.clientId!;

  const tableColumns = await tableColumnRepository.getTableColumns(clientId);

  if (!tableColumns) {
    await tableColumnRepository.createTableColumns(clientId, req.body);
  } else {
    await tableColumnRepository.updateTableColumns(clientId, req.body);
  }

  res.send();
}

export default {
  getTableColumns: asyncControllerWrapper(getTableColumns),
  updateTableColumns: asyncControllerWrapper(updateTableColumns),
};
