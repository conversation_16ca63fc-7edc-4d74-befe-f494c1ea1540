import { Request, Response } from 'express';

import companyAuditTrailRepository from '../repositories/companyAuditTrailRepository';
import companyRepository from '../repositories/companyRepository';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { NotFoundError } from '../utils/ErrorHandler';

async function getCompanyAuditTrailAll(req: Request, res: Response) {
  const companyExists = await companyRepository.companyExists(req.params.id, req.user?.clientId);
  if (companyExists) {
    const companyAuditTrails = await companyAuditTrailRepository.getCompanyAuditTrailAll(req.params.id);
    res.json(companyAuditTrails);
  } else {
    throw new NotFoundError('Company');
  }
}

async function getCompanyAuditTrail(req: Request, res: Response) {
  const companyExists = await companyRepository.companyExists(req?.params?.id, req.user?.clientId);
  if (companyExists) {
    const companyAuditTrail = await companyAuditTrailRepository.getCompanyAuditTrail(req?.params?.auditId);
    if (companyAuditTrail) res.json(companyAuditTrail);
    else throw new NotFoundError('Company audit trail');
  } else {
    throw new NotFoundError('Company');
  }
}

async function deleteCompanyAuditTrail(req: Request, res: Response) {
  const companyExists = await companyRepository.companyExists(req?.params?.id, req.user?.clientId);
  if (companyExists) {
    await companyAuditTrailRepository.deleteCompanyAuditTrail(req.params.auditId);
    return res.status(204).send();
  }
  throw new NotFoundError('Company');
}

export default {
  getCompanyAuditTrailAll: asyncControllerWrapper(getCompanyAuditTrailAll),
  getCompanyAuditTrail: asyncControllerWrapper(getCompanyAuditTrail),
  deleteCompanyAuditTrail: asyncControllerWrapper(deleteCompanyAuditTrail),
};
