import { Request, Response } from 'express';
import _ from 'lodash';

import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { creditRatingDraftRepository } from '../repositories';
import { CreditRatingDraftType } from '../types';

async function getCreditRatingDrafts(req: Request, res: Response) {
  const clientId = req.user?.clientId!;

  const drafts = await creditRatingDraftRepository.getCreditRatingDrafts(clientId);

  res.json(drafts);
}

async function postCreditRatingDraft(req: Request, res: Response) {
  const clientId = req.user?.clientId!;
  const draft = req.body as CreditRatingDraftType;
  draft.clientId = clientId;

  const creditRatingDraft = await creditRatingDraftRepository.createCreditRatingDraft(draft);
  res.json(creditRatingDraft);
}

async function deleteCreditRatingDraft(req: Request, res: Response) {
  const { id } = req.params;
  const clientId = req.user?.clientId!;
  await creditRatingDraftRepository.deleteCreditRatingDraft(id, clientId);
  res.status(204).send();
}

export default {
  getCreditRatingDrafts: asyncControllerWrapper(getCreditRatingDrafts),
  postCreditRatingDraft: asyncControllerWrapper(postCreditRatingDraft),
  deleteCreditRatingDraft: asyncControllerWrapper(deleteCreditRatingDraft),
};
