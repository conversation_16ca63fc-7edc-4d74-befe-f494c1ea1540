const _ = require('lodash');
const { Op } = require('sequelize');

const guaranteeRepository = require('../repositories/guaranteeRepository');
const paymentRepository = require('../repositories/paymentRepository');
const { getPayments: getSolverPayments } = require('../services');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { jsonToSheet } = require('../utils/documents');
const { BadRequestError, NotFoundError } = require('../utils/ErrorHandler');
const { getFilterBy, getOrderBy, getInterestPerYear, getGuaranteePaymentsSheetData } = require('../utils/payments');
const { checkIsImported } = require('../utils/reportUtils');

const orderByPaymentDueDate = [['paymentDueDate', 'ASC']];

async function getGuaranteePaymentsByGuaranteeId(req, res) {
  const { clientId } = req.user;
  const guaranteeId = req.params.id;
  const { limit, isPaid } = req.query;

  const where = { guaranteeId };
  if (isPaid) {
    where.isPaid = isPaid;
  }
  const guarantee = await guaranteeRepository.getGuarantee(guaranteeId, clientId);

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }

  const allPaymentsOfGuarantee = await paymentRepository.getPayments({
    where: { guaranteeId },
    order: orderByPaymentDueDate,
    guaranteeFilter: { clientId },
  });

  const totalNumberOfPayments = allPaymentsOfGuarantee.length;

  const guaranteePayments = await paymentRepository.getPayments({
    where,
    order: orderByPaymentDueDate,
    guaranteeFilter: { clientId },
    limit,
  });

  // ordinal not needed since paymentNumber is already passed, but needs changes on frontend to work without ordinal
  const guaranteePaymentsWithOrdinal = guaranteePayments.map((payment) => ({
    ...payment.dataValues,
    ordinal: payment.paymentNumber,
    totalNumberOfPayments,
  }));

  res.json(guaranteePaymentsWithOrdinal);
}

async function getPayments(req, res) {
  // same logic is used for exporting to excel (exportGuaranteePaymentsExcel)
  const { clientId } = req.user;
  const { offset = 0, limit = 10 } = req.query;
  const order = getOrderBy(req.query?.sort, 'guarantee') || orderByPaymentDueDate;
  const [paymentFilter, guaranteeFilter] = getFilterBy(req.query, { isLoan: false });

  const totalNumberOfAllPayments = await paymentRepository.getTotalPaymentsCount({
    where: { guaranteeId: { [Op.ne]: null }, ...paymentFilter },
    guaranteeFilter: { ...guaranteeFilter, status: 'Final', clientId },
  });

  const payments = await paymentRepository.getPayments({
    where: {
      guaranteeId: { [Op.ne]: null },
      ...paymentFilter,
    },
    guaranteeFilter: { ...guaranteeFilter, status: 'Final', clientId },
    order,
    limit,
    offset: offset * limit,
  });

  const guaranteeIdsOfPayments = _.uniq(payments.map((payment) => payment.guaranteeId));
  const totalCount = await paymentRepository.getPaymentsCount(
    { guaranteeId: guaranteeIdsOfPayments },
    'Payment.guaranteeId',
  );
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of payments) {
      if (count.guaranteeId === payment.guaranteeId) {
        payment.totalNumberOfPayments = count.totalNumberOfPayments;
      }
    }
  }

  return res.json({ payments, totalNumberOfPayments: totalNumberOfAllPayments });
}

async function getNextGuaranteePayments(req, res) {
  const { clientId } = req.user;
  const { limit = 3 } = req.query;

  /* payments only available for guarantees in portfolio */
  const guarantees = await guaranteeRepository.getGuarantees({ where: { clientId, isPortfolio: true } });
  const guaranteeIds = guarantees.map((guarantee) => guarantee.id);

  const guaranteePayments = await paymentRepository.getPayments({
    where: { guaranteeId: guaranteeIds, isPaid: false },
    order: orderByPaymentDueDate,
    guaranteeFilter: { status: 'Final', clientId },
    limit,
  });

  const guaranteeIdsOfPayments = guaranteePayments.map((guaranteePayment) => guaranteePayment.guaranteeId);
  const totalCount = await paymentRepository.getPaymentsCount(
    { guaranteeId: guaranteeIdsOfPayments },
    'Payment.guaranteeId',
  );
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of guaranteePayments) {
      if (count.guaranteeId === payment.guaranteeId) {
        payment.totalNumberOfPayments = count.totalNumberOfPayments;
        payment.paidPayments = count.paidPayments;
      }
    }
  }
  res.json(guaranteePayments);
}

async function calculatePaymentsForAnalyses(req, res) {
  const { id: guaranteeId } = req.params;
  const { finalInterestRate } = req.body;
  const { clientId } = req.user;

  const guarantee = await guaranteeRepository.getGuarantee(guaranteeId, clientId, null);

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }
  if (checkIsImported(guarantee.report)) {
    throw new BadRequestError('Guarantee must not be imported');
  }

  const [totalInterest, payments] = await getSolverPayments({
    report: guarantee,
    finalInterestRate,
    reportIdKey: 'guaranteeId',
  });

  const interestPerYear = {};
  payments.forEach((payment) => {
    const { year, interest } = getInterestPerYear({ payment, type: 'Bullet' });
    if (!interestPerYear[year]) interestPerYear[year] = interest;
    else interestPerYear[year] = interestPerYear[year] + interest;
  });

  const principalRepayment = payments[payments.length - 1].paymentAmount;

  res.json({ totalInterest, interestPerYear, principalRepayment });
}

async function markPaymentAsPaid(req, res) {
  const { clientId } = req.user;
  const { id: guaranteeId, paymentId } = req.params;
  const { isPaid } = req.body;

  const guarantee = await guaranteeRepository.getGuarantee(guaranteeId, clientId);

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }

  const [affectedRows, updatedGuaranteePayment] = await paymentRepository.updatePayment(paymentId, { isPaid }, true);

  if (affectedRows === 0) {
    throw new NotFoundError('Guarantee payment');
  }

  res.json(updatedGuaranteePayment);
}

async function exportGuaranteePaymentsExcel(req, res) {
  // same logic as in getPayments
  const { clientId } = req.user;
  const { columns } = req.query;
  const order = getOrderBy(req.query?.sort, 'guarantee') || orderByPaymentDueDate;
  const [paymentFilter, guaranteeFilter] = getFilterBy(req.query, { isLoan: false });

  const payments = await paymentRepository.getPayments({
    where: {
      guaranteeId: { [Op.ne]: null },
      ...paymentFilter,
    },
    guaranteeFilter: { ...guaranteeFilter, status: 'Final', clientId },
    order,
  });

  const guaranteeIdsOfPayments = _.uniq(payments.map((payment) => payment.guaranteeId));
  const totalCount = await paymentRepository.getPaymentsCount(
    { guaranteeId: guaranteeIdsOfPayments },
    'Payment.guaranteeId',
  );
  for (const { dataValues: count } of totalCount) {
    for (const { dataValues: payment } of payments) {
      if (count.guaranteeId === payment.guaranteeId) {
        payment.totalNumberOfPayments = count.totalNumberOfPayments;
      }
    }
  }
  const sheetData = getGuaranteePaymentsSheetData(payments, columns.split(', '));
  const data = [{ sheetData, sheetName: 'Guarantee interest' }];
  const result = jsonToSheet(data);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Guarantee interest.xlsx"');
  res.send(result);
}

module.exports = {
  getGuaranteePaymentsByGuaranteeId: asyncControllerWrapper(getGuaranteePaymentsByGuaranteeId),
  getPayments: asyncControllerWrapper(getPayments),
  getNextGuaranteePayments: asyncControllerWrapper(getNextGuaranteePayments),
  calculatePaymentsForAnalyses: asyncControllerWrapper(calculatePaymentsForAnalyses),
  markPaymentAsPaid: asyncControllerWrapper(markPaymentAsPaid),
  exportGuaranteePaymentsExcel: asyncControllerWrapper(exportGuaranteePaymentsExcel),
};
