import { Request, Response } from 'express';

import { cashPoolAuditTrailRepository, cashPoolRepository } from '../repositories';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { NotFoundError } from '../utils/ErrorHandler';

async function getCashPoolAuditTrailAll(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const cashPoolAuditTrails = await cashPoolAuditTrailRepository.getCashPoolAuditTrailAll(cashPoolId);
  return res.json(cashPoolAuditTrails);
}

async function getCashPoolAuditTrail(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;
  const { auditTrailId } = req.params;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const cashPoolAuditTrail = await cashPoolAuditTrailRepository.getCashPoolAuditTrail({ where: { id: auditTrailId } });
  if (!cashPoolAuditTrail) throw new NotFoundError('Cash pool audit trail');

  return res.json(cashPoolAuditTrail);
}

async function deleteCashPoolAuditTrail(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;
  const { auditTrailId } = req.params;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  await cashPoolAuditTrailRepository.deleteCashPoolAuditTrail(auditTrailId);
  return res.status(204).send();
}

export default {
  getCashPoolAuditTrailAll: asyncControllerWrapper(getCashPoolAuditTrailAll),
  getCashPoolAuditTrail: asyncControllerWrapper(getCashPoolAuditTrail),
  deleteCashPoolAuditTrail: asyncControllerWrapper(deleteCashPoolAuditTrail),
};
