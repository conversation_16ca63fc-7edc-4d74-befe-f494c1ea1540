import { addDays, format } from 'date-fns';
import { Request, Response } from 'express';
import { Op } from 'sequelize';

import { cashPoolEnums } from '../enums';
import models from '../models';
import {
  cashPoolBatchFileRepository,
  cashPoolBatchRepository,
  cashPoolParticipantAccountRepository,
  cashPoolRepository,
  cashPoolStatementDataRepository,
} from '../repositories';
import * as azureService from '../services/azureService';
import cashPoolService from '../services/cashPoolService';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';
const { sequelize } = models;

async function downloadBatchFile(req: Request, res: Response) {
  const { cashPoolId, batchId } = req.params;
  const { clientId } = req.user!;

  const batchFile = await cashPoolBatchFileRepository.getCashPoolBatchFile({ clientId, cashPoolId, batchId });

  if (!batchFile) {
    throw new NotFoundError('Batch file');
  }

  const fileBuffer = await azureService.getFile(`cashPoolBatch/${batchFile.id}`);

  res.set('Content-Type', batchFile.mimeType);
  res.set('Content-Disposition', `attachment; filename="${batchFile.name}${batchFile.extension}"`);
  return res.send(fileBuffer);
}

async function getBatches(req: Request, res: Response) {
  const { clientId } = req.user!;
  const { cashPoolId } = req.params;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) {
    throw new NotFoundError('Cash pool');
  }

  const result = await cashPoolBatchRepository.getCashPoolBatches({ cashPoolId, clientId });

  res.json(result);
}

async function uploadBatchFile(req: Request<{ cashPoolId: number }>, res: Response) {
  const { cashPoolId } = req.params;
  const clientId = req.user?.clientId!;
  const userId = req.user?.id!;

  await sequelize.transaction(async () => {
    await cashPoolService.uploadBatchFile({ clientId, userId, cashPoolId, file: req.file! });
    res.status(201).send();
  });
}

async function runBatchPhysicalCashPool(req: Request<{ cashPoolId: number; batchId: number }>, res: Response) {
  const { cashPoolId, batchId } = req.params;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    await cashPoolService.runBatchPhysical({ clientId, cashPoolId, batchId });
    res.send();
  });
}

async function runBatchNotionalCashPool(req: Request<{ cashPoolId: number; batchId: number }>, res: Response) {
  const { cashPoolId, batchId } = req.params;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    await cashPoolService.runBatchNotional({ clientId, cashPoolId, batchId });
    res.send();
  });
}

async function runBatchNordicCashPool(req: Request<{ cashPoolId: number; batchId: number }>, res: Response) {
  const { cashPoolId, batchId } = req.params;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    await cashPoolService.runBatchNordic({ clientId, cashPoolId, batchId });
    res.send();
  });
}

async function getBatchRange(req: Request<{ cashPoolId: number; batchId: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const { cashPoolId } = req.params;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const nonLeaderParticipantAccountIds = cashPool.participants
    .filter((p) => !p.isLeader)
    .map((participant) => participant.accounts[0]?.id);
  if (nonLeaderParticipantAccountIds.length === 0) throw new BadRequestError('Cash Pool participant has no accounts');

  const lastStatement = await cashPoolStatementDataRepository.getStatementData({
    where: { cashPoolAccountId: nonLeaderParticipantAccountIds },
    order: [['date', 'DESC']],
  });
  const endDate = lastStatement?.date;

  const lastBatch = await cashPoolBatchRepository.getCashPoolBatch({
    where: { status: { [Op.ne]: cashPoolEnums.batchStatus.AUTO_GENERATED } },
    cashPoolId,
    clientId,
    order: [['endDate', 'DESC']] as any,
  });

  if (endDate === lastBatch?.endDate) return res.json({ startDate: null, endDate: null });

  if (lastBatch)
    return res.json({ startDate: format(addDays(new Date(lastBatch?.endDate), 1), 'yyyy-MM-dd'), endDate });

  const firstStatement = await cashPoolStatementDataRepository.getStatementData({
    where: { cashPoolAccountId: nonLeaderParticipantAccountIds },
    order: [['date', 'ASC']],
  });

  res.json({ startDate: firstStatement?.date, endDate });
}

async function deleteBatch(req: Request, res: Response) {
  const { cashPoolId, batchId } = req.params;
  const { clientId } = req.user!;

  await sequelize.transaction(async () => {
    const batch = await cashPoolBatchRepository.getCashPoolBatch({ where: { id: batchId }, cashPoolId, clientId });
    if (!batch) throw new NotFoundError('Batch');

    if ([cashPoolEnums.batchStatus.PAID, cashPoolEnums.batchStatus.PARTIALLY_PAID].includes(batch.status)) {
      throw new BadRequestError('Cannot delete batch that has published interest.');
    }

    // Revert accounts to the state they were in before the batch was run.
    await Promise.all(
      batch.accountsSnapshot.map(
        ({ id, creditInterestRate, debitInterestRate, externalIds, ...attributesToUpdate }: any) =>
          cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount({ where: { id }, attributesToUpdate }),
      ),
    );

    const newGrossBenefit = batch.cashPool.grossBenefit - batch.grossBenefit;
    const updateCashPoolPromise = cashPoolRepository.updateCashPool({
      where: { id: cashPoolId },
      attributesToUpdate: { grossBenefit: newGrossBenefit },
    });

    const destroyBatchPromise = batch.destroy();
    const [[, [updatedCashPool]]] = await Promise.all([updateCashPoolPromise, destroyBatchPromise]);

    await azureService.deleteFile(`cashPoolBatch/${batch.cashPoolBatchFile.id}`).catch((error) => {
      if (error instanceof NotFoundError) return;
      throw error;
    });

    res.json(updatedCashPool);
  });
}

/**
 * User calls this endpoint by providing a start and end date for which they want to create payments. Each day data
 * is fetched from somewhere (currently the client, GLS, is uploading to our Azure Storage via SFTP). After the data
 * is fetched, it's processed, and the cash pool model is run for that day. After a while the user can create payments
 * for a specific date range. That is when this endpoint is called and the payments are created.
 */
async function createPhysicalBatchFromSftpData(
  req: Request<{ cashPoolId: number }, {}, { startDate: Date; endDate: Date; cutOffDate: Date }>,
  res: Response,
) {
  const { startDate, endDate } = req.body;
  const { cashPoolId } = req.params;
  const userId = req.user?.id!;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    await cashPoolService.runBatchPhysicalSFTP({ userId, cashPoolId, clientId, startDate, endDate });
    res.send();
  });
}

async function createNordicBatchFromSftpData(
  req: Request<{ cashPoolId: number }, {}, { startDate: Date; endDate: Date }>,
  res: Response,
) {
  const { startDate, endDate } = req.body;
  const { cashPoolId } = req.params;
  const userId = req.user?.id!;
  const clientId = req.user?.clientId!;

  await sequelize.transaction(async () => {
    await cashPoolService.runBatchNordicSFTP({ userId, cashPoolId, clientId, startDate, endDate });
    res.send();
  });
}

async function getPaymentInterestDates(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const lastBatch = await cashPoolBatchRepository.getCashPoolBatch({
    where: { status: { [Op.ne]: cashPoolEnums.batchStatus.AUTO_GENERATED } },
    cashPoolId,
    clientId,
    order: [['endDate', 'DESC']] as any,
  });

  res.json(lastBatch.endDate);
}

export default {
  downloadBatchFile: asyncControllerWrapper(downloadBatchFile),
  getBatches: asyncControllerWrapper(getBatches),
  uploadBatchFile: asyncControllerWrapper(uploadBatchFile),
  runBatchPhysicalCashPool: asyncControllerWrapper(runBatchPhysicalCashPool),
  runBatchNotionalCashPool: asyncControllerWrapper(runBatchNotionalCashPool),
  runBatchNordicCashPool: asyncControllerWrapper(runBatchNordicCashPool),
  getBatchRange: asyncControllerWrapper(getBatchRange),
  deleteBatch: asyncControllerWrapper(deleteBatch),
  createPhysicalBatchFromSftpData: asyncControllerWrapper(createPhysicalBatchFromSftpData),
  createNordicBatchFromSftpData: asyncControllerWrapper(createNordicBatchFromSftpData),
  getPaymentInterestDates: asyncControllerWrapper(getPaymentInterestDates),
};
