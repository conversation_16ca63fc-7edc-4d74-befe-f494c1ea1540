const { verifyRecaptcha, sendgridContactFormSendEmail } = require('../services');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { BadRequestError } = require('../utils/ErrorHandler');

const sendMail = async (req, res) => {
  const { recaptchaResponse, ...mail } = req.body;

  const isRecaptchaVerified = await verifyRecaptcha(recaptchaResponse);
  if (!isRecaptchaVerified) {
    throw new BadRequestError('Recaptcha not valid');
  }

  await sendgridContactFormSendEmail(mail);

  res.status(204).send();
};

module.exports = {
  sendMail: asyncControllerWrapper(sendMail),
};
