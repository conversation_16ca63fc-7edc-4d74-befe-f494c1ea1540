const { sequelize, CashPoolBatch } = require('../models');
const { Op } = require('sequelize');
const {
  cashPoolBatchPaymentsRepository,
  cashPoolBatchRepository,
  cashPoolStatementDataRepository,
  clientRepository,
  participantAccountTrailsRepository,
} = require('../repositories');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { getBatchStatus, getCashPoolPaymentsSheetData, getFilterBy, getOrderBy } = require('../utils/cashPoolBatch');
const { jsonToSheet } = require('../utils/documents');
const { NotFoundError, BadRequestError } = require('../utils/ErrorHandler');
const statementUtils = require('../utils/statementUtils');
const { subDays, startOfDay } = require('date-fns');

const orderByEndDate = [[{ model: CashPoolBatch, as: 'batch' }, 'endDate'], ['id']];

const roundTo = (value, decimals = 4) =>
  value == null ? null : Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);

const getCashPoolPayments = async (req, res) => {
  const { clientId } = req.user;
  const { offset = 0, limit = 10 } = req.query;
  const order = getOrderBy(req.query.sort) || orderByEndDate;
  const [paymentFilter, batchFilter, creditorFilter, debtorFilter] = getFilterBy(req.query);

  const totalNumberOfPayments = await cashPoolBatchPaymentsRepository.getTotalPaymentsCount({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
  });

  const payments = await cashPoolBatchPaymentsRepository.getCashPoolPayments({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
    order,
    limit,
    offset: offset * limit,
  });

  const paymentsWithTrails = await Promise.all(
    payments.map(async (payment) => {
      const { batch, participantAccount } = payment;
      const { startDate, endDate } = batch;

      const trails = await participantAccountTrailsRepository.getCashPoolTrails({
        cashPoolId: batch.cashPool.id,
        whereTrails: {
          participantAccountId: participantAccount.id,
          date: {
            [Op.between]: [startDate, endDate],
          },
        },
        whereCompany: { id: participantAccount.participant.companyId },
      });

      const initialBalanceTrail = await participantAccountTrailsRepository.getCashPoolTrails({
        cashPoolId: batch.cashPool.id,
        whereTrails: {
          participantAccountId: participantAccount.id,
          date: {
            [Op.eq]: startOfDay(subDays(new Date(startDate), 1)),
          },
        },
        whereCompany: { id: participantAccount.participant.companyId },
      });

      const sortedTrails = trails.sort((a, b) => new Date(a.date) - new Date(b.date));
      const initialBalanceRaw = initialBalanceTrail.length > 0 ? initialBalanceTrail[0].balance : null;
      const endingBalanceRaw = sortedTrails.length > 0 ? sortedTrails[sortedTrails.length - 1].balance : null;
      const averageBalanceRaw =
        sortedTrails.length > 0
          ? sortedTrails.reduce((sum, trail) => sum + (trail.balance || 0), 0) / sortedTrails.length
          : null;

      const initialBalance = roundTo(initialBalanceRaw, 4);
      const endingBalance = roundTo(endingBalanceRaw, 4);
      const averageBalance = roundTo(averageBalanceRaw, 4);

      const interestPayment = payment.interestPayable || payment.interestReceivable;
      let effectivePeriodInterestRate = 0;
      if (interestPayment && averageBalance) {
        effectivePeriodInterestRate = roundTo(interestPayment / averageBalance, 6);
      }

      return {
        ...payment.toJSON(),
        trailsData: {
          initialBalance,
          endingBalance,
          averageBalance,
          effectivePeriodInterestRate,
        },
      };
    }),
  );

  res.json({ payments: paymentsWithTrails, totalNumberOfPayments });
};

const exportCashPoolPayments = async (req, res) => {
  const { clientId } = req.user;
  const { columns } = req.query;
  const order = getOrderBy(req.query.sort) || orderByEndDate;
  const [paymentFilter, batchFilter, creditorFilter, debtorFilter] = getFilterBy(req.query);

  const client = await clientRepository.getClient(clientId);

  const payments = await cashPoolBatchPaymentsRepository.getCashPoolPayments({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
    order,
  });

  const paymentsWithTrails = await Promise.all(
    payments.map(async (payment) => {
      const { batch, participantAccount } = payment;
      const { startDate, endDate } = batch;

      const trails = await participantAccountTrailsRepository.getCashPoolTrails({
        cashPoolId: batch.cashPool.id,
        whereTrails: {
          participantAccountId: participantAccount.id,
          date: {
            [Op.between]: [startDate, endDate],
          },
        },
        whereCompany: { id: participantAccount.participant.companyId },
      });

      const initialBalanceTrail = await participantAccountTrailsRepository.getCashPoolTrails({
        cashPoolId: batch.cashPool.id,
        whereTrails: {
          participantAccountId: participantAccount.id,
          date: {
            [Op.eq]: startOfDay(subDays(new Date(startDate), 1)),
          },
        },
        whereCompany: { id: participantAccount.participant.companyId },
      });

      const sortedTrails = trails.sort((a, b) => new Date(a.date) - new Date(b.date));
      const initialBalanceRaw = initialBalanceTrail.length > 0 ? initialBalanceTrail[0].balance : null;
      const endingBalanceRaw = sortedTrails.length > 0 ? sortedTrails[sortedTrails.length - 1].balance : null;
      const averageBalanceRaw =
        sortedTrails.length > 0
          ? sortedTrails.reduce((sum, trail) => sum + (trail.balance || 0), 0) / sortedTrails.length
          : null;

      const initialBalance = roundTo(initialBalanceRaw, 4);
      const endingBalance = roundTo(endingBalanceRaw, 4);
      const averageBalance = roundTo(averageBalanceRaw, 4);

      const interestPayment = payment.interestPayable || payment.interestReceivable;
      let effectivePeriodInterestRate = 0;
      if (interestPayment && averageBalance) {
        effectivePeriodInterestRate = roundTo(interestPayment / averageBalance, 6);
      }

      return {
        ...payment.toJSON(),
        trailsData: {
          initialBalance,
          endingBalance,
          averageBalance,
          effectivePeriodInterestRate,
        },
      };
    }),
  );

  const sheetData = getCashPoolPaymentsSheetData(paymentsWithTrails, columns.split(', '), client.name);
  const data = [{ sheetData, sheetName: 'Cash Pool Interest' }];
  const result = jsonToSheet(data);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Cash Pool Interest.xlsx"');
  res.send(result);
};

/** Marks payment as paid and updates status of batch */
const markCashPoolPaymentAsPaid = async (req, res) => {
  const { cashPoolId, paymentId, batchId } = req.params;
  const { clientId } = req.user;
  const { isPaid, valueDate, statementDate } = req.body;

  await sequelize.transaction(async () => {
    const paymentWhere = { cashPoolId, clientId, batchId, paymentId };
    const payment = await cashPoolBatchPaymentsRepository.getCashPoolPayment(paymentWhere);

    if (!payment) throw new NotFoundError('Payment');
    if (isPaid === false && payment.statementData?.cashPoolBatchId != null) {
      throw new BadRequestError('Cannot mark interest as unpublished whose interest is used in batch.');
    }

    await cashPoolBatchPaymentsRepository.updateCashPoolPayment({ id: paymentId }, { isPaid });

    const { interestPayable, interestReceivable, debtor, creditor } = payment;

    const account = interestPayable !== null ? debtor.accounts[0] : creditor.accounts[0];
    const balanceChange = interestPayable !== null ? -interestPayable : interestReceivable;

    if (account.generateInterestStatementData) {
      if (isPaid) {
        await cashPoolStatementDataRepository.addStatementData({
          date: valueDate,
          statementDate: statementDate,
          balanceChange,
          cashPoolAccountId: account.id,
          transactionReferenceNumber: statementUtils.consts.INTEREST,
          statementNumber: statementUtils.consts.INTEREST,
          cashPoolPaymentId: paymentId,
        });
      } else {
        await cashPoolStatementDataRepository.deleteStatement({ cashPoolPaymentId: paymentId });
      }
    }

    const paymentCountData = { clientId, whereBatch: { id: batchId } };
    const [totalBatchPaymentsCount, paidBatchPaymentsCount] = await Promise.all([
      cashPoolBatchPaymentsRepository.getTotalPaymentsCount(paymentCountData),
      cashPoolBatchPaymentsRepository.getPaidPaymentsCount(paymentCountData),
    ]);

    const status = getBatchStatus(paidBatchPaymentsCount, totalBatchPaymentsCount);
    await cashPoolBatchRepository.updateCashPoolBatch(batchId, { status });

    res.send();
  });
};

module.exports = {
  getCashPoolPayments: asyncControllerWrapper(getCashPoolPayments),
  exportCashPoolPayments: asyncControllerWrapper(exportCashPoolPayments),
  markCashPoolPaymentAsPaid: asyncControllerWrapper(markCashPoolPaymentAsPaid),
};
