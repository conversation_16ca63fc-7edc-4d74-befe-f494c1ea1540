import { Request, Response } from 'express';
import _ from 'lodash';

import models from '../models';
import { cuftDataRepository } from '../repositories';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { cuftDataUtils } from '../utils/cuftDataUtils';
import { jsonToSheet } from '../utils/documents';
import { BadRequestError } from '../utils/ErrorHandler';
import { getCountries } from '../singletons';
import {
  CuftDataFilterBodyType,
  DbCuftDataTypeWithCreditRatingsType,
  CuftOrderByType,
  CuftDataSummaryStatisticsType,
  CuftDataSaveSearchBodyType,
  DistinctBorrowerCountriesType,
  DistinctTrancheAssetClassType,
  DistinctCurrenciesType,
} from '../types';
const { sequelize } = models;

const getCuftData = async (req: Request<never, never, CuftDataFilterBodyType>, res: Response) => {
  const { offset = 0, limit = 10 } = req.body;

  const { countriesByName } = await getCountries();
  const { cuftDataFilter, currenciesFilter, creditRatingsFilter } = cuftDataUtils.getCuftDataFilters(
    req.body,
    countriesByName,
  );

  const order: CuftOrderByType = cuftDataUtils.getOrderBy(req.body.sortBy);

  const cuftDataDbRaw: DbCuftDataTypeWithCreditRatingsType[] = await cuftDataRepository.getCuftData(
    cuftDataFilter,
    creditRatingsFilter,
    currenciesFilter,
    order,
  );

  const numberOfObservations = cuftDataDbRaw.length;

  const interests: Array<number> = cuftDataDbRaw.map((singleData) => singleData.creditRatings.creditRatingValue);

  const summaryStatistics: CuftDataSummaryStatisticsType = cuftDataUtils.getSummaryStatistics(
    interests,
    numberOfObservations,
  );

  const start = offset * limit;
  const end = (offset + 1) * limit;

  res.json({
    totalNumberOfCuftData: numberOfObservations,
    cuftData: cuftDataDbRaw.slice(start, end),
    summaryStatistics,
  });
};

const getAvailableFilteringOptions = async (req: Request<never, never, CuftDataFilterBodyType>, res: Response) => {
  const { countries, countriesByName } = await getCountries();
  const { cuftDataFilter, currenciesFilter, creditRatingsFilter } = cuftDataUtils.getCuftDataFilters(
    req.body,
    countriesByName,
  );
  /**
   * Omitted so it doesn't just return the one selected country/asset class.
   * Without this after picking one country/asset class just that one country/asset class would appear as an available option.
   * It's different for currency because that is in another table.
   */
  const cuftDataFilterWithoutBorrowerCountry = _.omit(cuftDataFilter, 'cuftBorrowerCountry');
  const cuftDataFilterWithoutTrancheAssetClass = _.omit(cuftDataFilter, 'cuftTrancheAssetClass');

  const [uniqueBorrowerCountries, uniqueTrancheAssetClasses, uniqueCurrencies]: [
    Array<DistinctBorrowerCountriesType>,
    Array<DistinctTrancheAssetClassType>,
    Array<DistinctCurrenciesType>,
  ] = await Promise.all([
    cuftDataRepository.getUniqueBorrowerCountries(
      cuftDataFilterWithoutBorrowerCountry,
      creditRatingsFilter,
      currenciesFilter,
    ),
    cuftDataRepository.getUniqueTrancheAssetClass(
      cuftDataFilterWithoutTrancheAssetClass,
      creditRatingsFilter,
      currenciesFilter,
    ),
    cuftDataRepository.getUniqueCurrencies(cuftDataFilter, creditRatingsFilter),
  ]);

  res.json({
    uniqueBorrowerCountries: uniqueBorrowerCountries.map(
      (country) => countries.find((c) => c.cuftCode === country.distinctBorrowerCountries)?.name,
    ),
    uniqueTrancheAssetClasses: uniqueTrancheAssetClasses.map((t) => t.distinctTrancheAssetClasses),
    uniqueCurrencies: uniqueCurrencies.map((currency) => currency.currencies.distinctCurrencies),
  });
};

const exportCuftData = async (req: Request<never, never, CuftDataFilterBodyType>, res: Response) => {
  const MAX_NUMBER_PER_EXPORT = 50;
  const MAX_NUMBER_OF_EXPORTS_IN_A_WEEK = 500;
  const userId = req.user?.id!;

  const { countriesByName } = await getCountries();
  const { cuftDataFilter, currenciesFilter, creditRatingsFilter } = cuftDataUtils.getCuftDataFilters(
    req.body,
    countriesByName,
  );

  const order: CuftOrderByType = cuftDataUtils.getOrderBy(req.body.sortBy);

  await sequelize.transaction(async () => {
    const numberOfExportInLastWeek = await cuftDataRepository.getNumberOfCuftDataExportsInLastWeek(userId);
    if (numberOfExportInLastWeek > MAX_NUMBER_OF_EXPORTS_IN_A_WEEK) {
      throw new BadRequestError('Maximum number of export done for this week.');
    }
    await cuftDataRepository.createCuftDataExportCounters(userId);

    const cuftDataDbRaw: DbCuftDataTypeWithCreditRatingsType[] = await cuftDataRepository.getCuftData(
      cuftDataFilter,
      creditRatingsFilter,
      currenciesFilter,
      order,
    );

    if (cuftDataDbRaw.length > MAX_NUMBER_PER_EXPORT) {
      throw new BadRequestError(`Maximum number of agreements per export is ${MAX_NUMBER_PER_EXPORT}.`);
    }

    const sheetData = cuftDataUtils.getCuftDataSheetData(cuftDataDbRaw);
    const data = [{ sheetData, sheetName: 'Cuft Data' }];
    const result = jsonToSheet(data);

    res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.set('Content-Disposition', 'attachment; filename="Exported Cuft Data.xlsx"');
    res.send(result);
  });
};

const createCuftDataSavedSearch = async (req: Request, res: Response) => {
  const userId = req.user?.id!;
  const { name } = req.body;
  const search = _.omit(req.body, ['name']) as CuftDataSaveSearchBodyType;

  await cuftDataRepository.createCuftDataSavedSearch({ userId, name, search });

  res.send();
};

const getCuftDataSavedSearch = async (req: Request, res: Response) => {
  const userId = req.user?.id!;

  const savedSearches = await cuftDataRepository.getUserCuftDataSavedSearch(userId);

  res.json(savedSearches);
};

export default {
  getCuftData: asyncControllerWrapper(getCuftData),
  getAvailableFilteringOptions: asyncControllerWrapper(getAvailableFilteringOptions),
  exportCuftData: asyncControllerWrapper(exportCuftData),
  createCuftDataSavedSearch: asyncControllerWrapper(createCuftDataSavedSearch),
  getCuftDataSavedSearch: asyncControllerWrapper(getCuftDataSavedSearch),
};
