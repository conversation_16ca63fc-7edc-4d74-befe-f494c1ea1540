import { Request, Response } from 'express';

import {
  featureRepository,
  loanRepository,
  guaranteeRepository,
  creditRatingRepository,
  userRepository,
  cashPoolRepository,
  b2bLoanRepository,
} from '../repositories';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { ExactClientFeaturesType, FeatureNamesWithValues } from '../types';

/** Used by all users to display features they, as a client, have available */
async function getClientFeatures(req: Request, res: Response) {
  const clientId = req.user!.clientId!;
  const clientFeatures = await featureRepository.getAllClientFeatures({ clientId });

  res.json(clientFeatures);
}

/** Used by superadmin to display features of a client and to allow editing */
async function getClientFeaturesOfSpecificClient(req: Request, res: Response) {
  const clientId = Number(req.params.clientId);

  const clientFeatures = await featureRepository.getAllClientFeatures({ clientId });

  res.json(clientFeatures);
}

async function updateEntireClientFeatures(req: Request, res: Response) {
  const body: ExactClientFeaturesType = req.body;
  const { clientId } = req.params;

  const allFeatures = await featureRepository.getAllFeatures();

  const updateFeaturePromises = allFeatures.map((feature) => {
    const featureId = feature.id;
    const featureName = feature.name;
    const isEnabled = body.featureAvailability[featureName];
    const values = body[featureName as FeatureNamesWithValues];
    return featureRepository.updateClientFeatureValues({
      values: { isEnabled, values },
      where: { clientId, featureId },
    });
  });

  await Promise.all(updateFeaturePromises);
  res.send();
}

async function getClientFeatureUsedNumbers(req: Request, res: Response) {
  const clientId = req.user?.clientId!;

  const [
    usedLoansNumber,
    usedGuaranteesNumber,
    usedBackToBackLoansNumber,
    usedCreditRatingsNumber,
    usedUsersNumber,
    usedCashPoolsNumber,
  ] = await Promise.all([
    loanRepository.getLoansCount(clientId),
    guaranteeRepository.getGuaranteesCount(clientId),
    b2bLoanRepository.getLoansCount(clientId),
    creditRatingRepository.getCreditRatingsCount(clientId),
    userRepository.getUsersCount(clientId),
    cashPoolRepository.getCashPoolCount(clientId),
  ]);

  res.json({
    usedLoansNumber,
    usedGuaranteesNumber,
    usedBackToBackLoansNumber,
    usedCreditRatingsNumber,
    usedUsersNumber,
    usedCashPoolsNumber,
  });
}

export default {
  getClientFeatures: asyncControllerWrapper(getClientFeatures),
  getClientFeaturesOfSpecificClient: asyncControllerWrapper(getClientFeaturesOfSpecificClient),
  updateEntireClientFeatures: asyncControllerWrapper(updateEntireClientFeatures),
  getClientFeatureUsedNumbers: asyncControllerWrapper(getClientFeatureUsedNumbers),
};
