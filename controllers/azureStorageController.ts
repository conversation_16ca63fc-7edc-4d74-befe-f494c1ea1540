import { Request, Response } from 'express';

import models from '../models';
import * as azureService from '../services/azureService';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';
import { capitalize } from '../utils/strings';
import { ReportValueType } from '../types';
const { sequelize } = models;

const getFile = async (
  req: Request<{ fileId: number; reportType: ReportValueType }, {}, {}, { type?: 'pdf' }>,
  res: Response,
) => {
  const { fileId, reportType } = req.params;
  const { type } = req.query;

  const getFileDb = azureService.repositoryStrategies.getFileGetter(reportType);

  const reportFile = await getFileDb(fileId);
  if (!reportFile) throw new NotFoundError(capitalize(reportType));

  const fileBuffer = await azureService.getFile(`${reportType}/${fileId}`);

  const { buffer, contentType, contentDisposition } = await azureService.handleFileConversion(
    type,
    fileBuffer,
    reportFile,
  );

  res.set('Content-Type', contentType);
  res.set('Content-Disposition', contentDisposition);
  res.send(buffer);
};

const uploadFile = async (
  req: Request<
    { reportId: number; reportType: ReportValueType },
    {},
    {},
    { label: string; newName: string; status: string }
  >,
  res: Response,
) => {
  if (!req.file) throw new BadRequestError('Upload file missing');

  const { reportId, reportType } = req.params;
  const clientId = req.user?.clientId!;
  const { buffer, size } = req.file;

  const [getReportDb, createReportFileDb] = azureService.repositoryStrategies.getFileGetterAndCreator(reportType);

  const report = await getReportDb(reportId, clientId);
  if (!report) throw new NotFoundError(capitalize(reportType));

  await sequelize.transaction(async () => {
    const { dataValues } = await createReportFileDb(req.file!, reportId, req.query);

    const fileId = dataValues.id;
    await azureService.uploadFile(`${reportType}/${fileId}`, buffer, size);
    res.json(dataValues);
  });
};

const deleteFile = async (
  req: Request<{ fileId: number; reportId: number; reportType: ReportValueType }>,
  res: Response,
) => {
  const { fileId, reportType, reportId } = req.params;
  const deleteFileDb = azureService.repositoryStrategies.getFileDelete(reportType);

  await sequelize.transaction(async () => {
    await deleteFileDb(fileId, reportId);

    await azureService.deleteFile(`${reportType}/${fileId}`);
    res.status(204).send();
  });
};

const updateFileMetadataDb = async (req: Request<{ fileId: number; reportType: ReportValueType }>, res: Response) => {
  const { fileId, reportType } = req.params;

  const updateFileDb = azureService.repositoryStrategies.getFileUpdate(reportType);

  const [affectedRows] = await updateFileDb(fileId, req.body);
  if (affectedRows === 0) throw new NotFoundError(capitalize(reportType));

  res.status(204).send();
};

export default {
  getFile: asyncControllerWrapper(getFile),
  uploadFile: asyncControllerWrapper(uploadFile),
  deleteFile: asyncControllerWrapper(deleteFile),
  updateFileMetadataDb: asyncControllerWrapper(updateFileMetadataDb),
};
