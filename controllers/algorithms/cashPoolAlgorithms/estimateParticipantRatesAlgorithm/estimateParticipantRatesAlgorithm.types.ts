import LoanCalculationLog from '../../../algorithms/reportAlgorithms/loanAlgorithm/LoanCalculationLog';
import {
  PaymentFrequencyType,
  LoanTypeType,
  LoanPricingApproachType,
  SeniorityType,
} from '../../../../types/report.types';

export type ParticipantRatesType = {
  participantId: number;
  notEstimatedDebitRate: number;
  debitRate: number | null;
  creditRate: number;
  calculationLog: LoanCalculationLog & { scaledRateLog?: string };
  scaledRateLog?: string;
};

export type RequestDataType = {
  isUsingRegionalProviderData: boolean;
  participants: any;
  issueDate: Date;
  rateType: {
    type: 'fixed' | 'float';
  };
  creditRate: number;
  pricingApproach: LoanPricingApproachType;
  cashPoolDebitRate: number;
  currency: string;
};

export type EstimateRequestType = {
  issueDate: Date;
  maturityDate: Date;
  paymentFrequency: PaymentFrequencyType;
  rateType: {
    type: 'fixed' | 'float';
  };
  type: LoanTypeType;
  pricingApproach: LoanPricingApproachType;
  seniority: SeniorityType;
  currency: string;
};

export type ParticipantType = {
  id: number;
  parentCompanyId: number;
  name: string;
  country: string;
  industry: string;
  creditRating: {
    rating: string;
    ratingAdj: string;
    probabilityOfDefault: number;
    probabilityOfDefaultAdj: number;
  };
};
