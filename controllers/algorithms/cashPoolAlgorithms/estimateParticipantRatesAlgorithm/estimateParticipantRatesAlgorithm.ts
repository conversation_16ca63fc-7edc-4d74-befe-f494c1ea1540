import { addMonths } from 'date-fns';

import { getProviderDataIssueDate, currencyExists } from '../../../../repositories/providerDataRepository';
import { BadRequestError } from '../../../../utils/ErrorHandler';
import { createParticipantProviderSearchData } from '../../../../utils/cashPool';
import { extractCreditRating } from '../../../../utils/reportUtils';
import { checkClientFeatureFlags } from '../../../../utils/clientUtils';
import Predictor from '../../reportAlgorithms/Predictor';
import Report from '../../reportAlgorithms/Report';
import { getOneProviderData, searchAttributes } from '../../reportAlgorithms';
import LoanCalculationLog from '../../reportAlgorithms/loanAlgorithm/LoanCalculationLog';
import { roundToXDecimals } from '../../../../utils/numbers';
import { Client, dayCountEnums } from '../../../../enums';
import {
  ParticipantType,
  EstimateRequestType,
  RequestDataType,
  ParticipantRatesType,
} from './estimateParticipantRatesAlgorithm.types';
import { ProviderSearchDataType } from '../../../../types';

type EstimateRateAlgorithmType = { participantMidPoint: number; calculationLog: LoanCalculationLog };

// This is modified version of loanAlgorithm function for estimating participants rates
const loanAlgorithmModifiedForParticipantRateEstimation = async ({
  estimateRequest,
  providerDataIssueDate,
  isUsingRegionalProviderData,
  participant,
}: {
  estimateRequest: EstimateRequestType;
  providerDataIssueDate: Date;
  isUsingRegionalProviderData: boolean;
  participant: ParticipantType;
}) => {
  const calculationLog = new LoanCalculationLog();
  calculationLog.addProviderDataIssueDateLog(providerDataIssueDate);

  const report = new Report(
    { startDate: estimateRequest.issueDate, endDate: estimateRequest.maturityDate },
    calculationLog,
  );

  const attributes: string[] = searchAttributes(report);
  const participantCreditRating: string = extractCreditRating(
    participant.creditRating,
    estimateRequest.pricingApproach,
  );

  const { participantSearchData }: { participantSearchData: ProviderSearchDataType } =
    createParticipantProviderSearchData({
      estimateRequest,
      participant,
      providerDataIssueDate,
      isUsingRegionalProviderData,
    });

  const participantProviderData = await getOneProviderData(participantSearchData, attributes);

  const participantPredictor = new Predictor({
    searchData: participantSearchData,
    alias: 'Participant',
    type: estimateRequest.type,
    dayCount: dayCountEnums.dayCountMapper['ACT/365'],
  });
  await participantPredictor.initializeInterpolationData(
    participantProviderData,
    estimateRequest.paymentFrequency,
    report,
    estimateRequest.rateType,
  );

  const participantMidPoint = roundToXDecimals(
    report.calculateMidPoint(participantPredictor, participantCreditRating, 'participant points'),
  );

  report.calculationLog.addPricingApproachLog({ participantMidPoint });

  return { participantMidPoint, calculationLog };
};

const getScaledCashPoolDebitRateLogLine = (cashPoolDebitRate: number, CASH_POOL_DEBIT_RATE_SCALE_FACTOR: number) => {
  return `Cash pool's Scaled Debit Rate = Cash Pool's Debit Rate * CASH_POOL_DEBIT_RATE_SCALE_FACTOR (${cashPoolDebitRate} * ${CASH_POOL_DEBIT_RATE_SCALE_FACTOR})`;
};

const setDebitRateLessThanZeroLog = (
  participant: ParticipantRatesType,
  cashPoolDebitRate: number,
  CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET: number,
) => {
  participant.calculationLog.scaledRateLog = `
    Participant's Debit Rate <= 0.
    (${participant.notEstimatedDebitRate} <= 0)
    Debit rate calculated using the following formula: Cash Pool's Debit Rate + CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET
    "Debit Rate: ${participant.debitRate} = ${cashPoolDebitRate} + ${CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET}"
  `;
};

const setDebitRateLessThanScaledDebitRateLog = (
  participant: ParticipantRatesType,
  cashPoolDebitRate: number,
  cashPoolScaledDebitRate: number,
  CASH_POOL_DEBIT_RATE_SCALE_FACTOR: number,
) => {
  participant.calculationLog.scaledRateLog = `
    ${getScaledCashPoolDebitRateLogLine(cashPoolDebitRate, CASH_POOL_DEBIT_RATE_SCALE_FACTOR)}
    Participant's Debit Rate <= Cash Pool's Scaled Debit Rate.
    (${participant.notEstimatedDebitRate} <= ${cashPoolScaledDebitRate})
    Debit rate calculated using the following formula: (participant.notEstimatedDebitRate / cashPoolScaledDebitRate) * (cashPoolScaledDebitRate - cashPoolDebitRate) + cashPoolDebitRate.
    "Debit Rate: ${participant.debitRate} = (${
    participant.notEstimatedDebitRate
  } / ${cashPoolScaledDebitRate}) * (${cashPoolScaledDebitRate} - ${cashPoolDebitRate}) + ${cashPoolDebitRate}"
  `;
};

const setDefaultDebitRateLog = (
  participant: ParticipantRatesType,
  cashPoolDebitRate: number,
  cashPoolScaledDebitRate: number,
  PARTICIPANT_RATE_SCALE_FACTOR: number,
  CASH_POOL_DEBIT_RATE_SCALE_FACTOR: number,
) => {
  participant.calculationLog.scaledRateLog = `
    ${getScaledCashPoolDebitRateLogLine(cashPoolDebitRate, CASH_POOL_DEBIT_RATE_SCALE_FACTOR)}
    Participant's Debit Rate > Cash Pool's Scaled Debit Rate
    (${participant.notEstimatedDebitRate} > ${cashPoolScaledDebitRate})
    Debit rate calculated using the following formula: (participant.notEstimatedDebitRate - cashPoolScaledDebitRate) * PARTICIPANT_RATE_SCALE_FACTOR + cashPoolScaledDebitRate
    "Debit Rate: ${participant.debitRate} = (${
    participant.notEstimatedDebitRate
  } - ${cashPoolScaledDebitRate}) * ${PARTICIPANT_RATE_SCALE_FACTOR} + ${cashPoolScaledDebitRate}"
  `;
};

export async function estimateParticipantRatesAlgorithm(
  data: RequestDataType,
  clientName?: string,
): Promise<ParticipantRatesType[]> {
  const { participants, isUsingRegionalProviderData, creditRate, pricingApproach, cashPoolDebitRate, currency } = data;

  // TODO noel: quick fix, we need to support AED, closes rate is USD (needed for Gunvor)
  const updatedCurrency = currency === 'AED' ? 'USD' : currency;

  const estimateRequest: EstimateRequestType = {
    issueDate: new Date(data.issueDate),
    maturityDate: addMonths(new Date(data.issueDate), 3),
    paymentFrequency: 'Monthly',
    rateType: data.rateType,
    type: 'Bullet',
    pricingApproach,
    seniority: 'Senior Secured',
    currency: updatedCurrency,
  };

  const providerDataIssueDate = await getProviderDataIssueDate(estimateRequest.issueDate);
  if (!providerDataIssueDate) throw new BadRequestError('Cannot estimate participant rates for selected date.');

  const isValidCurrency = await currencyExists(updatedCurrency);
  if (!isValidCurrency)
    throw new BadRequestError('No data matches the selected currency. Input the credit and debit rates manually.');

  const estimateRates: Array<EstimateRateAlgorithmType> = await Promise.all(
    participants.map((participant: any) =>
      loanAlgorithmModifiedForParticipantRateEstimation({
        estimateRequest,
        providerDataIssueDate,
        isUsingRegionalProviderData,
        participant,
      }),
    ),
  );

  const participantsRates: Array<ParticipantRatesType> = estimateRates.map(
    ({ participantMidPoint, calculationLog }, index) => ({
      participantId: participants[index].id,
      notEstimatedDebitRate: participantMidPoint,
      debitRate: null,
      creditRate,
      calculationLog,
    }),
  );

  const CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET = estimateRequest.rateType.type === 'fixed' ? 0.01 : 1;
  const CASH_POOL_DEBIT_RATE_SCALE_FACTOR = 1.1;
  const PARTICIPANT_RATE_SCALE_FACTOR = 0.25;

  // Scale benchmarked participant rates
  for (const participant of participantsRates) {
    if (participant.notEstimatedDebitRate <= 0) {
      participant.debitRate = cashPoolDebitRate + CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET;
      setDebitRateLessThanZeroLog(participant, cashPoolDebitRate, CASH_POOL_DEBIT_RATE_MINIMUM_OFFSET);
    } else {
      const cashPoolScaledDebitRate = cashPoolDebitRate * CASH_POOL_DEBIT_RATE_SCALE_FACTOR;
      if (participant.notEstimatedDebitRate <= cashPoolScaledDebitRate) {
        participant.debitRate =
          (participant.notEstimatedDebitRate / cashPoolScaledDebitRate) *
            (cashPoolScaledDebitRate - cashPoolDebitRate) +
          cashPoolDebitRate;
        setDebitRateLessThanScaledDebitRateLog(
          participant,
          cashPoolDebitRate,
          cashPoolScaledDebitRate,
          CASH_POOL_DEBIT_RATE_SCALE_FACTOR,
        );
      } else {
        if (clientName && checkClientFeatureFlags(Client.GUNVOR, clientName)) {
          participant.debitRate = participant.notEstimatedDebitRate;
        } else {
          participant.debitRate =
            (participant.notEstimatedDebitRate - cashPoolScaledDebitRate) * PARTICIPANT_RATE_SCALE_FACTOR +
            cashPoolScaledDebitRate;
        }
        setDefaultDebitRateLog(
          participant,
          cashPoolDebitRate,
          cashPoolScaledDebitRate,
          PARTICIPANT_RATE_SCALE_FACTOR,
          CASH_POOL_DEBIT_RATE_SCALE_FACTOR,
        );
      }
    }
  }

  return participantsRates;
}
