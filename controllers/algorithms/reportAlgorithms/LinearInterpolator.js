class LinearInterpolator {
  data; // [[0.08, data1], [0.16, data2]] example

  constructor(data) {
    this.data = data;
  }

  interpolate(x) {
    const secondPointIndex = this.data.findIndex((dataPoint) => x < dataPoint[0]);
    const firstPointIndex = secondPointIndex - 1;

    const d1 = this.data[secondPointIndex];
    const d0 = this.data[firstPointIndex];

    return d0[1] + (x - d0[0]) * ((d1[1] - d0[1]) / (d1[0] - d0[0]));
  }
}

module.exports = LinearInterpolator;
