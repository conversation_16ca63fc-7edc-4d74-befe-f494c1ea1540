const providerDataRepository = require('../../../repositories/providerDataRepository');
const { getHigherAndLowerCreditRating } = require('../../../utils/creditRatingUtils');
const { BadRequestError } = require('../../../utils/ErrorHandler');
const { tenors, tenorFloatToString } = require('../../../utils/reportUtils');

// If Tenor is not rounded then fetch the closest higher and lower tenor
function searchAttributes(report) {
  let attributes = [];
  const searchTenor = report.closestHigherTenor ? report.closestHigherTenor : report.tenor;
  for (let i = 0, curr = tenors[0]; curr <= searchTenor.floatValue; ++i, curr = tenors[i]) {
    attributes.push(`float${tenorFloatToString[curr]}`);
    attributes.push(`yield${tenorFloatToString[curr]}`);
    attributes.push(`discount${tenorFloatToString[curr]}`);
  }
  return attributes;
}

async function getProviderData(searchData1, searchData2, attributes) {
  const [providerData1, providerData2] = await Promise.all([
    providerDataRepository.getProviderData(searchData1, attributes),
    providerDataRepository.getProviderData(searchData2, attributes),
  ]);

  if (providerData1.length === 0 || providerData2 === 0) {
    throw new BadRequestError('No data matches the selected parameters. Choose a different date.');
  }

  return [_parseProviderData(providerData1), _parseProviderData(providerData2)];
}

// Used for estimating participant rates
async function getOneProviderData(searchData, attributes) {
  const providerData = await providerDataRepository.getProviderData(searchData, attributes);
  if (providerData.length === 0) {
    throw new BadRequestError('No data matches the selected parameters. Choose a different date.');
  }

  return _parseProviderData(providerData);
}

// Calculate the midPoint of the one credit rating higher and one credit rating lower
function calculateDataPointsOfSurroundingCreditRatings({ predictor, report, midPoint, creditRating, aliases }) {
  const { lowerCreditRating, higherCreditRating } = getHigherAndLowerCreditRating(creditRating);
  let lowerCreditRatingMidPoint, higherCreditRatingMidPoint;
  if (lowerCreditRating) {
    lowerCreditRatingMidPoint = report.calculateMidPoint(predictor, lowerCreditRating, aliases?.lowerPoints);
  }
  if (higherCreditRating) {
    higherCreditRatingMidPoint = report.calculateMidPoint(predictor, higherCreditRating, aliases?.upperPoints);
  }

  if (!lowerCreditRatingMidPoint) {
    lowerCreditRatingMidPoint = midPoint - higherCreditRatingMidPoint + midPoint;
    report.calculationLog.addMissingMidPointLog({
      alias: aliases?.lowerPoints,
      midPoint,
      otherMidPoint: higherCreditRatingMidPoint,
      missingMidPoint: lowerCreditRatingMidPoint,
    });
  } else if (!higherCreditRatingMidPoint) {
    higherCreditRatingMidPoint = midPoint - lowerCreditRatingMidPoint + midPoint;
    report.calculationLog.addMissingMidPointLog({
      alias: aliases?.upperPoints,
      midPoint,
      otherMidPoint: lowerCreditRatingMidPoint,
      missingMidPoint: higherCreditRatingMidPoint,
    });
  }

  return [lowerCreditRatingMidPoint, midPoint, higherCreditRatingMidPoint];
}

// Reformat the data from the database
function _parseProviderData(providerData) {
  const data = {};
  providerData.forEach((pd) => {
    const { rating, ...rest } = pd.dataValues;
    data[rating] = rest;
  });

  return data;
}

module.exports = {
  calculateDataPointsOfSurroundingCreditRatings,
  getProviderData,
  searchAttributes,
  getOneProviderData,
};
