const { formatDate } = require('../../../utils/dates');
const { calculateTenor } = require('../../../utils/dates');
const { roundToXDecimals } = require('../../../utils/numbers');
const { tenors } = require('./../../../utils/reportUtils');
const Tenor = require('./Tenor');

// Only using floatXX data points
class Report {
  data;
  calculationLog;
  tenor;
  closestLowerTenor;
  closestHigherTenor;

  constructor(data, calculationLog) {
    this.data = data;
    this.calculationLog = calculationLog;

    this._calculateTenor(this.data.startDate, this.data.endDate);
    if (!tenors.includes(this.tenor.floatValue)) {
      this._calculateClosestTenors();
    }
  }

  /**
   * This small number was added to fix the bug that came from having an round Tenor number.
   * Previously it wouldn't happen because there was a difference in time between issue date and end date
   * even though it was the same date only a different year, but after changing some things on the frontend
   * this was no longer the case. Because of a round Tenor _calculateClosestTenors function wouldn't set a
   * closest lower and higher tenor and it would appear as n/a in the Reports file. Previously the tenor between
   * for example 1.1.2022. and 1.1.2027 would be 5.0000347 so I don't think this newly introduced "mistake"
   * will have an effect on the outcome of the algorithm.
   */
  _calculateTenor(startDate, endDate) {
    const tenor = calculateTenor(startDate, endDate) + 0.00000001;

    this.calculationLog.addTenorLog(formatDate(startDate), formatDate(endDate), tenor);

    this.tenor = new Tenor(tenor);
  }

  _calculateClosestTenors() {
    let closestLowerTenor, closestHigherTenor;
    if (this.tenor.floatValue < tenors[0]) {
      this.tenor = new Tenor(tenors[0]);
    } else if (this.tenor.floatValue > tenors[tenors.length - 1]) {
      this.tenor = new Tenor(tenors[tenors.length - 1]);
    } else {
      for (let i = 0, len = tenors.length; i < len; i++) {
        if (tenors[i] > this.tenor.floatValue) {
          closestLowerTenor = tenors[i - 1];
          closestHigherTenor = tenors[i];
          break;
        }
      }
      this.closestLowerTenor = new Tenor(closestLowerTenor);
      this.closestHigherTenor = new Tenor(closestHigherTenor);

      this.calculationLog.addClosestTenorLog(this.closestLowerTenor, this.closestHigherTenor);
    }
  }

  _calculateTenorAdjustment(dataPoint, alias) {
    const lowerBasePoint = dataPoint[`float${this.closestLowerTenor.stringValue}`];
    const higherBasePoint = dataPoint[`float${this.closestHigherTenor.stringValue}`];

    let tenorAdjustment =
      (this.tenor.floatValue - this.closestLowerTenor.floatValue) *
      ((higherBasePoint - lowerBasePoint) / (this.closestHigherTenor.floatValue - this.closestLowerTenor.floatValue));

    this.calculationLog.addTenorAdjustmentLog(alias, this, higherBasePoint, lowerBasePoint, tenorAdjustment);

    return tenorAdjustment;
  }

  calculateMidPoint(predictor, creditRating, alias) {
    let midPoint;
    const dataPoint = predictor.getDataPoint(creditRating); // { 'float3M': ... , 'float6M':... } example
    if (this.closestHigherTenor && this.closestLowerTenor) {
      const basePoint = dataPoint[`float${this.closestLowerTenor.stringValue}`];
      const tenorAdjustment = this._calculateTenorAdjustment(dataPoint, alias);
      midPoint = basePoint + tenorAdjustment;

      this.calculationLog.addMidPointAdjustedLog({
        alias,
        basePoint,
        tenorAdjustment,
        midPoint,
      });
    } else {
      midPoint = dataPoint[`float${this.tenor.stringValue}`];

      this.calculationLog.addMidPointLog({ alias, midPoint });
    }

    return midPoint;
  }

  getFinalDataPoints(sortedDataPoints) {
    return {
      lowerBound: roundToXDecimals(sortedDataPoints[0]),
      midPoint: roundToXDecimals(sortedDataPoints[1]),
      upperBound: roundToXDecimals(sortedDataPoints[2]),
    };
  }
}

module.exports = Report;
