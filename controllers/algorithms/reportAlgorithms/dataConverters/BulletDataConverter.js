const { findRoot } = require('../../../../services/solverService');
const { tenors } = require('../../../../utils/reportUtils');
const LinearInterpolator = require('../LinearInterpolator');
const Tenor = require('../Tenor');
const DataConverter = require('./DataConverter');

class BulletDataConverter extends DataConverter {
  creditDiscounts;

  constructor(props) {
    super(props);
    this.creditDiscounts = this._initializeCreditDiscounts();
  }

  async convertFloat(tenor) {
    const tenorIndex = this.paymentFrequencyParameters.multiplier * tenor.floatValue;

    const sumZDeltaLs = this._calculateSumZDeltaLs(tenorIndex);
    const sumZDeltas = this._calculateSumZDeltas(tenorIndex);

    const q = (1 - this.creditDiscounts[tenorIndex - 1] - sumZDeltaLs) / sumZDeltas;

    return q * this.dayCountInterestMultiplier * 10000;
  }

  async convertFixed(tenor) {
    const tenorIndex = this.paymentFrequencyParameters.multiplier * tenor.floatValue;
    const tenorCreditDiscounts = this.creditDiscounts.slice(0, tenorIndex);
    const q = await findRoot('fixed', 'bullet', {
      creditDiscounts: tenorCreditDiscounts,
      paymentFrequency: this.paymentFrequency,
    });

    return q * this.dayCountInterestMultiplier * 100;
  }

  // Initialize credit discounts. For that interpolate all the missing zSpreads
  _initializeCreditDiscounts() {
    const creditDiscounts = [];
    const interpolatedZSpreads = [];
    const xDataToPredict = [];
    let startingIterator;

    if (this.paymentFrequency === 'Monthly') {
      const initialTenorFloatValue = parseFloat(this.paymentFrequencyParameters.step.toFixed(2)); // 0.08
      const initialInterpolatedZSpread = this.data['float3M'] / 3; // For the 1 M set the value of 3M / 3
      interpolatedZSpreads.push([initialTenorFloatValue, initialInterpolatedZSpread]);
      creditDiscounts.push([
        initialTenorFloatValue,
        this.riskFreeData[0][1] * Math.exp((-initialInterpolatedZSpread / 10000) * initialTenorFloatValue),
      ]);
      startingIterator = 1;
    } else {
      startingIterator = 0;
    }

    for (let i = startingIterator, len = this.T.length; i < len; i++) {
      const tenorObject = new Tenor(this.T[i]);
      if (tenors.includes(tenorObject.floatValue)) {
        interpolatedZSpreads.push([tenorObject.floatValue, this.data[`float${tenorObject.stringValue}`]]); // [[0.08, zSpread]]
        creditDiscounts.push([
          tenorObject.floatValue,
          this.riskFreeData[i][1] *
            Math.exp((-this.data[`float${tenorObject.stringValue}`] / 10000) * tenorObject.floatValue),
        ]);
      } else {
        xDataToPredict.push([i, tenorObject.floatValue]); // [[i, 0.16]]
      }
    }

    if (xDataToPredict.length !== 0) {
      const linearInterpolator = new LinearInterpolator(interpolatedZSpreads);
      xDataToPredict.forEach((xDataObject) => {
        creditDiscounts.push([
          xDataObject[1],
          this.riskFreeData[xDataObject[0]][1] *
            Math.exp((-linearInterpolator.interpolate(xDataObject[1]) / 10000) * xDataObject[1]),
        ]);
      });

      creditDiscounts.sort((a, b) => a[0] - b[0]);
    }

    return creditDiscounts.map((creditDiscountObject) => creditDiscountObject[1]);
  }

  _calculateSumZDeltaLs(tenorIndex) {
    let sumZDeltaLs = 0;
    for (let i = 0; i < tenorIndex; i++) {
      sumZDeltaLs += this.paymentFrequencyParameters.step * this.simpleRates[i] * this.creditDiscounts[i];
    }

    return sumZDeltaLs;
  }

  _calculateSumZDeltas(tenorIndex) {
    let sumZDeltas = 0;
    for (let i = 0; i < tenorIndex; i++) {
      sumZDeltas += this.paymentFrequencyParameters.step * this.creditDiscounts[i];
    }

    return sumZDeltas;
  }
}

module.exports = BulletDataConverter;
