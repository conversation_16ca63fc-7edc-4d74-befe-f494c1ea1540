const { findRoot } = require('../../../../services/solverService');
const DataConverter = require('./DataConverter');

class BalloonDataConverter extends DataConverter {
  constructor(props) {
    super(props);
  }

  async convertFloat(tenor) {
    const tenorIndex = this.paymentFrequencyParameters.multiplier * tenor.floatValue;

    const q = await findRoot('float', 'balloon', {
      T: this.T.slice(0, tenorIndex),
      simpleRates: this.simpleRates.slice(0, tenorIndex),
      creditDiscount: this.calculateTenorCreditDiscount(tenor),
    });

    return q * this.dayCountInterestMultiplier * 10000;
  }

  async convertFixed(tenor) {
    const q = await findRoot('fixed', 'balloon', {
      creditDiscount: this.calculateTenorCreditDiscount(tenor),
      paymentFrequency: this.paymentFrequency,
      tenorFloatValue: tenor.floatValue,
    });

    return q * this.dayCountInterestMultiplier * 100;
  }

  calculateTenorCreditDiscount(tenor) {
    const tenorRiskFreeData = this.riskFreeData.find(
      // Find the risk free data of the target tenor
      (riskFreeDataObject) => riskFreeDataObject[0] === tenor.floatValue,
    );
    return tenorRiskFreeData[1] * Math.exp((-this.data[`float${tenor.stringValue}`] / 10000) * tenor.floatValue);
  }
}

module.exports = BalloonDataConverter;
