const { tenors } = require('../../../../utils/reportUtils');
const LinearInterpolator = require('../LinearInterpolator');
const Tenor = require('./../Tenor');

const { dayCountEnums } = require('../../../../enums');

const paymentFrequencyParameters = {
  Monthly: {
    step: 1 / 12,
    multiplier: 12,
  },
  Quarterly: {
    step: 0.25,
    multiplier: 4,
  },
  'Semi-annual': {
    step: 0.5,
    multiplier: 2,
  },
  Annual: {
    step: 1,
    multiplier: 1,
  },
};

// Same object is used for converting closestLower and closestHigher tenor data
class DataConverter {
  data;
  paymentFrequency;
  tenor; // tenor to initialize data ( it is either tenor or closestHigher tenor )
  paymentFrequencyParameters;
  T; // tenor interpolations
  // These two are used for converting the zSpread
  riskFreeData; // DF-riskfree
  simpleRates;
  dayCountInterestMultiplier;

  // If there is a closestLower and closestHigher Tenor object then the tenor argument will be closestHigher Tenor object
  // The same data is used for the closestLower and closestHigher but you just sum the values differently
  constructor({ data, paymentFrequency, tenor, dayCount }) {
    this.data = data;
    this.paymentFrequency = paymentFrequency;
    this.tenor = tenor;
    this.paymentFrequencyParameters = paymentFrequencyParameters[paymentFrequency];
    this.dayCountInterestMultiplier = dayCountEnums.dayCountInterestMultiplierMapper[dayCount];
    this.T = [];

    this.riskFreeData = this._initializeRiskFreeData();
    this.simpleRates = this._calculateSimpleRates();
  }

  _initializeRiskFreeData() {
    const riskFreeData = []; // DF_{riskfree}
    const xDataToPredict = []; // x coordinates of the risk free curves to predict

    // interpolation data is to interpolate riskfree rate which is used for the DF-riskfree
    // For the existing tenors riskfree rate is the same as the risk free curve
    let startingIterator, interpolationData;
    // We don't get the value from the provider for the 1M tenor so we have to
    // initialize it to 1/3 value of the 3M tenor and use it for the interpolation
    if (this.paymentFrequency === 'Monthly') {
      startingIterator = 2 * this.paymentFrequencyParameters.step; // 0.16666666666666666
      const initialTenorFloatValue = parseFloat(this.paymentFrequencyParameters.step.toFixed(2)); // 0.08
      const initialRiskFreeCurve = (this.data['yield3M'] / 100 - this.data['float3M'] / 10000) / 3;
      interpolationData = [[initialTenorFloatValue, initialRiskFreeCurve]]; // [[0.08, initialRiskFreeCurve]]
      this.T.push(initialTenorFloatValue); // [0.08]
      riskFreeData.push([initialTenorFloatValue, Math.exp(-initialRiskFreeCurve * initialTenorFloatValue)]); // [[0.08, initialRiskFreeData]]
    } else {
      startingIterator = this.paymentFrequencyParameters.step;
      interpolationData = [];
    }

    for (
      let i = startingIterator;
      parseFloat(i.toFixed(2)) <= this.tenor.floatValue;
      i += this.paymentFrequencyParameters.step
    ) {
      const tenorObject = new Tenor(parseFloat(i.toFixed(2)));
      this.T.push(tenorObject.floatValue);
      if (tenors.includes(tenorObject.floatValue)) {
        const riskFreeCurve =
          this.data[`yield${tenorObject.stringValue}`] / 100 - this.data[`float${tenorObject.stringValue}`] / 10000;
        interpolationData.push([tenorObject.floatValue, riskFreeCurve]);
        riskFreeData.push([tenorObject.floatValue, Math.exp(-riskFreeCurve * tenorObject.floatValue)]);
      } else {
        xDataToPredict.push(tenorObject.floatValue);
      }
    }

    if (xDataToPredict.length !== 0) {
      // Predict the missing risk free curves and calculate their DF_riskfree
      const linearInterpolator = new LinearInterpolator(interpolationData);
      xDataToPredict.forEach((xData) => {
        riskFreeData.push([xData, Math.exp(-linearInterpolator.interpolate(xData) * xData)]);
      });

      // Sort the riskFreeData so that it is easy to handle later
      riskFreeData.sort((a, b) => a[0] - b[0]);
    }

    return riskFreeData;
  }

  _calculateSimpleRates() {
    const simpleRates = [this.paymentFrequencyParameters.multiplier * (1 / this.riskFreeData[0][1] - 1)]; // in the first one divide 1 from risk free point
    for (let i = 1, len = this.riskFreeData.length; i < len; i++) {
      simpleRates.push(
        this.paymentFrequencyParameters.multiplier * (this.riskFreeData[i - 1][1] / this.riskFreeData[i][1] - 1),
      );
    }

    return simpleRates;
  }
}

module.exports = DataConverter;
