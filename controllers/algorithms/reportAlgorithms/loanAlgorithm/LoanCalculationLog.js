const CalculationLog = require('../CalculationLog');

class LoanCalculationLog extends CalculationLog {
  constructor() {
    super();
  }

  addPricingApproachLog({ lenderMidPoint, borrowerMidPoint }) {
    if (lenderMidPoint > borrowerMidPoint) {
      this.log += '\nLender mid point > borrower mid point: using non-standard pricing approach.\n';
    } else {
      this.log += '\nBorrower mid point > lender mid point: using standard pricing approach\n';
    }
  }
}

module.exports = LoanCalculationLog;
