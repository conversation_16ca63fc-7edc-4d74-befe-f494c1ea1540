const { clientRepository, providerDataRepository } = require('../../../../repositories');
const { dayCountEnums } = require('../../../../enums');
const { BadRequestError } = require('../../../../utils/ErrorHandler');
const { createProviderSearchData } = require('./createProviderSearchData');
const { extractCreditRating } = require('../../../../utils/reportUtils');
const Predictor = require('../Predictor');
const Report = require('../Report');
const {
  calculateDataPointsOfSurroundingCreditRatings,
  getProviderData,
  searchAttributes,
} = require('../reportAlgorithms.utils');
const LoanCalculationLog = require('./LoanCalculationLog');

const getPricingApproach = (condition, loan) => {
  if (condition) return loan.pricingApproach.includes('stand-alone') ? 'stand-alone' : 'implicit';

  return loan.pricingApproach.includes('non-standard') ? loan.pricingApproach : loan.pricingApproach + ' non-standard';
};

module.exports = async function (data) {
  const { lender, borrower, isUsingRegionalProviderData, isApproachCalculated, ...loan } = data;
  const {
    issueDate,
    maturityDate,
    paymentFrequency,
    rateType,
    type,
    dayCount = dayCountEnums.dayCountMapper['ACT/365'],
  } = loan;

  // Fetch data by issueDate that is <= requested issue date
  const [providerDataIssueDate, { isLoanApproachCalculated }] = await Promise.all([
    providerDataRepository.getProviderDataIssueDate(loan.issueDate),
    clientRepository.getClient(loan.clientId),
  ]);
  if (!providerDataIssueDate) throw new BadRequestError('Cannot price a loan for selected issue date.');

  const calculationLog = new LoanCalculationLog();
  calculationLog.addProviderDataIssueDateLog(providerDataIssueDate);

  const lenderCreditRating = extractCreditRating(lender.creditRating, loan.pricingApproach);
  const borrowerCreditRating = extractCreditRating(borrower.creditRating, loan.pricingApproach);

  const report = new Report({ startDate: issueDate, endDate: maturityDate }, calculationLog);

  const attributes = searchAttributes(report);

  // Fetch the data for the lender and the borrower
  const { lenderSearchData, borrowerSearchData } = createProviderSearchData({
    loan,
    lender,
    borrower,
    providerDataIssueDate,
    isUsingRegionalProviderData,
  });

  const [lenderProviderData, borrowerProviderData] = await getProviderData(
    lenderSearchData,
    borrowerSearchData,
    attributes,
  );

  const lenderPredictor = new Predictor({ searchData: lenderSearchData, alias: 'Lender', type, dayCount });
  const borrowerPredictor = new Predictor({ searchData: borrowerSearchData, alias: 'Borrower', type, dayCount });
  await Promise.all([
    lenderPredictor.initializeInterpolationData(lenderProviderData, paymentFrequency, report, rateType),
    borrowerPredictor.initializeInterpolationData(borrowerProviderData, paymentFrequency, report, rateType),
  ]);

  const lenderMidPoint = report.calculateMidPoint(lenderPredictor, lenderCreditRating, 'lender points');
  const borrowerMidPoint = report.calculateMidPoint(borrowerPredictor, borrowerCreditRating, 'borrower points');

  // After calculating the mid Points we need to calculate the mid points of the
  // one credit rating higher and one credit rating lower then the lender/borrower.
  // And then of those 3 mid points we need to define the final data points
  report.calculationLog.addPricingApproachLog({ lenderMidPoint, borrowerMidPoint });

  /**
    Uses loan's isApproachCalculated if it's not undefined/null since that's what the loan was original priced as.
    Otherwise it uses the client's setting isLoanApproachCalculated.
  */
  const defaultIsLoanApproachCalculated =
    isApproachCalculated != null ? isApproachCalculated : isLoanApproachCalculated;

  const condition = lenderMidPoint <= borrowerMidPoint || !defaultIsLoanApproachCalculated;
  const dataPoints = calculateDataPointsOfSurroundingCreditRatings({
    report,
    predictor: condition ? borrowerPredictor : lenderPredictor,
    midPoint: condition ? borrowerMidPoint : lenderMidPoint,
    creditRating: condition ? borrowerCreditRating : lenderCreditRating,
    aliases: { lowerPoints: 'lower points', upperPoints: 'upper points' },
  });

  // identify lowerBound, midPoints and upperBound
  const sortedDataPoints = dataPoints.sort((a, b) => a - b);
  const finalDataPoints = report.getFinalDataPoints(sortedDataPoints);
  report.calculationLog.addFinalLog(finalDataPoints);

  // Only for testing purposes
  report.calculationLog.addProviderDataLog(lenderProviderData, lenderSearchData, 'Lender');
  report.calculationLog.addProviderDataLog(borrowerProviderData, borrowerSearchData, 'Borrower');
  report.calculationLog.addPredictedDataLog(lenderPredictor, 'Lender');
  report.calculationLog.addPredictedDataLog(borrowerPredictor, 'Borrower');

  return {
    report: finalDataPoints,
    pricingApproach: getPricingApproach(condition, loan),
    calculationLog: report.calculationLog,
  };
};
