import { countryToISOMapping } from '../../../../utils/creditRatingUtils';
import {
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
  providerCountries,
} from '../../../../utils/providerDataUtils';
import { countryToRegionMapper } from '../../../../utils/reportUtils';
import { LoanType, EmbeddedCompanyType } from '../../../../types';

export function createProviderSearchData({
  loan,
  lender,
  borrower,
  providerDataIssueDate,
  isUsingRegionalProviderData,
}: {
  loan: LoanType;
  lender: EmbeddedCompanyType;
  borrower: EmbeddedCompanyType;
  providerDataIssueDate: Date;
  isUsingRegionalProviderData: boolean;
}) {
  const lenderSearchData = {
    issueDate: providerDataIssueDate,
    region: regionAbbreviations[countryToRegionMapper[lender.country]],
    country:
      providerCountries.includes(lender.country) && !isUsingRegionalProviderData
        ? countryToISOMapping[lender.country]
        : 'ALL',
    industryGroup: industryAbbreviations[lender.industry],
    currency: loan.currency,
    seniority: seniorityAbbreviations[loan.seniority],
  };
  const borrowerSearchData = {
    issueDate: providerDataIssueDate,
    region: regionAbbreviations[countryToRegionMapper[borrower.country]],
    country:
      providerCountries.includes(borrower.country) && !isUsingRegionalProviderData
        ? countryToISOMapping[borrower.country]
        : 'ALL',
    industryGroup: industryAbbreviations[borrower.industry],
    currency: loan.currency,
    seniority: seniorityAbbreviations[loan.seniority],
  };

  return { lenderSearchData, borrowerSearchData };
}
