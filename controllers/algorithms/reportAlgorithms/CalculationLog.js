class CalculationLog {
  log;
  data;

  constructor() {
    this.log = '';
  }

  // get log() {
  //   return this.log;
  // }

  _addData(data) {
    this.data = { ...this.data, ...data };
  }

  addTenorLog(startDate, endDate, tenor) {
    this.log += `Calculate tenor in years: ${endDate} - ${startDate} = ${tenor}\n`;
    this._addData({ tenor });
  }

  addClosestTenorLog(closestLowerTenor, closestHigherTenor) {
    this.log += `Calculate closest tenors: ${closestLowerTenor.stringValue}, ${closestHigherTenor.stringValue}\n`;
    this._addData({
      'tenor shorter': closestLowerTenor.floatValue,
      'tenor longer': closestHigherTenor.floatValue,
    });
  }

  addTenorAdjustmentLog(alias, report, higherBasePoint, lowerBasePoint, tenorAdjustment) {
    const { tenor, closestLowerTenor, closestHigherTenor } = report;
    this.log +=
      `\nCalculate ${alias} tenor adjustment:` +
      ' ' +
      `(${tenor.floatValue} - ${closestLowerTenor.floatValue}) *` +
      ' ' +
      `((${higherBasePoint} - ${lowerBasePoint})/(${closestHigherTenor.floatValue} - ${closestLowerTenor.floatValue})) = ${tenorAdjustment}\n`;
    this._addData({
      [alias]: {
        ...this.data[alias],
        higherBasePoint,
        lowerBasePoint,
      },
    });
  }

  addMidPointAdjustedLog({ alias, basePoint, tenorAdjustment, midPoint }) {
    this.log += `${alias}: ${basePoint} + ${tenorAdjustment} = ${midPoint}\n`;
    this._addData({
      [alias]: {
        ...this.data[alias],
        midPoint,
      },
    });
  }

  addMidPointLog({ alias, midPoint }) {
    this.log += `${alias}: ${midPoint}\n`;
    this._addData({
      [alias]: {
        ...this.data[alias],
        midPoint,
      },
    });
  }

  addFinalLog(dataPoints) {
    this.log += `\nFinal points: lowerBound: ${dataPoints.lowerBound}, midPoint: ${dataPoints.midPoint}, upperBound: ${dataPoints.upperBound}`;
  }

  // Only for the testing purposes
  addProviderDataLog(providerData, searchData, alias) {
    this.log += `\n\n${alias} search data:\n`;
    Object.entries(searchData).forEach(([key, value]) => {
      this.log += `${key}: ${value}\n`;
    });

    this.log += `${alias} provider data:\n`;
    Object.entries(providerData).forEach(([key, value]) => {
      this.log += `${key}: ${JSON.stringify(value)}\n`;
    });
  }

  // Only for testing purposes
  addPredictedDataLog({ predictedDataPoints, data }, alias) {
    this.log += `\n${alias} predicted values:\n`;
    predictedDataPoints.forEach((predictedValue) => {
      this.log += JSON.stringify(predictedValue);
    });

    this.log += `\n${alias} converted values:\n`;
    Object.entries(data).forEach(([creditRating, values]) => {
      this.log += creditRating + ' ' + JSON.stringify(values) + '\n';
    });
  }

  addProviderDataIssueDateLog(providerDataIssueDate) {
    this.log += `Report date: ${providerDataIssueDate}\n`;
    this._addData({ dataDate: providerDataIssueDate });
  }

  addMissingMidPointLog({ alias, missingMidPoint, midPoint, otherMidPoint }) {
    this.log += `\n${alias} midPoint: ${missingMidPoint} = (${midPoint} - ${otherMidPoint}) + ${midPoint}\n`;
    this._addData({
      [alias]: {
        ...this.data[alias],
        midPoint: missingMidPoint,
      },
    });
  }
}

module.exports = CalculationLog;
