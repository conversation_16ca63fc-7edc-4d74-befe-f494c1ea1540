const CalculationLog = require('../CalculationLog');

class GuaranteeCalculationLog extends CalculationLog {
  constructor() {
    super();
  }

  addCalculateUpperBoundLog({ principalMidPoint, guarantorMidPoint, upperBound }) {
    this.log += `\nUpper bound: ${principalMidPoint} - ${guarantorMidPoint} = ${upperBound}\n`;
  }

  addCalculateLowerBoundLog({ probabilityOfDefault, lossGivenDefault, lowerBound }) {
    this.log += `\nLower bound: ${probabilityOfDefault} * ${lossGivenDefault} = ${lowerBound}\n`;
  }

  addCalculateMidPointLog({ lowerBound, midPoint, upperBound }) {
    this.log += `\nMid point: (${upperBound} + ${lowerBound}) / 2 = ${midPoint}\n`;
  }
}

module.exports = GuaranteeCalculationLog;
