const _ = require('lodash');

const { getProviderDataIssueDate } = require('../../../../repositories/providerDataRepository');
const {
  createProviderSearchData,
  getLossGivenDefault,
  calculateSecurityApproachBounds,
} = require('../../../../utils/guaranteeUtils');
const { extractCreditRating } = require('../../../../utils/reportUtils');
const Predictor = require('../Predictor');
const Report = require('../Report');
const {
  calculateDataPointsOfSurroundingCreditRatings,
  getProviderData,
  searchAttributes,
} = require('../reportAlgorithms.utils');
const GuaranteeCalculationLog = require('./GuaranteeCalculationLog');
const { reportEnums, dayCountEnums } = require('../../../../enums');
const { BadRequestError, InternalServerError } = require('../../../../utils/ErrorHandler');
const { roundToXDecimals } = require('../../../../utils/numbers');
const dateUtils = require('../../../../utils/dates');
const getCumulativeProbabilityOfDefault = require('../../../../utils/companyUtils/cumulativeProbabilityOfDefault');
const { seniorityEnum } = require('../../../../utils/providerDataUtils/constants');

module.exports = async (guarantee) => {
  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH) {
    const isSecurityApproach = false;
    return actualCalculateGuaranteeDataAlgorithm(guarantee, isSecurityApproach);
  }

  /** cumulativeProbabilityOfDefault should be the same for all three seniorities  */
  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.SECURITY_APPROACH) {
    const isSecurityApproach = true;
    const {
      report: unsubordinatedReport,
      calculationLog: unsubordinatedReportLog,
      cumulativeProbabilityOfDefault: unsubordinatedCumulativeProbabilityOfDefault,
    } = await actualCalculateGuaranteeDataAlgorithm(
      { ...guarantee, seniority: seniorityEnum.unsubordinated },
      isSecurityApproach,
    );

    const {
      report: subordinatedReport,
      calculationLog: subordinatedReportLog,
      cumulativeProbabilityOfDefault: subordinatedCumulativeProbabilityOfDefault,
    } = await actualCalculateGuaranteeDataAlgorithm(
      { ...guarantee, seniority: seniorityEnum.subordinated },
      isSecurityApproach,
    );

    const {
      report: seniorSecuredReport,
      calculationLog: seniorSecuredReportLog,
      cumulativeProbabilityOfDefault: seniorSecuredProbabilityOfDefault,
    } = await actualCalculateGuaranteeDataAlgorithm(
      { ...guarantee, seniority: seniorityEnum.seniorSecured },
      isSecurityApproach,
    );

    // Check here just to make sure while testing. If this never throws in the future it can be removed.
    if (
      !_.isEqual(unsubordinatedCumulativeProbabilityOfDefault, subordinatedCumulativeProbabilityOfDefault) ||
      !_.isEqual(subordinatedCumulativeProbabilityOfDefault, seniorSecuredProbabilityOfDefault)
    ) {
      throw new InternalServerError('Probability of defaults do not match');
    }

    const sortedBounds = calculateSecurityApproachBounds({
      seniorSecuredMidPoint: seniorSecuredReportLog.data['principal points']?.midPoint,
      unsubordinatedMidPoint: unsubordinatedReportLog.data['principal points']?.midPoint,
      subordinatedMidPoint: subordinatedReportLog.data['principal points']?.midPoint,
    });

    return {
      report: {
        lowerBound: roundToXDecimals(sortedBounds[0]),
        midPoint: roundToXDecimals(sortedBounds[1]),
        upperBound: roundToXDecimals(sortedBounds[2]),
      },
      cumulativeProbabilityOfDefault: unsubordinatedCumulativeProbabilityOfDefault,
      calculationLog: [
        {
          unsubordinated: {
            report: unsubordinatedReport,
            log: unsubordinatedReportLog.log,
            data: unsubordinatedReportLog.data,
          },
        },
        {
          subordinated: {
            report: subordinatedReport,
            log: subordinatedReportLog.log,
            data: subordinatedReportLog.data,
          },
        },
        {
          seniorSecured: {
            report: seniorSecuredReport,
            log: seniorSecuredReportLog.log,
            data: seniorSecuredReportLog.data,
          },
        },
      ],
    };
  }

  throw new InternalServerError('Invalid pricing methodology.');
};

async function actualCalculateGuaranteeDataAlgorithm(data, isSecurityApproach) {
  const { guarantor, principal, isUsingRegionalProviderData, ...guarantee } = data;
  const { issueDate, terminationDate, paymentFrequency, pricingApproach } = guarantee;
  const rateType = { type: 'fixed' };
  const type = 'Bullet';

  // Fetch data by issue date that is <= requested issue date
  const providerDataIssueDate = await getProviderDataIssueDate(issueDate);
  if (!providerDataIssueDate) throw new BadRequestError('Cannot price a guarantee for selected issue date.');

  const calculationLog = new GuaranteeCalculationLog();
  calculationLog.addProviderDataIssueDateLog(providerDataIssueDate);

  const guarantorCreditRating = extractCreditRating(guarantor.creditRating, pricingApproach);
  const principalCreditRating = extractCreditRating(principal.creditRating, pricingApproach);

  const report = new Report({ startDate: issueDate, endDate: terminationDate }, calculationLog);

  const attributes = searchAttributes(report);

  const { guarantorSearchData, principalSearchData } = createProviderSearchData({
    guarantee,
    guarantor,
    principal,
    providerDataIssueDate,
    isUsingRegionalProviderData,
  });
  const [guarantorProviderData, principalProviderData] = await getProviderData(
    guarantorSearchData,
    principalSearchData,
    attributes,
  );

  const dayCount = dayCountEnums.dayCountMapper['ACT/365'];
  const guarantorPredictor = new Predictor({ searchData: guarantorSearchData, alias: 'Guarantor', type, dayCount });
  const principalPredictor = new Predictor({ searchData: principalSearchData, alias: 'Principal', type, dayCount });
  await Promise.all([
    guarantorPredictor.initializeInterpolationData(guarantorProviderData, paymentFrequency, report, rateType),
    principalPredictor.initializeInterpolationData(principalProviderData, paymentFrequency, report, rateType),
  ]);

  const guarantorMidPoint = report.calculateMidPoint(guarantorPredictor, guarantorCreditRating, 'guarantor points');
  const principalMidPoint = report.calculateMidPoint(principalPredictor, principalCreditRating, 'principal points');

  const upperBound = isSecurityApproach ? principalMidPoint : principalMidPoint - guarantorMidPoint;
  report.calculationLog.addCalculateUpperBoundLog({ upperBound, principalMidPoint, guarantorMidPoint });

  const tenor = dateUtils.calculateTenor(issueDate, terminationDate);
  const { probabilityOfDefaultPercentage } = getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach);

  const lossGivenDefault = getLossGivenDefault(guarantee?.seniority, principal?.industry);
  const lowerBound = probabilityOfDefaultPercentage * lossGivenDefault;
  report.calculationLog.addCalculateLowerBoundLog({ probabilityOfDefaultPercentage, lowerBound, lossGivenDefault });

  const midPoint = (upperBound + lowerBound) / 2;
  report.calculationLog.addCalculateMidPointLog({ lowerBound, midPoint, upperBound });

  const finalDataPoints = report.getFinalDataPoints([lowerBound, midPoint, upperBound]);

  _calculateDataPointsForTemplate({
    report,
    guarantorData: { guarantorPredictor, guarantorMidPoint, guarantorCreditRating },
    principalData: { principalPredictor, principalMidPoint, principalCreditRating },
  });

  // Only for testing purposes
  report.calculationLog.addProviderDataLog(guarantorProviderData, guarantorSearchData, 'Guarantor');
  report.calculationLog.addProviderDataLog(principalProviderData, principalSearchData, 'Principal');
  report.calculationLog.addPredictedDataLog(guarantorPredictor, 'Guarantor');
  report.calculationLog.addPredictedDataLog(principalPredictor, 'Principal');

  return {
    report: finalDataPoints,
    calculationLog: report.calculationLog,
    cumulativeProbabilityOfDefault: probabilityOfDefaultPercentage,
  };
}

// Calculate data for the word template ( this data is not used in the algorithm )
function _calculateDataPointsForTemplate({ report, guarantorData, principalData }) {
  const { guarantorPredictor, guarantorMidPoint, guarantorCreditRating } = guarantorData;
  const { principalPredictor, principalMidPoint, principalCreditRating } = principalData;

  // Calculate data points of the principals and guarantors surrounding credit ratings
  calculateDataPointsOfSurroundingCreditRatings({
    predictor: guarantorPredictor,
    report,
    midPoint: guarantorMidPoint,
    creditRating: guarantorCreditRating,
    aliases: { lowerPoints: 'guarantor lower points', upperPoints: 'guarantor upper points' },
  });
  calculateDataPointsOfSurroundingCreditRatings({
    predictor: principalPredictor,
    report,
    midPoint: principalMidPoint,
    creditRating: principalCreditRating,
    aliases: { lowerPoints: 'principal lower points', upperPoints: 'principal upper points' },
  });
}
