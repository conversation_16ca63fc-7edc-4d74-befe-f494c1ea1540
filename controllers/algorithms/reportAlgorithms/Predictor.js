const regression = require('regression');

const { providerRatings, ratingToXCoordinateMapper } = require('../../../utils/providerDataUtils');
const { BulletDataConverter, BalloonDataConverter } = require('./dataConverters');

const regressionOptions = { order: 5, precision: 10 };

// Predict the missing data points
class Predictor {
  alias;
  data;
  attributes;
  searchData;
  predictors;
  predictedDataPoints;
  dayCount;
  DataConverter;

  constructor({ searchData, alias, type, dayCount }) {
    this.alias = alias; // For the logs
    this.data = {};
    this.attributes = [];
    this.searchData = searchData;
    this.predictors = {};
    this.predictedDataPoints = [];
    this.dayCount = dayCount;
    this.DataConverter = type === 'Bullet' ? BulletDataConverter : BalloonDataConverter;
  }

  async initializeInterpolationData(providerData, paymentFrequency, report, rateType) {
    const interpolationData = {};
    let conversionTenor; // either closestHigher Tenor or just Tenor
    // keys are floatXX
    // do interpolation by tenor or by closestLower and closestHigher tenor
    let closestLowerTenorAttribute, closestHigherTenorAttribute, tenorAttribute;
    if (report.closestLowerTenor && report.closestHigherTenor) {
      closestLowerTenorAttribute = `float${report.closestLowerTenor.stringValue}`; // float3M (example)
      closestHigherTenorAttribute = `float${report.closestHigherTenor.stringValue}`; // float6M (example)
      interpolationData[closestLowerTenorAttribute] = [];
      interpolationData[closestHigherTenorAttribute] = [];
      this.attributes = [closestLowerTenorAttribute, closestHigherTenorAttribute];
      conversionTenor = report.closestHigherTenor;
    } else {
      tenorAttribute = `float${report.tenor.stringValue}`;
      interpolationData[tenorAttribute] = [];
      conversionTenor = report.tenor;
      this.attributes = [tenorAttribute];
    }

    await Promise.all(
      providerRatings.map(async (creditRating) => {
        const dataConverter = new this.DataConverter({
          data: providerData[creditRating],
          paymentFrequency,
          tenor: conversionTenor,
          dayCount: this.dayCount,
        });
        const xCoordinateValue = ratingToXCoordinateMapper[creditRating];
        if (closestLowerTenorAttribute && closestHigherTenorAttribute) {
          const convertedClosestLowerTenorValue =
            rateType.type === 'fixed'
              ? await dataConverter.convertFixed(report.closestLowerTenor)
              : await dataConverter.convertFloat(report.closestLowerTenor);
          const convertedClosestHigherTenorValue =
            rateType.type === 'fixed'
              ? await dataConverter.convertFixed(report.closestHigherTenor)
              : await dataConverter.convertFloat(report.closestHigherTenor);
          interpolationData[closestLowerTenorAttribute].push([xCoordinateValue, convertedClosestLowerTenorValue]); // { 'AAA': [[0, convertedValue]] } data[0] is the x coordinate of a credit rating. For all the provider credit ratings
          interpolationData[closestHigherTenorAttribute].push([xCoordinateValue, convertedClosestHigherTenorValue]);
          this.data[creditRating] = {
            id: providerData[creditRating].id,
            [closestLowerTenorAttribute]: convertedClosestLowerTenorValue,
            [closestHigherTenorAttribute]: convertedClosestHigherTenorValue,
          };
        } else {
          const convertedTenorValue =
            rateType.type === 'fixed'
              ? await dataConverter.convertFixed(report.tenor)
              : await dataConverter.convertFloat(report.tenor);
          interpolationData[tenorAttribute].push([xCoordinateValue, convertedTenorValue]);
          this.data[creditRating] = {
            id: providerData[creditRating].id,
            [tenorAttribute]: convertedTenorValue,
          };
        }
      }),
    );

    // Initialize Predictors
    this.attributes.forEach((attribute) => {
      this.predictors[attribute] = regression.polynomial(interpolationData[attribute], regressionOptions);
    });
  }

  getDataPoint(creditRating) {
    const dataPoints = {};
    const predictedPoints = {};
    if (creditRating in this.data) {
      // If credit rating exists in data base
      let predicted = false;
      this.attributes.forEach((attribute) => {
        // If attribute doesn't exists then predict it
        if (this.data[creditRating][attribute]) {
          dataPoints[attribute] = this.data[creditRating][attribute];
        } else {
          dataPoints[attribute] = this.predictors[attribute].predict(ratingToXCoordinateMapper[creditRating])[1];
          predictedPoints[attribute] = dataPoints[attribute];
          predicted = true;
        }
      });

      if (predicted) {
        // Add to predictedDataPoints for later logs
        this.predictedDataPoints.push({
          ...this.searchData,
          rating: creditRating,
          ...predictedPoints,
        });
      }
    } else {
      // If credit rating doesn't exist in database then predict all the attributes
      this.attributes.forEach((attribute) => {
        dataPoints[attribute] = this.predictors[attribute].predict(ratingToXCoordinateMapper[creditRating])[1];
        predictedPoints[attribute] = dataPoints[attribute];
      });

      // Add to predictedDataPoints for later logs
      this.predictedDataPoints.push({
        ...this.searchData,
        rating: creditRating,
        ...predictedPoints,
      });
    }

    return dataPoints;
  }
}

module.exports = Predictor;
