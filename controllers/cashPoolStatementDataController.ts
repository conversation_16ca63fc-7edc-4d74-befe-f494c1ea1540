import { Request, Response } from 'express';

import {
  cashPoolParticipantAccountRepository,
  cashPoolRepository,
  cashPoolStatementDataRepository,
  cashPoolStatementDataFileRepository,
} from '../repositories';
import * as azureService from '../services/azureService';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import * as statementUtils from '../utils/statementUtils';
import { jsonToSheet } from '../utils/documents';
import { formatDate } from '../utils/dates';
import { AddStatementDataType, EditStatementDataType } from '../schemas/cashPoolSchemas/statementDataSchemas';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';
import { CashPoolStatementBlockType } from '../types';
import cashPoolService from '../services/cashPoolService';
import models from '../models';
const { sequelize } = models;

async function getStatementData(req: Request<{ cashPoolId: number }>, res: Response): Promise<void> {
  const clientId = req.user?.clientId!;
  const { cashPoolId } = req.params;
  const { offset = 0, limit = 5, sort, ...filters } = req.query;

  const { statementDataFilter, companyFilter } = statementUtils.getStatementDataFilters(filters);

  const [count, data] = await Promise.all([
    cashPoolStatementDataRepository.getTotalCashPoolStatementDataCount({
      cashPoolId,
      clientId,
      where: statementDataFilter,
      whereCompany: companyFilter,
    }),
    cashPoolStatementDataRepository.getStatementDataForTemplateFile({
      cashPoolId: Number(cashPoolId),
      clientId,
      where: statementDataFilter,
      whereCompany: companyFilter,
      offset: Number(offset) * Number(limit),
      limit: Number(limit),
      order: statementUtils.getOrderBy(sort),
    }),
  ]);

  res.json({ data, count });
}

async function addStatementData(req: Request<{}, {}, AddStatementDataType>, res: Response): Promise<void> {
  const { cashPoolAccountId, date, statementDate, balanceChange, cashPoolId } = req.body;
  const { clientId } = req.user!;

  const account = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccount({ id: cashPoolAccountId });
  if (!account) throw new NotFoundError('Cash pool participant account');

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const isOverlap = statementUtils.validateDate(cashPool, date);
  if (isOverlap) throw new BadRequestError('Value date overlaps with previous batches.');

  const statement: CashPoolStatementBlockType = {
    date,
    statementDate,
    balanceChange,
    cashPoolAccountId,
    transactionReferenceNumber: statementUtils.consts.MANUALLY_ADDED,
    statementNumber: statementUtils.consts.MANUALLY_ADDED,
  };

  const data = await cashPoolStatementDataRepository.addStatementData(statement);

  res.json(data);
}

async function exportStatementData(req: Request<{ cashPoolId: number }>, res: Response) {
  const clientId = req.user?.clientId!;
  const { cashPoolId } = req.params;
  const { sort, ...filters } = req.query;

  const { statementDataFilter, companyFilter } = statementUtils.getStatementDataFilters(filters);

  const data = await cashPoolStatementDataRepository.getStatementDataForTemplateFile({
    cashPoolId: Number(cashPoolId),
    clientId,
    where: statementDataFilter,
    whereCompany: companyFilter,
    order: statementUtils.getOrderBy(sort),
  });

  const sheetData = data.map((d) => ({
    'Value Date': formatDate(d.date),
    'Statement Date': formatDate(d.statementDate),
    Company: d.account.participant.company.name,
    'Balance Change': d.balanceChange,
    Source: d.statementNumber === statementUtils.consts.MANUALLY_ADDED ? 'Manual' : 'Statement',
    Used: d.cashPoolBatchId ? 'Yes' : 'No',
  }));

  const result = jsonToSheet([{ sheetData, sheetName: 'Statement Data' }]);

  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Statement Data Export.xlsx"');
  return res.send(result);
}

async function editStatementData(req: Request<{ id: number }, {}, EditStatementDataType>, res: Response) {
  const { id } = req.params;
  const { date, statementDate, comment } = req.body;

  const statement = await cashPoolStatementDataRepository.getStatementDataById(id, req.user?.clientId!);
  if (!statement) throw new NotFoundError('Statement');

  const data = await cashPoolStatementDataRepository.updateStatementDataById(id, { date, statementDate, comment });

  res.json(data);
}

async function deleteStatementData(req: Request<{ id: number }>, res: Response): Promise<void> {
  const { id } = req.params;

  const statement = await cashPoolStatementDataRepository.getStatementDataById(id, req.user?.clientId!);
  if (!statement) throw new NotFoundError('Statement');

  const data = await cashPoolStatementDataRepository.deleteStatementById(id);

  res.json(data);
}

async function uploadStatementDataFile(req: Request<{ cashPoolId: number }>, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId, id } = req.user!;

  await sequelize.transaction(async () => {
    await cashPoolService.uploadStatementDataFile({
      clientId: clientId!,
      cashPoolId,
      file: req.file!,
      userId: id!,
    });
    res.status(201).send();
  });
}

async function getStatementDataFiles(req: Request<{ cashPoolId: number }>, res: Response) {
  const { cashPoolId } = req.params;
  // const clientId = req.user?.clientId!;

  const [count, data] = await Promise.all([
    cashPoolStatementDataFileRepository.getStatementDataFilesCount(cashPoolId),
    cashPoolStatementDataFileRepository.getStatementDataFiles(cashPoolId),
  ]);

  res.json({ count, data });
}

async function deleteStatementDataFile(req: Request<{ cashPoolId: number; id: number }>, res: Response) {
  const { cashPoolId, id } = req.params;
  const { clientId } = req.user!;

  await sequelize.transaction(async () => {
    await cashPoolService.deleteStatementDataFile({ fileId: id, clientId: clientId!, cashPoolId });
  });

  res.status(204).send();
}

async function massDeleteStatementData(req: Request, res: Response) {
  const { clientId } = req.user!;
  const { ids } = req.body;

  await sequelize.transaction(async () => {
    await Promise.all(
      ids.map(async (id: number) => {
        const statement = await cashPoolStatementDataRepository.getStatementDataById(id, clientId!);
        if (!statement) throw new NotFoundError('Statement');

        await cashPoolStatementDataRepository.deleteStatementById(id);
      }),
    );
  });

  res.status(204).send();
}

async function downloadStatementDataFile(req: Request, res: Response) {
  const { id } = req.params;
  const { clientId } = req.user!;

  const file = await cashPoolStatementDataFileRepository.getStatementDataFile(Number(id));

  if (!file) {
    throw new NotFoundError('Statement data file');
  }

  const fileBuffer = await azureService.getFile(`statement/${clientId!}/${file.id}_${file.name}${file.extension}`);

  res.set('Content-Type', file.mimeType);
  res.set('Content-Disposition', `attachment; filename="${file.name}${file.extension}"`);
  return res.send(fileBuffer);
}

export default {
  getStatementData: asyncControllerWrapper(getStatementData),
  addStatementData: asyncControllerWrapper(addStatementData),
  exportStatementData: asyncControllerWrapper(exportStatementData),
  editStatementData: asyncControllerWrapper(editStatementData),
  deleteStatementData: asyncControllerWrapper(deleteStatementData),
  uploadStatementDataFile: asyncControllerWrapper(uploadStatementDataFile),
  getStatementDataFiles: asyncControllerWrapper(getStatementDataFiles),
  deleteStatementDataFile: asyncControllerWrapper(deleteStatementDataFile),
  massDeleteStatementData: asyncControllerWrapper(massDeleteStatementData),
  downloadStatementDataFile: asyncControllerWrapper(downloadStatementDataFile),
};
