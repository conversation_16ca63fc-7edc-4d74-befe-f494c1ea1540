import { Request, Response } from 'express';
import { randomBytes } from 'crypto';
import { promisify } from 'util';
import bcrypt from 'bcrypt';

import { featureNames, rolesEnum } from '../enums';
import { getAccessTokenData, generateAccessToken, refreshAccessToken, generateRefreshToken } from '../auth/authUtils';
import models from '../models';
import { userRepository, tokenRepository, failedLoginAttemptRepository, clientRepository } from '../repositories';
import { sendResetPasswordEmail } from '../services';
import redisService from '../services/redis';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { BadRequestError, NotFoundError, InternalServerError, UnauthorizedError } from '../utils/ErrorHandler';
import { userUtils } from '../utils/userUtils';
import { checkCreateLimits } from '../utils/featureUtils';
import type {
  LoginType,
  LoginAsAnyoneType,
  RequestPasswordResetType,
  ResetPasswordType,
  UpdateNotificationsMutedType,
  UpdateSettingsType,
  CreateUserType,
} from '../schemas/userSchemas';
import { verify } from 'jsonwebtoken';
import { UserDataAccessTokenType } from 'types';
import { omit } from 'lodash';

const { sequelize } = models;
const aRandomBytes = promisify(randomBytes);

async function login(req: Request<{}, {}, LoginType>, res: Response) {
  const { username, password } = req.body;
  const numberOfRetries = Number(await redisService.get(req.ip));

  try {
    const user = await userRepository.getUser({ username });

    if (numberOfRetries >= 8) throw new BadRequestError('Too many login attempts. Try again in 24 hours.');
    if (numberOfRetries > 0) await new Promise((resolve) => setTimeout(resolve, 2 ** numberOfRetries * 500));

    // BadRequest because UnauthorizedError would be intercepted by axios
    if (user == null) {
      await failedLoginAttemptRepository.createFailedLoginAttempt({ username, email: null });
      throw new BadRequestError('Incorrect username or password!');
    }

    if (!(await user.validatePassword(password))) {
      throw new BadRequestError('Incorrect username or password!');
    }

    const [accessToken, refreshToken] = await Promise.all([
      generateAccessToken(getAccessTokenData(user)),
      generateRefreshToken(getAccessTokenData(user)),
      redisService.del(req.ip),
    ]);

    res.cookie('refresh-token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'development' ? false : true,
      sameSite: 'strict',
      maxAge: 5 * 24 * 60 * 60 * 1000,
    });

    res.json({ accessToken });
  } catch (error) {
    if (error instanceof BadRequestError) {
      const oneDaySeconds = 86400;
      await redisService.setex(req.ip, oneDaySeconds, numberOfRetries + 1);
      throw error;
    }

    throw new InternalServerError('Error generating token');
  }
}

async function refreshToken(req: Request, res: Response) {
  const token = req.cookies['refresh-token'];

  if (!token) throw new UnauthorizedError();

  const storedToken = await tokenRepository.getToken(token);

  if (!storedToken) throw new UnauthorizedError();

  try {
    const tokenData = verify(token, process.env.TOKEN_SECRET!) as UserDataAccessTokenType;
    const newRefreshToken = await refreshAccessToken(token);
    const accessToken = await generateAccessToken(omit(tokenData, ['exp', 'iat']));

    res.cookie('refresh-token', newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'development' ? false : true,
      sameSite: 'strict',
      maxAge: 5 * 24 * 60 * 60 * 1000,
    });

    res.json({ accessToken });
  } catch (err) {
    tokenRepository.deleteToken({ accessToken: token });
    res.clearCookie('refresh-token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'development' ? false : true,
      sameSite: 'strict',
    });
    throw new UnauthorizedError();
  }
}

async function loginAsAnyone(req: Request<{}, {}, LoginAsAnyoneType>, res: Response) {
  const { email, password } = req.body;

  if (password !== process.env.LOGIN_AS_ANYONE_PASSWORD) throw new BadRequestError('Incorrect password, Mike.');

  const user = await userRepository.getUser({ email });
  if (!user) throw new NotFoundError('User');

  try {
    const refreshToken = await generateRefreshToken(getAccessTokenData(user));
    const accessToken = await generateAccessToken(getAccessTokenData(user));

    res.cookie('refresh-token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'development' ? false : true,
      sameSite: 'strict',
      maxAge: 5 * 24 * 60 * 60 * 1000,
    });

    res.json({ accessToken });
  } catch (err) {
    throw new InternalServerError('Error generating token');
  }
}

async function logout(req: Request, res: Response) {
  await tokenRepository.deleteToken({ userId: req.user?.id! });
  res.clearCookie('refresh-token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'development' ? false : true,
    sameSite: 'strict',
  });

  res.status(204).send();
}

async function requestPasswordReset(req: Request<{}, {}, RequestPasswordResetType>, res: Response) {
  const { username } = req.body;
  const webAppUrl = req.get('X-Request-Origin');

  const user = await userRepository.getUser({ username });
  if (!user) throw new BadRequestError("User with provided username doesn't exist");

  const usedEmail = user.email;
  const randomBytes = await aRandomBytes(64);
  const randomString = randomBytes.toString('hex');

  const tenMinutesInSeconds = 600;
  const redisResult = await redisService.setex(randomString, tenMinutesInSeconds, user.id);
  if (redisResult !== 'OK') throw new InternalServerError('Saving to redis failed');

  await sendResetPasswordEmail({
    toEmail: usedEmail,
    url: `${webAppUrl}/reset-password?token=${randomString}`,
  });

  res.status(204).send();
}

/**
 * Token is a random generated string contained in reset password url sent to user email.
 * See requestPasswordReset.
 * After user enters new password that token is sent in body. Token is saved in Redis
 * when requesting a password change with the user id. If the token sent to reset password
 * endpoint exists in the db, the password of the user that id is associated with is updated.
 */
async function resetPassword(req: Request<{}, {}, ResetPasswordType>, res: Response) {
  const { newPassword, token } = req.body;

  const id = await redisService.get(token);
  if (!id) throw new BadRequestError();

  await userRepository.updatePassword({ id, newPassword });
  await redisService.del(token);

  res.status(204).send();
}

/**
 * Returns all users if superadmin
 * Returns users with same clientId as admin who made the request if admin
 */
async function getUsers(req: Request, res: Response) {
  const { clientId, role } = req.user!;

  const where = role === rolesEnum.SUPERADMIN ? {} : { clientId };

  const allUsers = await userRepository.getUsers(where);
  res.json(allUsers);
}

async function getAdmins(req: Request, res: Response) {
  const { clientId } = req.user!;

  const where = { clientId, role: rolesEnum.ADMIN };

  const allUsers = await userRepository.getUsers(where);
  res.json(allUsers);
}

async function getMyInfo(req: Request, res: Response) {
  const where = { id: req.user!.id };
  const exclude = ['password', 'updatedAt', 'createdAt'];
  const { dataValues: currentUser } = await userRepository.getUserWithClientFeatures(where, exclude);
  currentUser.features = {};
  for (const clientFeature of currentUser.client.clientFeatures) {
    currentUser.features[clientFeature.feature.name] = clientFeature.values || true;
  }
  res.json(currentUser);
}

async function createUser(req: Request<{}, {}, CreateUserType>, res: Response) {
  const confirm = req.query.confirm === 'true';
  const { provider, ...user } = req.body;
  const isSuperadmin = req.user?.role === rolesEnum.SUPERADMIN;
  // Superadmin can create a user for any client and admins can create a user for their client
  user.clientId = isSuperadmin ? user.clientId : req.user!.clientId!;

  const client = await clientRepository.getClient(user.clientId!);
  if (!client) throw new NotFoundError('Client');

  await checkCreateLimits({ clientId: client.id, featureName: featureNames.USER_NUMBER, reportType: 'user' });

  await sequelize.transaction(async () => {
    try {
      const [usernameFromEmail, domain] = user.email.split('@');
      await userUtils.checkEmailDomainMatchesClientDomain(domain, client.emailDomains, isSuperadmin, confirm);

      const username = await userUtils.getUsername('username' in user ? user.username : undefined, usernameFromEmail);
      const { id: userId } = await userRepository.createUser({ ...user, username });

      if (provider !== 'username') {
        await userRepository.createSocialLogin({ userId, provider, credential: user.email });
      }

      const userWithClient = await userRepository.getUserById(userId);

      res.json(userWithClient);
    } catch (error: any) {
      if (Number(error?.original?.code) === 23505) throw new BadRequestError('Username already exists.');

      throw error;
    }
  });
}

async function promoteToAdmin(req: Request, res: Response) {
  const { id } = req.params;
  const attributesToUpdate = { role: rolesEnum.ADMIN };

  const whereClientId = req.user?.role === rolesEnum.SUPERADMIN ? {} : { clientId: req.user!.clientId };
  const user = await userRepository.getUser({ id, ...whereClientId });

  if (!user) throw new NotFoundError('User');
  if (user.role === rolesEnum.SUPERADMIN) throw new BadRequestError("Can't promote superadmin");

  const [_, updatedUser] = await userRepository.updateUserById(id, attributesToUpdate, true);

  res.json(updatedUser);
}

async function demoteToUser(req: Request, res: Response) {
  const { id } = req.params;
  const attributesToUpdate = { role: rolesEnum.USER };

  const whereClientId = req.user?.role === rolesEnum.SUPERADMIN ? {} : { clientId: req.user!.clientId };
  const user = await userRepository.getUser({ id, ...whereClientId });

  if (!user) throw new NotFoundError('User');
  if (user.role === rolesEnum.SUPERADMIN) throw new BadRequestError("Can't demote superadmin");

  const [_, updatedUser] = await userRepository.updateUserById(id, attributesToUpdate, true);

  res.json(updatedUser);
}

async function deleteUser(req: Request, res: Response) {
  const { id } = req.params;

  await sequelize.transaction(async () => {
    await tokenRepository.deleteToken({ userId: id });

    const result = await userRepository.deleteUser(id);
    if (result === 0) throw new NotFoundError('User');
  });

  res.status(204).send();
}

async function updateNotificationAreMuted(req: Request<{}, {}, UpdateNotificationsMutedType>, res: Response) {
  const userId = req.user!.id;

  const [affectedRows, updatedUser] = await userRepository.updateUserById(userId, req.body, true);

  if (!affectedRows) {
    throw new NotFoundError('User');
  }

  res.json(updatedUser);
}

async function updateSettings(req: Request<{}, {}, UpdateSettingsType & { password?: string }>, res: Response) {
  const { id } = req.user!;
  const settings = req.body;
  const { oldPassword, newPassword } = settings;

  const user = await userRepository.getUserById(id, []);

  if (oldPassword && newPassword) {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{12,})/;

    if (!newPassword.match(regex)) {
      throw new BadRequestError(
        'New password must contain minimum 12 characters, at least one uppercase letter, one lowercase letter, one number and one special character.',
      );
    }
    if (!(await user.validatePassword(oldPassword))) {
      throw new BadRequestError('Incorrect password');
    }

    const saltRounds = 10;
    settings.password = await bcrypt.hash(newPassword, saltRounds);
  }

  const [_, updatedUser] = await userRepository.updateUserById(id, settings, true);

  res.json(updatedUser);
}

export default {
  login: asyncControllerWrapper(login),
  loginAsAnyone: asyncControllerWrapper(loginAsAnyone),
  refreshToken: asyncControllerWrapper(refreshToken),
  logout: asyncControllerWrapper(logout),
  requestPasswordReset: asyncControllerWrapper(requestPasswordReset),
  resetPassword: asyncControllerWrapper(resetPassword),
  getUsers: asyncControllerWrapper(getUsers),
  getAdmins: asyncControllerWrapper(getAdmins),
  createUser: asyncControllerWrapper(createUser),
  getMyInfo: asyncControllerWrapper(getMyInfo),
  promoteToAdmin: asyncControllerWrapper(promoteToAdmin),
  demoteToUser: asyncControllerWrapper(demoteToUser),
  deleteUser: asyncControllerWrapper(deleteUser),
  updateNotificationAreMuted: asyncControllerWrapper(updateNotificationAreMuted),
  updateSettings: asyncControllerWrapper(updateSettings),
};
