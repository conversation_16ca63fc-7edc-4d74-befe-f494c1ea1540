const { cashPoolRepository, topCurrencyAccountAuditTrailRepository } = require('../repositories');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { NotFoundError } = require('../utils/ErrorHandler');

async function getTopCurrencyAccountAuditTrailAll(req, res) {
  const { cashPoolId, auditTrailId } = req.params;
  const { clientId } = req.user;
  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) {
    throw new NotFoundError('Cash pool');
  }

  const topCurrencyAccountAuditTrails = await topCurrencyAccountAuditTrailRepository.getTopCurrencyAccountAuditTrailAll(
    auditTrailId,
  );
  return res.json(topCurrencyAccountAuditTrails);
}

async function getTopCurrencyAccountAuditTrail(req, res) {
  const { cashPoolId } = req.params;
  const { auditTrailTopCurrencyAccountId } = req.params;
  const { clientId } = req.user;
  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) {
    throw new NotFoundError('Cash pool');
  }

  const topCurrencyAccountAuditTrail = await topCurrencyAccountAuditTrailRepository.getTopCurrencyAccountAuditTrail({
    where: { id: auditTrailTopCurrencyAccountId },
  });
  if (!topCurrencyAccountAuditTrail) {
    throw new NotFoundError('Audit trail top currency account');
  }
  return res.json(topCurrencyAccountAuditTrail);
}

module.exports = {
  getTopCurrencyAccountAuditTrailAll: asyncControllerWrapper(getTopCurrencyAccountAuditTrailAll),
  getTopCurrencyAccountAuditTrail: asyncControllerWrapper(getTopCurrencyAccountAuditTrail),
};
