import { Request, Response } from 'express';

import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { whtRecipientRepository } from '../repositories';
import { getCountries } from '../singletons';

async function getWht(req: Request, res: Response) {
  const origin: string = req.body.origin; // loan lender country
  const recipient: string = req.body.recipient; // loan borrower country

  const { countriesByName } = await getCountries();

  const originId = Number(countriesByName[origin]?.id);
  const recipientId = Number(countriesByName[recipient]?.id);

  const recipientData = await whtRecipientRepository.getLatestRecipientWhtData(originId, recipientId);

  if (recipientData) {
    return res.json({ whtData: recipientData, isDefault: false });
  }

  const defaultData = await whtRecipientRepository.getOriginWhtData(recipientId);
  res.json({ whtData: defaultData, isDefault: true });
}

export default { getWht: asyncControllerWrapper(getWht) };
