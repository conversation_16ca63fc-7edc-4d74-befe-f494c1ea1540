const { sequelize } = require('../models');
const notificationRepository = require('../repositories/notificationRepository');
const userRepository = require('../repositories/userRepository');
const { sendgridNewNotificationSendEmail } = require('../services');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { NotFoundError } = require('../utils/ErrorHandler');

async function getNotifications(req, res) {
  const { offset = 0, limit = 5 } = req.query;
  const userId = req.user.id;

  const where = { receivingUserId: userId };
  const count = await notificationRepository.getNotificationCount(where);
  const notifications = await notificationRepository.getNotificationsByReceivingUserId(where, offset * limit, limit);

  res.json({ notifications, count });
}

async function getUnhandledNotifications(req, res) {
  const userId = req.user.id;

  const where = { receivingUserId: userId, isHandled: false };
  const notifications = await notificationRepository.getNotificationsByReceivingUserId(where);

  res.json(notifications);
}

async function createNotification(req, res) {
  const userId = req.user.id;
  const { adminIds, url, action, note } = req.body;

  await sequelize.transaction(async () => {
    const user = await userRepository.getUserById(userId);
    const userEmail = user.email;

    const notification = await notificationRepository.createNotification({
      createdByUserId: userId,
      url,
      action,
      note,
    });

    const receivingUsersNotification = adminIds.map((adminId) => ({
      receivingUserId: adminId,
      notificationId: notification.id,
    }));

    await notificationRepository.createUserNotifications(receivingUsersNotification);

    const admins = await userRepository.getUsers({ id: adminIds, areNotificationsMuted: false });

    await Promise.all(
      admins.map(({ email }) => sendgridNewNotificationSendEmail(email, action, url, note, userEmail, user.fullName)),
    );

    res.json(notification);
  });
}

async function deleteNotification(req, res) {
  const notificationId = req.params.id;
  const userId = req.user.id;

  const result = await notificationRepository.deleteNotificationByReceivingUserId(notificationId, userId);

  if (result === 0) {
    throw new NotFoundError('Notification');
  }

  res.status(204).send();
}

async function updateNotificationIsHandled(req, res) {
  const notificationId = req.params.id;
  const userId = req.user.id;

  const [affectedRows, rows] = await notificationRepository.updateNotificationIsHandled(
    notificationId,
    userId,
    req.body,
  );

  if (affectedRows === 0) {
    throw new NotFoundError('Notification');
  }

  res.json(rows[0]);
}

module.exports = {
  getNotifications: asyncControllerWrapper(getNotifications),
  getUnhandledNotifications: asyncControllerWrapper(getUnhandledNotifications),
  createNotification: asyncControllerWrapper(createNotification),
  deleteNotification: asyncControllerWrapper(deleteNotification),
  updateNotificationIsHandled: asyncControllerWrapper(updateNotificationIsHandled),
};
