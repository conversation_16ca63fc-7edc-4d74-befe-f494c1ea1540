import { Request, Response } from 'express';

import models from '../models';
import clientRepository from '../repositories/clientRepository';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import { NotFoundError } from '../utils/ErrorHandler';
import { createDefaultClientFeatures } from '../utils/featureUtils';
import * as clientUtils from '../utils/clientUtils';
import { CreateClientBodyType } from '../types';

async function getClient(req: Request, res: Response): Promise<void> {
  const { clientId } = req.params;
  const client = await clientRepository.getClient(Number(clientId));

  if (!client) {
    throw new NotFoundError('Client');
  }

  res.json(client);
}

async function getClients(_: Request, res: Response): Promise<void> {
  const clients = await clientRepository.getClients();

  res.json(clients);
}

async function createClient(req: Request, res: Response): Promise<void> {
  const { companiesToCreate, ...client }: CreateClientBodyType = req.body;

  await models.sequelize.transaction(async () => {
    const newClient = await clientRepository.createClient(client);

    await createDefaultClientFeatures(newClient.id);

    if (companiesToCreate) {
      await clientUtils.seedInitialClientData({
        user: { clientId: newClient.id, username: req.user!.username! },
        companiesToCreate,
      });
    }

    res.json(newClient);
  });
}

async function updateClient(req: Request, res: Response): Promise<void> {
  const clientId = req.user?.clientId!;

  const client = await clientRepository.getClient(clientId);
  if (!client) throw new NotFoundError('Client');

  const updatedClient = await clientRepository.updateClient(clientId, req.body);

  res.json(updatedClient);
}

async function deleteClient(req: Request, res: Response): Promise<void> {
  const { id } = req.params;

  const result = await clientRepository.deleteClient(id);

  if (result === 0) {
    throw new NotFoundError('Client');
  }

  res.json(result);
}

export default {
  getClient: asyncControllerWrapper(getClient),
  getClients: asyncControllerWrapper(getClients),
  createClient: asyncControllerWrapper(createClient),
  updateClient: asyncControllerWrapper(updateClient),
  deleteClient: asyncControllerWrapper(deleteClient),
};
