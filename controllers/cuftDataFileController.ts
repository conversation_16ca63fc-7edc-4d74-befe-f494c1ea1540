import { Request, Response } from 'express';

import models from '../models';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';
import { cuftDataFileRepository } from '../repositories';
import * as azureService from '../services/azureService';
import cuftService from '../services/cuftService';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
const { sequelize } = models;

const getCuftDataFile = async (req: Request<{ cuftFileId: number }>, res: Response) => {
  const { cuftFileId } = req.params;

  const cuftFile = await cuftDataFileRepository.getCuftDataFile({ where: { id: cuftFileId } });

  if (!cuftFile) {
    throw new NotFoundError('Template');
  }

  const fileBuffer = await azureService.getFile(`cuftData/${cuftFileId}`);

  res.set('Content-Type', cuftFile.mimeType);
  res.set('Content-Disposition', `attachment; filename="${cuftFile.name}${cuftFile.extension}"`);
  res.send(fileBuffer);
};

const getAllCuftDataFiles = async (req: Request, res: Response) => {
  const cuftFiles = await cuftDataFileRepository.getCuftDataFiles({});

  res.json(cuftFiles);
};

const uploadCuftDataFile = async (req: Request, res: Response) => {
  if (!req.file) {
    throw new BadRequestError('Upload file missing.');
  }

  const { buffer, size } = req.file;

  try {
    await sequelize.transaction(async () => {
      const cuftDataFile = await cuftDataFileRepository.createCuftFile(req.file!);
      const fileId = cuftDataFile.id;

      await cuftService.parseAndInsertCuftData(buffer, fileId);

      await azureService.uploadFile(`cuftData/${fileId}`, buffer, size);
      res.send();
    });
  } catch (err: any) {
    // Bad Request is thrown so the error message gets displayed in the toast
    throw new BadRequestError(err.message);
  }
};

const deleteCuftDataFile = async (req: Request, res: Response) => {
  const { cuftFileId } = req.params;

  await sequelize.transaction(async () => {
    await cuftDataFileRepository.deleteCuftFile(Number(cuftFileId));

    await azureService.deleteFile(`cuftData/${cuftFileId}`);
    res.status(204).send();
  });
};

export default {
  uploadCuftDataFile: asyncControllerWrapper(uploadCuftDataFile),
  getCuftDataFile: asyncControllerWrapper(getCuftDataFile),
  getAllCuftDataFiles: asyncControllerWrapper(getAllCuftDataFiles),
  deleteCuftDataFile: asyncControllerWrapper(deleteCuftDataFile),
};
