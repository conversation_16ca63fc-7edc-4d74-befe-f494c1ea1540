const providerDataRepository = require('../../repositories/providerDataRepository');
const { parseDate } = require('../../utils/dates');
const {
  parseDataPoints,
  headerToModelEntries,
  ratingMapper,
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
} = require('../../utils/providerDataUtils');

async function parseProviderData(readInterface) {
  // Parse
  const headers = [];
  let headerLine = true;
  let chunk = [];
  let chunkLength = 1;

  for await (const line of readInterface) {
    const data = line.split('|');
    if (headerLine) {
      // Get header
      for (let i = 0; i < data.length; i++) {
        headers.push(data[i].trim());
      }
      headerLine = false;
    } else {
      let parsedData = {};

      // Get row values
      const dataJSON = {};
      for (let i = 0, len = headers.length; i < len; i++) {
        dataJSON[headers[i]] = data[i].trim();
      }

      // Set values that need no parsing
      for (let i = 0, len = headerToModelEntries.length; i < len; i++) {
        const [key, value] = headerToModelEntries[i];
        parsedData[value] = dataJSON[key];
      }

      parsedData.rating = ratingMapper[parsedData.rating];

      //Abbreviate values
      parsedData.seniority = seniorityAbbreviations[parsedData.seniority];
      parsedData.industryGroup = industryAbbreviations[parsedData.industryGroup];
      parsedData.region = regionAbbreviations[parsedData.region];

      // if any of these isn't expected through provider data constants then discard the row
      if (!parsedData.rating || !parsedData.seniority || !parsedData.industryGroup || !parsedData.region) {
        continue;
      }

      // Setting values that need parsing
      // Parse Date
      parsedData.issueDate = parseDate(dataJSON['DATE']);

      // Parse data points
      const parsedDataPoints = parseDataPoints(JSON.parse(dataJSON['VALUES']));
      parsedData = { ...parsedData, ...parsedDataPoints };

      chunk.push(parsedData);
      if (chunkLength % 10000 === 0) {
        await providerDataRepository.createProviderData(chunk, { logging: false });
        chunkLength = 1;
        chunk = [];
      }

      chunkLength += 1;
    }
  }

  if (chunk.length !== 0) {
    await providerDataRepository.createProviderData(chunk, { logging: false });
  }
}

module.exports = {
  parseProviderData,
};
