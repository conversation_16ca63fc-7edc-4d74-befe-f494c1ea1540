export enum TrancheAssetClassEnum {
  'Senior Secured' = 'Senior Secured',
  'Senior Unsecured' = 'Senior Unsecured',
  'Secured' = 'Secured',
  'Unsecured' = 'Unsecured',
  'Second Lien Secured' = 'Second Lien Secured',
  '1.5 Lien Secured' = '1.5 Lien Secured',
  'Junior Lien Secured' = 'Junior Lien Secured',
  'Subordinated' = 'Subordinated',
  'Convertible' = 'Convertible',
  'Mezzanine' = 'Mezzanine',
}

export enum TrancheTypeEnum {
  Term = 'Term',
  Revolver = 'Revolver',
}

// Keys of this enum are the countries on the frontend in enums/countries.ts
export enum CountryToCuftCountryCodeEnum {
  'United States of America' = 'USA',
  Canada = 'CAN',
  'United Kingdom' = 'GBR',
  Luxembourg = 'LUX',
  Netherlands = 'NLD',
  'Cayman Islands' = 'CYM',
  Bermuda = 'BMU',
  Australia = 'AUS',
  China = 'CHN',
  Afghanistan = 'AFG',
  'Åland Islands' = 'ALA',
  Albania = 'ALB',
  Algeria = 'DZA',
  'American Samoa' = 'ASM',
  Andorra = 'AND',
  Angola = 'AGO',
  Anguilla = 'AIA',
  Antarctica = 'ATA',
  'Antigua and Barbuda' = 'ATG',
  Argentina = 'ARG',
  Armenia = 'ARM',
  Aruba = 'ABW',
  Austria = 'AUT',
  Azerbaijan = 'AZE',
  Bahamas = 'BHS',
  Bahrain = 'BHR',
  Bangladesh = 'BGD',
  Barbados = 'BRB',
  Belarus = 'BLR',
  Belgium = 'BEL',
  Belize = 'BLZ',
  Benin = 'BEN',
  Bhutan = 'BTN',
  Bolivia = 'BOL',
  'Bonaire, Sint Eustatius and Saba' = 'BES',
  'Bosnia and Herz.' = 'BIH',
  Botswana = 'BWA',
  'Bouvet Island' = 'BVT',
  Brazil = 'BRA',
  'British Indian Ocean Territory' = 'IOT',
  Brunei = 'BRN',
  Bulgaria = 'BGR',
  'Burkina Faso' = 'BFA',
  Burundi = 'BDI',
  'Cabo Verde' = 'CPV', // we don't have this country
  Cambodia = 'KHM',
  Cameroon = 'CMR',
  'Central African Rep.' = 'CAF',
  Chad = 'TCD',
  Chile = 'CHL',
  'Christmas Island' = 'CXR',
  'Cocos (Keeling) Islands' = 'CCK',
  Colombia = 'COL',
  Comoros = 'COM',
  Congo = 'COG',
  'Dem. Rep. Congo' = 'COD',
  'Cook Islands' = 'COK',
  'Costa Rica' = 'CRI',
  "Côte d'Ivoire" = 'CIV',
  Croatia = 'HRV',
  Cuba = 'CUB',
  Curaçao = 'CUW',
  Cyprus = 'CYP',
  'Czech Republic' = 'CZE',
  Denmark = 'DNK',
  Djibouti = 'DJI',
  Dominica = 'DMA',
  'Dominican Rep.' = 'DOM',
  Ecuador = 'ECU',
  Egypt = 'EGY',
  'El Salvador' = 'SLV',
  'Eq. Guinea' = 'GNQ',
  Eritrea = 'ERI',
  Estonia = 'EST',
  Swaziland = 'SWZ',
  Ethiopia = 'ETH',
  'Falkland Is.' = 'FLK',
  'Faroe Islands' = 'FRO',
  Fiji = 'FJI',
  Finland = 'FIN',
  France = 'FRA',
  'French Guiana' = 'GUF',
  'French Polynesia' = 'PYF',
  'Fr. S. Antarctic Lands' = 'ATF',
  Gabon = 'GAB',
  Gambia = 'GMB',
  Georgia = 'GEO',
  Germany = 'DEU',
  Ghana = 'GHA',
  Gibraltar = 'GIB',
  Greece = 'GRC',
  Greenland = 'GRL',
  Grenada = 'GRD',
  Guadeloupe = 'GLP',
  Guam = 'GUM',
  Guatemala = 'GTM',
  Guernsey = 'GGY',
  Guinea = 'GIN',
  'Guinea-Bissau' = 'GNB',
  Guyana = 'GUY',
  Haiti = 'HTI',
  'Heard Island and McDonald Islands' = 'HMD',
  'Holy See (Vatican City)' = 'VAT',
  Honduras = 'HND',
  'Hong Kong' = 'HKG',
  Hungary = 'HUN',
  Iceland = 'ISL',
  India = 'IND',
  Indonesia = 'IDN',
  Iran = 'IRN',
  Iraq = 'IRQ',
  Ireland = 'IRL',
  'Isle of Man' = 'IMN',
  Israel = 'ISR',
  Italy = 'ITA',
  Jamaica = 'JAM',
  Japan = 'JPN',
  Jersey = 'JEY',
  Jordan = 'JOR',
  Kazakhstan = 'KAZ',
  Kenya = 'KEN',
  Kiribati = 'KIR',
  'North Korea' = 'PRK',
  'South Korea' = 'KOR',
  Kuwait = 'KWT',
  Kyrgyzstan = 'KGZ',
  Laos = 'LAO',
  Latvia = 'LVA',
  Lebanon = 'LBN',
  Lesotho = 'LSO',
  Liberia = 'LBR',
  Libya = 'LBY',
  Liechtenstein = 'LIE',
  Lithuania = 'LTU',
  Macao = 'MAC',
  Madagascar = 'MDG',
  Malawi = 'MWI',
  Malaysia = 'MYS',
  Maldives = 'MDV',
  Mali = 'MLI',
  Malta = 'MLT',
  'Marshall Islands' = 'MHL',
  Martinique = 'MTQ',
  Mauritania = 'MRT',
  Mauritius = 'MUS',
  Mayotte = 'MYT',
  Mexico = 'MEX',
  'Micronesia, Federated States of' = 'FSM',
  Moldova = 'MDA',
  Monaco = 'MCO',
  Mongolia = 'MNG',
  Montenegro = 'MNE',
  Montserrat = 'MSR',
  Morocco = 'MAR',
  Mozambique = 'MOZ',
  Myanmar = 'MMR',
  Namibia = 'NAM',
  Nauru = 'NRU',
  Nepal = 'NPL',
  'New Caledonia' = 'NCL',
  'New Zealand' = 'NZL',
  Nicaragua = 'NIC',
  Niger = 'NER',
  Nigeria = 'NGA',
  Niue = 'NIU',
  'Norfolk Island' = 'NFK',
  'North Macedonia' = 'MKD',
  'Northern Mariana Islands' = 'MNP',
  Norway = 'NOR',
  Oman = 'OMN',
  Pakistan = 'PAK',
  Palau = 'PLW',
  Palestine = 'PSE',
  Panama = 'PAN',
  'Papua New Guinea' = 'PNG',
  Paraguay = 'PRY',
  Peru = 'PER',
  Philippines = 'PHL',
  Pitcairn = 'PCN',
  Poland = 'POL',
  Portugal = 'PRT',
  'Puerto Rico' = 'PRI',
  Qatar = 'QAT',
  Réunion = 'REU',
  Romania = 'ROU',
  Russia = 'RUS',
  Rwanda = 'RWA',
  'Saint Barthélemy' = 'BLM',
  'Saint Helena, Ascension and Tristan da Cunha' = 'SHN',
  'Saint Kitts and Nevis' = 'KNA',
  'Saint Lucia' = 'LCA',
  'Saint Martin' = 'MAF',
  'Saint Pierre and Miquelon' = 'SPM',
  'Saint Vincent and the Grenadines' = 'VCT',
  Samoa = 'WSM',
  'San Marino' = 'SMR',
  'Sao Tome and Principe' = 'STP',
  'Saudi Arabia' = 'SAU',
  Senegal = 'SEN',
  Serbia = 'SRB',
  Seychelles = 'SYC',
  'Sierra Leone' = 'SLE',
  Singapore = 'SGP',
  'Sint Maarten (Dutch part)' = 'SXM',
  Slovakia = 'SVK',
  Slovenia = 'SVN',
  'Solomon Is.' = 'SLB',
  Somalia = 'SOM',
  'South Africa' = 'ZAF',
  'South Georgia and South Sandwich Islands' = 'SGS',
  'S. Sudan' = 'SSD',
  Spain = 'ESP',
  'Sri Lanka' = 'LKA',
  Sudan = 'SDN',
  Suriname = 'SUR',
  'Svalbard and Jan Mayen' = 'SJM', // we don't have this country
  Sweden = 'SWE',
  Switzerland = 'CHE',
  Syria = 'SYR',
  Taiwan = 'TWN',
  Tajikistan = 'TJK',
  Tanzania = 'TZA',
  Thailand = 'THA',
  'Timor-Leste' = 'TLS',
  Togo = 'TGO',
  Tokelau = 'TKL',
  Tonga = 'TON',
  'Trinidad and Tobago' = 'TTO',
  Tunisia = 'TUN',
  Turkey = 'TUR',
  Turkmenistan = 'TKM',
  'Turks and Caicos Islands' = 'TCA',
  Tuvalu = 'TUV',
  Uganda = 'UGA',
  Ukraine = 'UKR',
  'United Arab Emirates' = 'ARE',
  'United States Minor Outlying Islands' = 'UMI',
  Uruguay = 'URY',
  Uzbekistan = 'UZB',
  Vanuatu = 'VUT',
  Venezuela = 'VEN',
  Vietnam = 'VNM',
  'Virgin Islands, British' = 'VGB',
  'Virgin Islands, U.S.' = 'VIR',
  'Wallis and Futuna' = 'WLF',
  'W. Sahara' = 'ESH',
  Yemen = 'YEM',
  Zambia = 'ZMB',
  Zimbabwe = 'ZWE',
}

export enum CurrencyToCuftCurrencyEnum {
  'USD' = 'USD',
  'CAD' = 'CAD',
  'MXN' = 'MXN',
  'EUR' = 'EUR',
  'GBP' = 'GBP',
  'CHF' = 'CHF',
  'DKK' = 'DKK',
  'NOK' = 'NOK',
  'SEK' = 'SEK',
  'AUD' = 'AUD',
  'NZD' = 'NZD',
  'JPY' = 'JPY',
  'HKD' = 'HKD',
  'SGD' = 'SGD',
  'AFN' = 'AFN',
  'ALL' = 'ALL',
  'DZD' = 'DZD',
  'AOA' = 'AOA',
  'ARS' = 'ARS',
  'AMD' = 'AMD',
  'AWG' = 'AWG',
  'AZN' = 'AZN',
  'BSD' = 'BSD',
  'BHD' = 'BHD',
  'BDT' = 'BDT',
  'BBD' = 'BBD',
  'BYN' = 'BYN',
  'BZD' = 'BZD',
  'BMD' = 'BMD',
  'BTN' = 'BTN',
  'BOV' = 'BOV',
  'BOB' = 'BOB',
  'BAM' = 'BAM',
  'BWP' = 'BWP',
  'BRL' = 'BRL',
  'BND' = 'BND',
  'BGN' = 'BGN',
  'BIF' = 'BIF',
  'KHR' = 'KHR',
  'CVE' = 'CVE',
  'KYD' = 'KYD',
  'XAF' = 'XAF',
  'CLP' = 'CLP',
  'COP' = 'COP',
  'KMF' = 'KMF',
  'CDF' = 'CDF',
  'CRC' = 'CRC',
  'HRK' = 'HRK',
  'CUC' = 'CUC',
  'CUP' = 'CUP',
  'CZK' = 'CZK',
  'DJF' = 'DJF',
  'DOP' = 'DOP',
  'XCD' = 'XCD',
  'EGP' = 'EGP',
  'ERN' = 'ERN',
  'ETB' = 'ETB',
  'FKP' = 'FKP',
  'FJD' = 'FJD',
  'GMD' = 'GMD',
  'GEL' = 'GEL',
  'GHS' = 'GHS',
  'GIP' = 'GIP',
  'GTQ' = 'GTQ',
  'GNF' = 'GNF',
  'GYD' = 'GYD',
  'HTG' = 'HTG',
  'HNL' = 'HNL',
  'HUF' = 'HUF',
  'ISK' = 'ISK',
  'INR' = 'INR',
  'IDR' = 'IDR',
  'IRR' = 'IRR',
  'IQD' = 'IQD',
  'ILS' = 'ILS',
  'JMD' = 'JMD',
  'JOD' = 'JOD',
  'KZT' = 'KZT',
  'KES' = 'KES',
  'KWD' = 'KWD',
  'KGS' = 'KGS',
  'LAK' = 'LAK',
  'LBP' = 'LBP',
  'LSL' = 'LSL',
  'LRD' = 'LRD',
  'LYD' = 'LYD',
  'MOP' = 'MOP',
  'MKD' = 'MKD',
  'MGA' = 'MGA',
  'MWK' = 'MWK',
  'MYR' = 'MYR',
  'MVR' = 'MVR',
  'MRU' = 'MRU',
  'MUR' = 'MUR',
  'MXV' = 'MXV',
  'MDL' = 'MDL',
  'MNT' = 'MNT',
  'MAD' = 'MAD',
  'MZN' = 'MZN',
  'MMK' = 'MMK',
  'NAD' = 'NAD',
  'NPR' = 'NPR',
  'ANG' = 'ANG',
  'TWD' = 'TWD',
  'NIO' = 'NIO',
  'NGN' = 'NGN',
  'KPW' = 'KPW',
  'OMR' = 'OMR',
  'PKR' = 'PKR',
  'PAB' = 'PAB',
  'PGK' = 'PGK',
  'PYG' = 'PYG',
  'PEN' = 'PEN',
  'PHP' = 'PHP',
  'PLN' = 'PLN',
  'QAR' = 'QAR',
  'CNY' = 'CNY',
  'RMB' = 'RMB',
  'RON' = 'RON',
  'RUB' = 'RUB',
  'RWF' = 'RWF',
  'SHP' = 'SHP',
  'SVC' = 'SVC',
  'WST' = 'WST',
  'STN' = 'STN',
  'SAR' = 'SAR',
  'RSD' = 'RSD',
  'SCR' = 'SCR',
  'SLL' = 'SLL',
  'SBD' = 'SBD',
  'SOS' = 'SOS',
  'ZAR' = 'ZAR',
  'KRW' = 'KRW',
  'SSP' = 'SSP',
  'LKR' = 'LKR',
  'SDG' = 'SDG',
  'SRD' = 'SRD',
  'SZL' = 'SZL',
  'SYP' = 'SYP',
  'TJS' = 'TJS',
  'TZS' = 'TZS',
  'THB' = 'THB',
  'TOP' = 'TOP',
  'TTD' = 'TTD',
  'TND' = 'TND',
  'TRY' = 'TRY',
  'TMT' = 'TMT',
  'UGX' = 'UGX',
  'UAH' = 'UAH',
  'CLF' = 'CLF',
  'COU' = 'COU',
  'UYW' = 'UYW',
  'AED' = 'AED',
  'USN' = 'USN',
  'UYI' = 'UYI',
  'UYU' = 'UYU',
  'UZS' = 'UZS',
  'VUV' = 'VUV',
  'VED' = 'VED',
  'VES' = 'VES',
  'VND' = 'VND',
  'CHE' = 'CHE',
  'CHW' = 'CHW',
  'YER' = 'YER',
  'ZMW' = 'ZMW',
  'ZWL' = 'ZWL',
}
