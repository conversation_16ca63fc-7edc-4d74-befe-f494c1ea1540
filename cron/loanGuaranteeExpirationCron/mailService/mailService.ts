import sendgridAPI from '../../../services/sendgridAPI';
import { contact } from '../../../config/config';
import { UserWithClientType, LoanType, GuaranteeType } from '../../../types';
import logger from '../../../utils/logger';

import { formatEmail } from './formatEmail';
import { getUrl } from '../loanGuaranteeExpirationCronJobUtils';

const sendMail = (loansHtml: string, guaranteesHtml: string, toEmail: string) => {
  return sendgridAPI.post('', {
    personalizations: [{ to: [{ email: toEmail }], subject: 'Loans/Guarantees are about to expire' }],
    content: [{ type: 'text/html', value: formatEmail(loansHtml, guaranteesHtml) }],
    from: { email: contact.fromEmail },
  });
};

export const sendLoanGuaranteeExpirationEmail = async (
  clientUsers: UserWithClientType[],
  loansAboutToExpire: LoanType[],
  guaranteesAboutToExpire: GuaranteeType[],
): Promise<void> => {
  try {
    const loansAboutToExpireTexts = loansAboutToExpire.map(({ id, currency, amount, borrower }: LoanType) => {
      return `<a href=${getUrl(id, 'loan')}>${currency} ${amount} ${borrower.name}</a>`;
    });

    const guaranteesAboutToExpireTexts = guaranteesAboutToExpire.map(
      ({ id, currency, amount, principal }: GuaranteeType) => {
        return `<a href=${getUrl(id, 'guarantee')}>${currency} ${amount} ${principal.name}</a>`;
      },
    );

    const loansHtml = loansAboutToExpireTexts.join('<br />');
    const guaranteesHtml = guaranteesAboutToExpireTexts.join('<br />');

    if (loansHtml !== '' || guaranteesHtml !== '') {
      await Promise.all(clientUsers.map((user) => sendMail(loansHtml, guaranteesHtml, user.email)));
    }
  } catch (error: any) {
    logger.error({
      message: 'Sending loan/guarantee to expire mail failed',
      error,
      payload: { loansAboutToExpire, guaranteesAboutToExpire },
    });
  }
};
