export const formatEmail = (loansHtml: string, guaranteesHtml: string) => {
  const getAboutToExpireTitle = (isShowing: boolean, reportName: 'Loans' | 'Guarantees'): string => {
    if (!isShowing) return '';

    const expireWord = reportName === 'Loans' ? 'mature' : 'expire';
    return `
      <div style="text-align: left; margin: 32px 0 12px 0;">
        <span
          style="
            font-weight: 400;
            font-size: 18px;
            line-height: 24px;
            color: #12246c;
            font-family: 'Roboto', sans-serif;
            margin: 0 6px 12px 0;
          "
        >
          ${reportName} about to ${expireWord}
        </span>
      </div>
    `;
  };

  return `
  <!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Notification email</title>
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,400;0,500;1,400&display=swap"
        rel="stylesheet"
      />
    </head>
    <body>
      <div
        style="
          position: relative;
          max-width: 700px;
          background: #f7fafc;
          border-radius: 4px;
          padding: 48px;
          margin: 0 auto;
        "
      >
        <div style="text-align: center; margin-bottom: 48px">
          <img
            alt="logo"
            src="https://nordadvisorystorage.blob.core.windows.net/production/logo-dark.png?sv=2020-08-04&st=2021-11-10T13%3A29%3A42Z&se=2050-12-31T13%3A29%3A00Z&sr=b&sp=r&sig=bB1DRhtVFzl8VB700BJ%2BFBgp%2FihiTV0qdINQBy4byro%3D"
          />
        </div>
        ${loansHtml && getAboutToExpireTitle(!!loansHtml, 'Loans')}
        ${loansHtml}

        ${guaranteesHtml && getAboutToExpireTitle(!!guaranteesHtml, 'Guarantees')}
        ${guaranteesHtml}
      </div>
    </body>
  </html>
  `;
};
