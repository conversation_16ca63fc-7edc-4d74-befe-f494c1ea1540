import { notificationRepository } from '../../repositories';
import {
  LoanType,
  GuaranteeType,
  UserWithClientType,
  NotificationType,
  ReceivingUserNotificationType,
} from '../../types';
import { notificationActions } from '../../enums';

const { LOAN_ABOUT_TO_EXPIRE, GUARANTEE_ABOUT_TO_EXPIRE } = notificationActions.NOTIFICATION_ACTIONS;

export const getUrl = (id: number, reportType: 'loan' | 'guarantee') => {
  return `${process.env.WEB_APP_URL}/portfolio/${id}?reportType=${reportType}`;
};

const handleNotificationCreation = async (
  report: LoanType | GuaranteeType,
  clientUsers: UserWithClientType[],
  note: string,
  reportType: 'loan' | 'guarantee',
): Promise<ReceivingUserNotificationType[]> => {
  const action = reportType === 'loan' ? LOAN_ABOUT_TO_EXPIRE : GUARANTEE_ABOUT_TO_EXPIRE;
  const url = getUrl(report.id, reportType);
  const SUPERADMIN_ID = 1;

  const notification: NotificationType = await notificationRepository.createNotification({
    createdByUserId: SUPERADMIN_ID,
    url,
    action,
    note,
  });

  const userNotifications = clientUsers.map(({ id }) => ({ receivingUserId: id, notificationId: notification.id }));

  return notificationRepository.createUserNotifications(userNotifications);
};

export const handleLoanNotificationCreation = (
  loan: LoanType,
  clientUsers: UserWithClientType[],
): Promise<ReceivingUserNotificationType[]> => {
  const note = 'Loan is about to mature';
  return handleNotificationCreation(loan, clientUsers, note, 'loan');
};

export const handleGuaranteeNotificationCreation = (
  guarantee: GuaranteeType,
  clientUsers: UserWithClientType[],
): Promise<ReceivingUserNotificationType[]> => {
  const note = 'Guarantee is about to expire';
  return handleNotificationCreation(guarantee, clientUsers, note, 'guarantee');
};
