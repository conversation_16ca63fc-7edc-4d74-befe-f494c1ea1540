import sendgridAPI from '../../../services/sendgridAPI';
import { contact } from '../../../config/config';
import { DeloitteOriginMailDataType, DeloitteRecipientMailDataType } from '../../../types';
import logger from '../../../utils/logger';

import { formatEmail } from './formatEmail';

const sendMail = (mailHtml: string) => {
  return sendgridAPI.post('', {
    personalizations: [{ to: [{ email: contact.toEmail }], subject: 'WHT changes' }],
    content: [{ type: 'text/html', value: formatEmail(mailHtml) }],
    from: { email: contact.fromEmail },
  });
};

export const sendWhtInterestChangeEmail = async (
  allOriginMailData: DeloitteOriginMailDataType[],
  allRecipientMailData: DeloitteRecipientMailDataType[],
) => {
  try {
    const defaultInterestChangeTexts = allOriginMailData.map((o: DeloitteOriginMailDataType) => {
      return `Default WHT on interest applicable to payments from <b>${o.country}</b> changed from ${
        o.previousInterestRate || 'No previous value'
      } to ${o.currentInterestRate}`;
    });

    const countryInterestChangeTexts = allRecipientMailData.map((r: DeloitteRecipientMailDataType) => {
      return `WHT on interest applicable to payments from <b>${r.originCountry}</b> to <b>${
        r.country
      }</b> changed from ${r.previousInterestRate || 'No previous value'} to ${r.currentInterestRate}`;
    });

    const mailHtml = [...defaultInterestChangeTexts, '<br /><br />', ...countryInterestChangeTexts].join('<br />');

    await sendMail(mailHtml);
  } catch (error: any) {
    logger.error({
      message: 'Sending WHT mail failed',
      error,
      payload: { allOriginMailData, allRecipientMailData },
    });
  }
};
