/* eslint-disable no-console */
import { parentPort } from 'worker_threads';
import { Op } from 'sequelize';
import { subDays } from 'date-fns';
import chalk from 'chalk';

import models from '../../models';
import {
  DeloitteApiWHTRecipientType,
  DeloitteApiWHTOriginType,
  DeloitteApiResponseObject,
  DeloitteWHTOriginType,
  DeloitteOriginMailDataType,
  DeloitteRecipientMailDataType,
} from '../../types';
import { whtOriginRepository, whtRecipientRepository } from '../../repositories';
import { InternalServerError } from '../../utils/ErrorHandler';
import {
  getDitsDeloitteData,
  checkOriginModifyDate,
  checkRecipientModifyDate,
  mapOriginObject,
  mapRecipientObject,
} from './withholdingTaxCronJobUtils';
import { getCountries } from '../../singletons';
import { sendWhtInterestChangeEmail } from './mailService';
import { whtCountryMapper } from '../../enums/wht';

const MAX_COUNTRY_ID = 225;

/**
 * Fetches all the data from the Deloitte API. Checks if the modifyDate has been in the last day
 * and sends an email with all the changes to Mike. Finally saves all the data in the database.
 * Takes about 2 minutes for the job to finish
 */
const run = async () => {
  try {
    console.log(chalk.green('\n====STARTED WITHHOLDING TAX DATA FETCHING====\n'));

    await models.sequelize.transaction(async () => {
      const { countriesByName } = await getCountries();
      const allOriginMailData: DeloitteOriginMailDataType[] = [];
      const allRecipientMailData: DeloitteRecipientMailDataType[] = [];
      for (let ditsCountryId = 1; ditsCountryId <= MAX_COUNTRY_ID; ditsCountryId++) {
        const response: DeloitteApiResponseObject = await getDitsDeloitteData(ditsCountryId);
        if (response.status !== 200) {
          throw new InternalServerError(`Deloitte API returned status: ${response.status}`);
        }

        if (!response.data.longDesc) continue;

        const originCountry = countriesByName[whtCountryMapper[response.data.longDesc] || response.data.longDesc];
        if (!originCountry) continue;

        const ditsOrigin = response.data.domesticRates.withholdingTax[0];
        const mailData = await checkOriginModifyDate(ditsOrigin, originCountry.id);
        if (mailData) allOriginMailData.push(mailData);

        const originToCreate: DeloitteApiWHTOriginType = mapOriginObject(originCountry.id, ditsOrigin);
        const { dividend, interest, royalty } = originToCreate;

        if (!dividend && !interest && !royalty && response.data.treatyRates.length === 0) continue;

        const origin: DeloitteWHTOriginType = await whtOriginRepository.deloitteApiCreate(originToCreate);

        const recipientsToCreate: DeloitteApiWHTRecipientType[] = [];
        for (const recipientToCreate of response.data.treatyRates) {
          const recipientCountryNameDits = recipientToCreate.longDesc;
          if (!recipientCountryNameDits) continue;

          const recipientCountry =
            countriesByName[whtCountryMapper[recipientCountryNameDits] || recipientCountryNameDits];
          if (!recipientCountry) continue;

          const recipientMailData = await checkRecipientModifyDate(
            origin,
            originCountry,
            recipientToCreate,
            recipientCountry,
          );
          if (recipientMailData) allRecipientMailData.push(recipientMailData);
          recipientsToCreate.push(mapRecipientObject(origin.id, recipientCountry.id, recipientToCreate));
        }

        await whtRecipientRepository.deloitteApiBulkCreate(recipientsToCreate);
      }

      if (allOriginMailData.length !== 0 || allRecipientMailData.length !== 0) {
        await sendWhtInterestChangeEmail(allOriginMailData, allRecipientMailData);
      }
    });
    const whtOriginCount = await whtOriginRepository.getCount();
    const arbitraryWhtCountToPreventDeletion = 300;
    if (whtOriginCount > arbitraryWhtCountToPreventDeletion) {
      // Delete WHT data older than two days because only current data is relevant
      await whtOriginRepository.destroy({ date: { [Op.lte]: subDays(new Date(), 2) } });
    }

    console.log(chalk.green('\n====DONE FETCHING WITHHOLDING TAX DATA====\n'));
    parentPort?.postMessage('WHT cron job finished successfully.');
  } catch (err) {
    parentPort?.postMessage(err);
  }
};

run();
