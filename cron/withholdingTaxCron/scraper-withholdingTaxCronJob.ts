/* eslint-disable no-console */
// import { parentPort } from 'worker_threads';
// import puppeteer from 'puppeteer';
// import chalk from 'chalk';

// import models from '../../models';
// import { whtOriginRepository, whtRecipientRepository } from '../../repositories';
// import { WHTOriginType, CreateWHTOriginType, CreateWHTRecipientType, ScrapedWHTRecipientType } from '../../types';

// const DELOITTE_URL = 'https://dits.deloitte.com/#Jurisdiction';
// const MAX_COUNTRY_ID = 225;
// const ARGENTINA_ID = 1;

// type JurisdictionType = {
//   countryId: number;
//   origin: string;
//   defaultDividend?: string;
//   defaultInterest?: string;
//   defaultRoyalty?: string;
//   recipientData: ScrapedWHTRecipientType[];
// };

// const acceptCookies = async (page: puppeteer.Page) => {
//   await page.goto(`${DELOITTE_URL}/${ARGENTINA_ID}`, { waitUntil: 'networkidle2' });
//   await page.evaluate(() => document.getElementById('onetrust-accept-btn-handler')?.click());
// };

// const argentinaCheck = async (page: puppeteer.Page) => {
//   await page.goto(`${DELOITTE_URL}/${ARGENTINA_ID}`, { waitUntil: 'networkidle2' });
//   await new Promise((r) => setTimeout(r, 5000));
//   await page.evaluate(async () => {
//     const tableItems = document.getElementsByTagName('tbody')[0]?.children.length;
//     if (!tableItems) {
//       throw new Error("Argentina's recipient table has zero items or doesn't exist");
//     }

//     const origin = document.querySelector<HTMLElement>('.tax-treaty-name')?.innerText!;
//     if (!origin) {
//       throw new Error('Argentina missing origin');
//     }

//     const defaultsContainer = document.getElementById('jurisdiction-withholdingTax');
//     if (!defaultsContainer) {
//       throw new Error('Argentina missing defaults container');
//     }
//   });
// };

// const getOriginObject = (jurisdiction: JurisdictionType): CreateWHTOriginType => ({
//   countryId: Number(jurisdiction.origin),
//   dividend: jurisdiction.defaultDividend,
//   interest: jurisdiction.defaultInterest,
//   royalty: jurisdiction.defaultRoyalty,
//   date: new Date(),
// });

// const runScraper = async () => {
//   let countryId;
//   try {
//     const browser = await puppeteer.launch({ headless: true });
//     const [page] = await browser.pages();
//     await page.setViewport({ width: 1000, height: 1000 });

//     await acceptCookies(page);
//     await argentinaCheck(page);

//     const result = [];
//     for (countryId = 1; countryId <= MAX_COUNTRY_ID; countryId++) {
//       await page.goto(`${DELOITTE_URL}/${countryId}`, { waitUntil: ['networkidle2', 'load', 'domcontentloaded'] });
//       await new Promise((r) => setTimeout(r, 2500));

//       const data = await page.evaluate(async (countryId: number) => {
//         /**
//          * Opens the interest explanation modal if it exists. Tries 10 times with a small delay to get
//          * the text from the modal. Returns either the text if it exists or undefined otherwise.
//          */
//         const getInterestExplanation = async (rowNumber: number, country: string): Promise<string | undefined> => {
//           const interestExplanationElement = document
//             .getElementsByTagName<any>('tbody')[0]
//             .children[rowNumber]?.children[3]?.querySelector('.notesIndicator');

//           if (!interestExplanationElement) return;

//           interestExplanationElement.click();
//           for (let tries = 1; tries <= 10; tries++) {
//             await new Promise((r) => setTimeout(r, 50));
//             const interestExplanation =
//               document.querySelector<any>('.modal-body')?.children[0]?.children[0]?.children[1]?.innerText;
//             const isCorrectModalOpen = document.querySelector<any>('.modal-title').innerText.includes(country);

//             /**
//              * For some reason without isCorrectModalOpen it would scrape the previous modal text sometimes.
//              * This checks the title in the modal contains the country name of the row. The modal title always
//              * includes the name of the country for that row.
//              */
//             if (interestExplanation && isCorrectModalOpen) return interestExplanation;
//           }
//         };

//         const getOrigin = async () => {
//           for (let tries = 1; tries <= 50; tries++) {
//             await new Promise((r) => setTimeout(r, 100));
//             const origin = document.querySelector<HTMLElement>('.tax-treaty-name')?.innerText!;
//             if (origin) return origin;
//           }
//           throw new Error('Origin not found');
//         };

//         const getTableRow = async (rowNumber: number): Promise<string> => {
//           for (let tries = 1; tries <= 50; tries++) {
//             await new Promise((r) => setTimeout(r, 100));
//             const rowDataString = document.getElementsByTagName<any>('tbody')[0]?.children[rowNumber]?.innerText;
//             if (rowDataString) return rowDataString;
//           }
//           throw new Error(`No data in table row - countryId: ${countryId} rowNumber: ${rowNumber}`);
//         };

//         const origin: string = await getOrigin();
//         /**
//          * Some pages like countryId = 19 don't work but still show the data from the last page.
//          * This is used to skip those pages.
//          */
//         if (origin === localStorage.getItem('previousOrigin')) {
//           return null;
//         }
//         localStorage.setItem('previousOrigin', origin);
//         const defaultsContainer = document.getElementById('jurisdiction-withholdingTax');
//         if (!defaultsContainer) {
//           throw new Error('Defaults container missing');
//         }

//         const defaultDividend =
//           defaultsContainer.querySelectorAll<HTMLElement>('[data-column-name=Dividends]')[0]?.innerText;
//         const defaultInterest =
//           defaultsContainer.querySelectorAll<HTMLElement>('[data-column-name=Interest]')[0]?.innerText;
//         const defaultRoyalty =
//           defaultsContainer.querySelectorAll<HTMLElement>('[data-column-name=Royalties]')[0]?.innerText;

//         const rowsLength = document.getElementsByTagName('tbody')[0]?.children.length;
//         if (!defaultDividend && !defaultInterest && !defaultRoyalty && !rowsLength) return null;

//         const recipientData: ScrapedWHTRecipientType[] = [];
//         for (let rowNumber = 0; rowNumber < rowsLength; rowNumber++) {
//           const rowDataString = await getTableRow(rowNumber);

//           const [country, rest] = rowDataString.split('\n\t');
//           if (!country || !rest) {
//             throw new Error('No country or rest in table');
//           }

//           const interestExplanation = await getInterestExplanation(rowNumber, country);

//           const [_ownership, dividend, interest, royalty] = rest.split('\t');

//           // TODO needs to be fixed when used, 3 should be replaced with id of country from countries singleton
//           recipientData.push({ countryId: 3, dividend, interest, royalty, interestExplanation });
//         }

//         return { countryId, origin, defaultDividend, defaultInterest, defaultRoyalty, recipientData };
//       }, countryId);

//       result.push(data);
//     }
//     await browser.close();
//     return result;
//   } catch (error: any) {
//     console.log('ERROR', error);
//     error.message = `${error.message} - Scraping failed on countryId: ${countryId}`;
//     throw error;
//   }
// };

// Takes about 24 minutes for job to finish
const run = async () => {
  // try {
  //   console.log(chalk.green('\n====STARTED WITHHOLDING TAX DATA SCRAPING====\n'));
  //   const scrapedData = (await runScraper()) || [];
  //   await models.sequelize.transaction(async () => {
  //     for (const jurisdiction of scrapedData) {
  //       if (jurisdiction == null) continue;
  //       const originToCreate: CreateWHTOriginType = getOriginObject(jurisdiction);
  //       const origin: WHTOriginType = await whtOriginRepository.create(originToCreate);
  //       const recipientsToCreate: CreateWHTRecipientType[] = jurisdiction.recipientData.map((r) => ({
  //         originId: origin.id,
  //         ...r,
  //       }));
  //       await whtRecipientRepository.bulkCreate(recipientsToCreate);
  //     }
  //   });
  //   console.log(chalk.green('\n====DONE SCRAPING WITHHOLDING TAX DATA====\n'));
  // } catch (error: any) {
  //   parentPort?.postMessage(error);
  // }
};

run();
