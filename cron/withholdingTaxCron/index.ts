const CronJob = require('cron').CronJob;
const { Worker } = require('worker_threads');

const logger = require('../../utils/logger');

const withholdingTaxCronJob = new CronJob(
  '0 2 * * *',
  async () => {
    try {
      await new Promise((resolve, reject) => {
        const worker = new Worker('./cron/withholdingTaxCron/withholdingTaxCronJob.import.js');
        worker.on('message', (value: any) => {
          if (value instanceof Error) reject(value);
          else resolve(value);
        });
        worker.on('error', reject);
        worker.on('exit', (code: number) => {
          if (code !== 0) reject(new Error(`Worker stopped with exit code ${code}`));
        });
      });
    } catch (error: any) {
      logger.error({ message: error?.message, error });
    }
  },
  null,
  true,
);

export = { withholdingTaxCronJob };
