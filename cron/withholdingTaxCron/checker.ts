/* eslint-disable no-console */
import { differenceInDays } from 'date-fns';
import chalk from 'chalk';

import { DeloitteApiWHTOriginType, DeloitteApiResponseObject } from '../../types';
import { InternalServerError } from '../../utils/ErrorHandler';
import { getDitsDeloitteData, mapOriginObject } from './withholdingTaxCronJobUtils';
import { getCountries } from '../../singletons';
import { whtCountryMapper } from '../../enums/wht';

const MAX_COUNTRY_ID = 225;

/**
 * Manual check for modify dates to confirm mail sending works as it should
 */
const run = async () => {
  console.log(chalk.green('\n====STARTED WITHHOLDING TAX DATA FETCHING====\n'));
  const { countriesByName } = await getCountries();
  for (let countryId = 1; countryId <= MAX_COUNTRY_ID; countryId++) {
    const response: DeloitteApiResponseObject = await getDitsDeloitteData(countryId);
    if (response.status !== 200) {
      throw new InternalServerError(`Deloitte API returned status: ${response.status}`);
    }
    if (!response.data.longDesc) continue;
    const ditsOrigin = response.data.domesticRates.withholdingTax[0];

    const differenceOrigin = differenceInDays(new Date(), new Date(ditsOrigin.modifyDate!));
    if (differenceOrigin <= 7) {
      console.log(`Difference of ${differenceOrigin} days for countryId: ${countryId}`);
    }

    const originCountry = countriesByName[whtCountryMapper[response.data.longDesc] || response.data.longDesc];
    if (!originCountry) continue;

    const originToCreate: DeloitteApiWHTOriginType = mapOriginObject(originCountry.id, ditsOrigin);
    const { dividend, interest, royalty } = originToCreate;
    if (!dividend && !interest && !royalty && response.data.treatyRates.length === 0) continue;
    for (const recipientToCreate of response.data.treatyRates) {
      const differenceRecipient = differenceInDays(new Date(), new Date(recipientToCreate.modifyDate!));
      if (differenceRecipient <= 7) {
        console.log(
          `Difference of ${differenceRecipient} days for countryId: ${countryId} and recipient: ${recipientToCreate.longDesc}`,
        );
      }
    }
  }
  console.log(chalk.green('\n====DONE FETCHING WITHHOLDING TAX DATA====\n'));
};

run();
