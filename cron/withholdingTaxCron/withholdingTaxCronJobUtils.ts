import axios from 'axios';
import { differenceInDays } from 'date-fns';

import {
  DeloitteApiWHTRecipientType,
  DeloitteApiWHTOriginType,
  DeloitteOriginType,
  DeloitteRecipientType,
  DeloitteWHTOriginType,
  DeloitteApiResponseObject,
  DeloitteOriginMailDataType,
  DeloitteRecipientMailDataType,
  CountryType,
} from '../../types';
import { whtOriginRepository, whtRecipientRepository } from '../../repositories';

const DELOITTE_API_URL = 'https://dits.deloitte.com/api/jurisdiction';

export const getDitsDeloitteData = (id: number): Promise<DeloitteApiResponseObject> => {
  return axios({
    url: DELOITTE_API_URL,
    method: 'GET',
    params: { type: 'home', id },
  });
};

export const mapOriginObject = (countryId: number, origin: DeloitteOriginType): DeloitteApiWHTOriginType => ({
  countryId,
  dividend: origin.dividends,
  interest: origin.interest,
  royalty: origin.royalties,
  date: new Date(),
});

export const mapRecipientObject = (
  originId: number,
  recipientCountryId: number,
  recipient: DeloitteRecipientType,
): DeloitteApiWHTRecipientType => ({
  originId,
  countryId: recipientCountryId,
  dividend: recipient.dividends,
  interest: recipient.interests,
  royalty: recipient.royalties,
});

/** Checks if modify date has been in the last day and interest has changed and returns an object that will be used to create an email */
export const checkRecipientModifyDate = async (
  origin: DeloitteWHTOriginType,
  originCountry: CountryType,
  recipient: DeloitteRecipientType,
  recipientCountry: CountryType,
): Promise<DeloitteRecipientMailDataType | undefined> => {
  if (!recipient.modifyDate || !recipient.longDesc) return;

  const previousRecipient = await whtRecipientRepository.getLatestRecipientWhtData(
    origin.countryId,
    recipientCountry.id,
  );

  if (previousRecipient?.interest === recipient.interests) return;

  return {
    originCountry: originCountry.name,
    country: recipientCountry.name,
    previousInterestRate: previousRecipient?.interest,
    currentInterestRate: recipient.interests,
  };
};

/* Same thing as checkRecipientModifyDate but for origins */
export const checkOriginModifyDate = async (
  originData: DeloitteOriginType,
  originCountryId: number,
): Promise<DeloitteOriginMailDataType | undefined> => {
  if (!originData.modifyDate || !originData.jurisdictionName) return;

  if (differenceInDays(new Date(), new Date(originData.modifyDate)) !== 0) return;

  const previousOrigin = await whtOriginRepository.getLatestOriginWhtData(originCountryId);

  if (previousOrigin?.interest === originData.interest) return;

  return {
    country: originData.jurisdictionName,
    previousInterestRate: previousOrigin?.interest,
    currentInterestRate: originData.interest,
  };
};
