import _ from 'lodash';

import * as statementParsers from '../../../services/statementParsers';
import * as statementUtils from '../../../utils/statementUtils';
import { cashPoolStatementDataRepository } from '../../../repositories';
import * as config from './config';

const processStatement = async (filePath: string | Buffer, cashPoolId: number) => {
  const { accountIdMapper } = await statementUtils.getAccountIdentificationToAccountIdMapping(
    config.userData.clientId,
    cashPoolId,
  );

  const statementsDataForDb = await statementParsers.camt053Parser.parse(filePath, accountIdMapper, cashPoolId);

  if (statementsDataForDb.length === 0) throw new Error('No data in statement file.');

  await cashPoolStatementDataRepository.bulkCreate(statementsDataForDb);
};

export default processStatement;
