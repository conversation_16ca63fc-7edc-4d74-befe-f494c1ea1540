import { cashPoolEnums } from '../../../enums';
import cashPoolService from '../../../services/cashPoolService';
import { UploadBatchFileType } from '../../../types';

import { userData } from './config';

const { clientId } = userData;

const runBatches = (createdBatchFiles: Array<UploadBatchFileType>) => {
  return Promise.all(
    createdBatchFiles.map(({ cashPoolId, id }) => {
      return cashPoolService.runBatchPhysical({
        shouldCreatePayments: false,
        batchStatus: cashPoolEnums.batchStatus.AUTO_GENERATED,
        clientId,
        cashPoolId,
        batchId: id,
      });
    }),
  );
};

export default runBatches;
