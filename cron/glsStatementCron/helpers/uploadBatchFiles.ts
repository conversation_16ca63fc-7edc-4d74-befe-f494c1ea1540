import _ from 'lodash';

import { cashPoolEnums } from '../../../enums';
import cashPoolService from '../../../services/cashPoolService';

import { userData } from './config';

const { clientId, userId } = userData;

type BatchFileWithMetadata = {
  buffer: Buffer;
  sheetAsJson: Array<any>;
  cashPoolId: number;
  filename: string;
};

const uploadBatchFiles = async (batchFiles: Array<BatchFileWithMetadata>) => {
  const mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

  return Promise.all(
    batchFiles.map(({ buffer, cashPoolId, filename }) => {
      return cashPoolService.uploadBatchFile({
        batchStatus: cashPoolEnums.batchStatus.AUTO_GENERATED,
        clientId,
        userId,
        cashPoolId,
        file: { buffer, size: buffer.byteLength, originalname: filename, mimetype },
      });
    }),
  );
};

export default uploadBatchFiles;
