const fs = require('fs');
const { unlink } = require('fs/promises');
const { EOL } = require('os');
const readline = require('readline');
const { parentPort } = require('worker_threads');

const { ftp } = require('../../config/config');
const { sequelize } = require('../../models');
const { parseProviderData } = require('../../controllers/workers/providerDataWorker');
const providerDataRepository = require('../../repositories/providerDataRepository');
const { providerDataFtpClient } = require('../../services/providerDataService/providerDataFtpClient');
const { parseDate } = require('../../utils/dates');

function getFile() {
  let targetIssueDate: string;

  const filenamePattern = 'curves\\.\\d+\\.csv';
  const filenameRegEx = new RegExp(filenamePattern);
  const dailyFileRegEx = new RegExp(`^${filenamePattern}$`);

  providerDataFtpClient.list(`${ftp.username}`, async (err: any, res: any) => {
    if (err) {
      parentPort.postMessage(err);
    } else {
      const files = res.split(EOL).filter((line: string) => line.match(filenameRegEx));
      for (let i = 0; i < files.length; i++) {
        const fileLineChunks = files[i].split(' ');
        const file = fileLineChunks[fileLineChunks.length - 1]?.trim();
        if (dailyFileRegEx.test(file)) {
          const fileNameChunks = file.split('.');
          const issueDate = fileNameChunks[1];
          const parsedIssueDate = parseDate(issueDate);
          if (!(await providerDataRepository.issueDateExists(parsedIssueDate))) {
            targetIssueDate = issueDate;
            break;
          }
        }
      }

      if (targetIssueDate) {
        providerDataFtpClient.get(`${ftp.username}/curves.${targetIssueDate}.csv`, async (err: any, stream: any) => {
          if (err) {
            parentPort.postMessage(err);
          } else {
            stream.once('close', async (err: any) => {
              if (err) {
                parentPort.postMessage(err);
              } else {
                try {
                  await getData();
                } catch (err) {
                  parentPort.postMessage(err);
                }
              }
            });

            stream.pipe(fs.createWriteStream('/datadrive/curves.csv'));
          }
        });
      }
    }
  });
}

async function getData() {
  await sequelize.transaction(async () => {
    const readInterface = readline.createInterface({
      input: fs.createReadStream('/datadrive/curves.csv'),
      console: false,
    });

    await parseProviderData(readInterface);
    await unlink('/datadrive/curves.csv');
  });
}

getFile();
