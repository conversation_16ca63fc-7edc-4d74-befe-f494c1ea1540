const CronJob = require('cron').CronJob;
import { Worker } from 'worker_threads';

import logger from '../../utils/logger';

const walwilSftpDataCronJob = new CronJob(
  // '*/7 * * * * *',
  '0 1 * * *',
  async () => {
    try {
      await new Promise((resolve, reject) => {
        const worker = new Worker('./cron/walwilStatementCron/walwilStatementCronJob.import.js');
        worker.on('message', (value: any) => {
          if (value instanceof Error) reject(value);
          else resolve(value);
        });
        worker.on('error', reject);
        worker.on('exit', (code: number) => {
          if (code !== 0) reject(new Error(`Worker stopped with exit code ${code}`));
        });
      });
      logger.info('cron job done');
    } catch (err: any) {
      logger.error({ message: err?.message, error: err });
    }
  },
  null,
  true,
);

export = { walwilSftpDataCronJob };
