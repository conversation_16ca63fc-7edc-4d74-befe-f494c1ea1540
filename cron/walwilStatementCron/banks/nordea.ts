import fs from 'fs/promises';
import SFTPClient from 'ssh2-sftp-client';

import * as walwilSftpConfig from '../sftp.config';
import { cashPoolStatementDataRepository } from '../../../repositories';
import logger from '../../../utils/logger';
import * as statementParsers from '../../../services/statementParsers';
import * as statementUtils from '../../../utils/statementUtils';
import hardcodedIds from './hardcodedIds';

export const nordeaStatementJob = async () => {
  const sftp = new SFTPClient();
  const sftpConfig = await walwilSftpConfig.getSftpConfig();
  const remoteDir = 'upload/nordea';
  const localDir = 'walwil_nordea_sftpdownload';

  try {
    await sftp.connect(sftpConfig);
    await sftp.downloadDir(remoteDir, localDir);

    const { accountIdMapper } = await statementUtils.getAccountIdentificationToAccountIdMapping(
      hardcodedIds.WALWIL_CLIENT_ID,
      hardcodedIds.NORDEA_CASH_POOL_ID,
    );

    const filenames = await fs.readdir(localDir);
    for (const filename of filenames) {
      const statementsDataForDb = await statementParsers.nordeaStatementParser.parse(
        `${localDir}/${filename}`,
        accountIdMapper,
      );
      await cashPoolStatementDataRepository.bulkCreate(statementsDataForDb);
    }
  } catch (error: any) {
    logger.error({ message: 'WalWil Nordea Statement Job Failed', error });
    throw error;
  } finally {
    await fs.rm(localDir, { recursive: true, force: true });
    sftp.end();
  }
};
