#!/bin/bash
echo "Using ${SERVER_USER} to deploy to ${SERVER_IP}"

each_variable_in_seperate_row=$(echo "$API_ENV_VARIABLES" | tr ' ' '\n')
echo "$each_variable_in_seperate_row" > .env

npm install
npm run build

scp -r dist/ ${SERVER_USER}@${SERVER_IP}:/srv/nord/nord-api
scp .env ${SERVER_USER}@${SERVER_IP}:/srv/nord/nord-api

echo "Finished copying the build files"

ssh $SERVER_USER@$SERVER_IP 'cd /srv/nord/nord-api && npm run migrate:and:seed && pm2 reload all'

echo "Finished migrating db and reloading pm2"
