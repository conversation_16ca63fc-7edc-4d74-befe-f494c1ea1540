import _ from 'lodash';

import * as statementParsers from '../services/statementParsers';
import * as statementUtils from '../utils/statementUtils';
import { cashPoolStatementDataRepository } from '../repositories';
import { config } from './helpers';

const getAccountIdMapper = async (
  clientId: number,
  cashPoolId: number,
  predefinedAccountIdMapper?: Record<string, number>,
) => {
  if (predefinedAccountIdMapper) return predefinedAccountIdMapper;

  const { accountIdMapper } = await statementUtils.getAccountIdentificationToAccountIdMapping(clientId, cashPoolId);

  return accountIdMapper;
};

const processStatement = async (
  filePath: string | Buffer,
  cashPoolId: number,
  predefinedAccountIdMapper?: Record<string, number>,
) => {
  const accountIdMapper = await getAccountIdMapper(config.userData.clientId, cashPoolId, predefinedAccountIdMapper);

  const statementsDataForDb = await statementParsers.camt053Parser.parse(filePath, accountIdMapper, cashPoolId);

  if (statementsDataForDb.length === 0) throw new Error('No data in statement file.');

  await cashPoolStatementDataRepository.bulkCreate(statementsDataForDb);
};

export default processStatement;
