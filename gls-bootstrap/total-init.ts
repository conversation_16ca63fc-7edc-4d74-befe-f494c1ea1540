/* eslint-disable no-process-exit */
/* eslint-disable no-console */
import _ from 'lodash';

import models from '../models';
import {
  cashPoolParticipantAccountRepository,
  cashPoolParticipantAccountIdsRepository,
  companyRepository,
} from '../repositories';
import logger from '../utils/logger';
import { config } from './helpers';

import createCashPool from './createCashPool';

export const slovakiaId = 522;
export const austriaId = 476;
export const czechRepublicId = 486;
export const hungaryId = 507;

export const germanyGmbh = 487;
export const servicesGmbh = 488;
export const overnightServiceGmbh = 494;
export const derKurierGmbh = 493;
export const verwaltungsGmbh = 491;
export const eComLabGmbH = 489;
export const generalLogisticsSystemsSpain = 499;
export const generalLogisticsSystemsPoland = 517;
export const mobilitySolutionsGmbH = 492;
export const dERKURIERBeteiligungsgesellschaftGmbH = 495;
export const beteiligungsGmbH = 490;

export const franceSas = 502;
export const investFranceSas = 503;
export const belgiumDistributionNv = 478;
export const belgiumNv = 477;
export const portugalLda = 518;
export const irelandLtd = 508;
export const italySpa = 509;

const parentCompanyId = 529;
const parentCompany = {
  id: parentCompanyId,
  parentCompanyId,
  name: 'International Distribution Services Ltd',
  industry: 'Industrials',
  country: 'United Kingdom',
  creditRating: {
    rating: 'BBB/Baa2',
    ratingAdj: null,
    probabilityOfDefault: null,
    probabilityOfDefaultAdj: null,
  },
  assessment: null,
} as const;

const topLeader = {
  id: 516,
  parentCompanyId: parentCompany.id,
  name: 'General Logistics Systems B.V.',
  industry: 'Industrials',
  country: 'Netherlands',
  creditRating: {
    rating: 'A+/A1',
    ratingAdj: 'BBB/Baa2',
    probabilityOfDefault: 0.18,
    probabilityOfDefaultAdj: 0.57,
  },
  assessment: {
    answers: {
      ringFencing: false,
      question1: true,
      question2: false,
      question3: false,
      question4: false,
      question5: true,
      question6: true,
      question7: true,
      question8: true,
      question9: true,
      question10: true,
    },
    name: 'Nearly Central',
  },
  assessmentName: 'Nearly Central',
} as const;

const commerzBankBottomLeaderCompany = {
  id: beteiligungsGmbH,
  parentCompanyId: parentCompany.id,
  name: 'GLS Beteiligungs GmbH',
  industry: 'Industrials',
  country: 'Germany',
  creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
  assessment: null,
} as const;

const companies = [
  topLeader,
  commerzBankBottomLeaderCompany,
  {
    id: slovakiaId,
    parentCompanyId: parentCompany.id,
    name: 'GLS General Logistics Systems Slovakia s.r.o.',
    industry: 'Industrials',
    country: 'Slovakia',
    creditRating: {
      rating: 'A-/A3',
      ratingAdj: 'BBB/Baa2',
      probabilityOfDefault: 0.36,
      probabilityOfDefaultAdj: 0.684,
    },
    assessment: null,
  },
  {
    id: austriaId,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Austria GmbH',
    industry: 'Industrials',
    country: 'Austria',
    creditRating: {
      rating: 'BBB-/Baa3',
      ratingAdj: 'BBB-/Baa3',
      probabilityOfDefault: 1.03,
      probabilityOfDefaultAdj: 1.03,
    },
    assessment: null,
  },
  {
    id: czechRepublicId,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Czech Republic s.r.o.',
    industry: 'Industrials',
    country: 'Czech Republic',
    creditRating: {
      rating: 'A-/A3',
      ratingAdj: 'BBB/Baa2',
      probabilityOfDefault: 0.42,
      probabilityOfDefaultAdj: 0.798,
    },
    assessment: null,
  },
  {
    id: hungaryId,
    parentCompanyId: parentCompany.id,
    name: 'GLS General Logistics Systems Hungary Kft.',
    industry: 'Industrials',
    country: 'Hungary',
    creditRating: {
      rating: 'AA+/Aa1',
      ratingAdj: 'BBB/Baa2',
      probabilityOfDefault: 0.13,
      probabilityOfDefaultAdj: 1.235,
    },
    assessment: null,
  },
  {
    id: germanyGmbh,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Germany GmbH & Co. OHG',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: servicesGmbh,
    parentCompanyId: parentCompany.id,
    name: 'GLS IT Services GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: verwaltungsGmbh,
    parentCompanyId: parentCompany.id,
    name: 'GLS Verwaltungs - und Services GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: mobilitySolutionsGmbH,
    parentCompanyId: parentCompany.id,
    name: 'GLS Mobility Solutions GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: dERKURIERBeteiligungsgesellschaftGmbH,
    parentCompanyId: parentCompany.id,
    name: 'DER KURIER Beteiligungsgesellschaft GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: derKurierGmbh,
    parentCompanyId: parentCompany.id,
    name: 'DER KURIER GmbH & Co. KG',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: overnightServiceGmbh,
    parentCompanyId: parentCompany.id,
    name: 'Overnight Service GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: eComLabGmbH,
    parentCompanyId: parentCompany.id,
    name: 'GLS eCom Lab GmbH',
    industry: 'Industrials',
    country: 'Germany',
    creditRating: {
      rating: 'AA/Aa2',
      ratingAdj: 'AA/Aa2',
      probabilityOfDefault: 4,
      probabilityOfDefaultAdj: 4,
    },
    assessment: null,
  },
  {
    id: generalLogisticsSystemsSpain,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Spain, S.A',
    industry: 'Industrials',
    country: 'Spain',
    creditRating: {
      rating: 'AA/Aa2',
      ratingAdj: 'AA/Aa2',
      probabilityOfDefault: 4,
      probabilityOfDefaultAdj: 4,
    },
    assessment: null,
  },
  {
    id: generalLogisticsSystemsPoland,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Poland Sp. z o.o.',
    industry: 'Industrials',
    country: 'Poland',
    creditRating: {
      rating: 'AA/Aa2',
      ratingAdj: 'AA/Aa2',
      probabilityOfDefault: 4,
      probabilityOfDefaultAdj: 4,
    },
    assessment: null,
  },
  {
    id: franceSas,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems France S.A.S.',
    industry: 'Industrials',
    country: 'France',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: investFranceSas,
    parentCompanyId: parentCompany.id,
    name: 'GLS Invest France SAS',
    industry: 'Industrials',
    country: 'France',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: belgiumDistributionNv,
    parentCompanyId: parentCompany.id,
    name: 'GLS Belgium Distribution N.V.',
    industry: 'Industrials',
    country: 'Belgium',
    creditRating: {
      rating: 'A-/A3',
      ratingAdj: 'BBB/Baa2',
      probabilityOfDefault: 0.27,
      probabilityOfDefaultAdj: 0.513,
    },
    assessment: null,
  },
  {
    id: belgiumNv,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Belgium N.V.',
    industry: 'Industrials',
    country: 'Belgium',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: portugalLda,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Portugal Lda.',
    industry: 'Industrials',
    country: 'Portugal',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: irelandLtd,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Ireland Ltd.',
    industry: 'Industrials',
    country: 'Ireland',
    creditRating: { rating: null, ratingAdj: null, probabilityOfDefault: null, probabilityOfDefaultAdj: null },
    assessment: null,
  },
  {
    id: italySpa,
    parentCompanyId: parentCompany.id,
    name: 'General Logistics Systems Italy S.p.A',
    industry: 'Industrials',
    country: 'Italy',
    creditRating: {
      rating: 'BBB+/Baa1',
      ratingAdj: 'BBB/Baa2',
      probabilityOfDefault: 0.34,
      probabilityOfDefaultAdj: 0.403,
    },
    assessment: null,
  },
];

const commerzbankBottomLayerCashPoolBottom = {
  id: 6000,
  externalId: '**********************',
  type: 'Physical',
  assessment: 'Medium',
  country: 'Germany',
  creditInterestRate: 1.25,
  debitInterestRate: 4.25,
  currencies: 'EUR',
  interestType: 'fixed',
  leaderId: commerzBankBottomLeaderCompany.id,
  name: 'Commerzbank Bottom Layer',
  operatingCost: 0,
  operatingCostMarkup: 0,
  overnightRate: null,
  totalRisk: 0.65,
  accounts: [
    {
      companyId: commerzBankBottomLeaderCompany.id,
      creditInterestRate: 1.24,
      debitInterestRate: 4.***************,
      isLeader: true,
    },
    { companyId: germanyGmbh, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
    { companyId: servicesGmbh, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
    { companyId: verwaltungsGmbh, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
    { companyId: mobilitySolutionsGmbH, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
    {
      companyId: dERKURIERBeteiligungsgesellschaftGmbH,
      creditInterestRate: 1.24,
      debitInterestRate: 4.***************,
    },
    { companyId: derKurierGmbh, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
    { companyId: overnightServiceGmbh, creditInterestRate: 1.24, debitInterestRate: 4.*************** },
  ],
  riskAnalysisAnswers: {
    guarantee: false,
    liquidityRisk1: false,
    liquidityRisk2: false,
    creditRisk1: false,
    creditRisk2: false,
    functions1: false,
    functions2: false,
    functions3: false,
    functions4: false,
    functions5: false,
    functions6: false,
  },
  clientId: config.userData.clientId,
};
const commerzbankTopLayerCashPoolTop = {
  id: 7000,
  externalId: '******************',
  type: 'Physical',
  assessment: 'Medium',
  country: 'Netherlands',
  creditInterestRate: 1.25,
  debitInterestRate: 4.25,
  currencies: 'EUR',
  interestType: 'fixed',
  leaderId: topLeader.id,
  name: 'Commerzbank Top Layer',
  operatingCost: 0,
  operatingCostMarkup: 0,
  overnightRate: null,
  totalRisk: 0.65,
  accounts: [
    { companyId: topLeader.id, creditInterestRate: 0.1, debitInterestRate: 4.***************, isLeader: true },
    { companyId: eComLabGmbH, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: generalLogisticsSystemsSpain, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: generalLogisticsSystemsPoland, creditInterestRate: 0.1, debitInterestRate: 4.**************** },
    { companyId: beteiligungsGmbH, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
  ],
  riskAnalysisAnswers: {
    guarantee: false,
    liquidityRisk1: false,
    liquidityRisk2: false,
    creditRisk1: false,
    creditRisk2: false,
    functions1: false,
    functions2: false,
    functions3: false,
    functions4: false,
    functions5: false,
    functions6: false,
  },
  clientId: config.userData.clientId,
};
const unicreditTopCashPool = {
  id: 5000,
  externalId: '********************',
  type: 'Physical',
  assessment: 'Medium',
  country: 'Austria',
  creditInterestRate: 1,
  debitInterestRate: 4.25,
  currencies: 'EUR',
  interestType: 'fixed',
  leaderId: topLeader.id,
  name: 'UniCredit Top Layer',
  operatingCost: 0,
  operatingCostMarkup: 0,
  overnightRate: null,
  totalRisk: 0.65,
  accounts: [
    { companyId: topLeader.id, creditInterestRate: 0.1, debitInterestRate: 4.***************, isLeader: true },
    { companyId: slovakiaId, creditInterestRate: 0.1, debitInterestRate: 4.**************** },
    { companyId: austriaId, creditInterestRate: 0.1, debitInterestRate: 4.**************** },
    { companyId: czechRepublicId, creditInterestRate: 0.1, debitInterestRate: 4.**************** },
    { companyId: hungaryId, creditInterestRate: 0.1, debitInterestRate: 4.**************** },
  ],
  riskAnalysisAnswers: {
    guarantee: false,
    liquidityRisk1: false,
    liquidityRisk2: false,
    creditRisk1: false,
    creditRisk2: false,
    functions1: false,
    functions2: false,
    functions3: false,
    functions4: false,
    functions5: false,
    functions6: false,
  },
  clientId: config.userData.clientId,
};
const bnpTopCashPool = {
  id: 9000,
  externalId: '******************',
  type: 'Physical',
  assessment: 'Medium',
  country: 'Netherlands',
  creditInterestRate: 3.4,
  debitInterestRate: 4.25,
  currencies: 'EUR',
  interestType: 'fixed',
  leaderId: topLeader.id,
  name: 'BNP Top Layer',
  operatingCost: 0,
  operatingCostMarkup: 0,
  overnightRate: null,
  totalRisk: 0.65,
  accounts: [
    { companyId: topLeader.id, creditInterestRate: 0.1, debitInterestRate: 4.***************, isLeader: true },
    { companyId: franceSas, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: investFranceSas, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: belgiumDistributionNv, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: belgiumNv, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: portugalLda, creditInterestRate: 0.1, debitInterestRate: 4.3 },
    { companyId: irelandLtd, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
    { companyId: italySpa, creditInterestRate: 0.1, debitInterestRate: 4.*************** },
  ],
  riskAnalysisAnswers: {
    guarantee: false,
    liquidityRisk1: false,
    liquidityRisk2: false,
    creditRisk1: false,
    creditRisk2: false,
    functions1: false,
    functions2: false,
    functions3: false,
    functions4: false,
    functions5: false,
    functions6: false,
  },
  clientId: config.userData.clientId,
};
const commerzBottomCompanyIdMapper = {
  [commerzBankBottomLeaderCompany.id]: ['CommerzbankBottomLeader'],
  [germanyGmbh]: ['W/990101'],
  [servicesGmbh]: ['W/990102'],
  [verwaltungsGmbh]: ['W/990106'],
  [mobilitySolutionsGmbH]: ['W/990116'],
  [dERKURIERBeteiligungsgesellschaftGmbH]: ['W/990110'],
  [derKurierGmbh]: ['W/990111'],
  [overnightServiceGmbh]: ['W/990114'],
};
const commerzTopCompanyIdMapper = {
  [topLeader.id]: ['CommerzbankTopLeader'],
  [eComLabGmbH]: ['W/DE45520800800460052600'],
  [generalLogisticsSystemsSpain]: ['W/ ************'],
  [generalLogisticsSystemsPoland]: ['TODO'],
  [beteiligungsGmbH]: ['W/**********************', 'W/990 302'],
};
const unicreditTopCompanyIdMapper = {
  [topLeader.id]: ['UnicreditTopLeader'],
  [slovakiaId]: ['CP01470459005'],
  [austriaId]: ['CP80017802400'],
  [czechRepublicId]: ['CP03002840015'],
  [hungaryId]: ['CP000256360044'],
};
const bnpTopCompanyIdMapper = {
  [topLeader.id]: ['BNPTopLeader'],
  [franceSas]: ['FR7630004024970001105323577'],
  [investFranceSas]: ['FR7630004024970001105333277'],
  [belgiumDistributionNv]: ['BE04210005750031EUR'],
  [belgiumNv]: ['BE71001376407869EUR'],
  [portugalLda]: ['PT50003401090013699017044'],
  [irelandLtd]: ['IE35BNPA99020624830153'],
  [italySpa]: ['IT58U0100501660000000000495'],
};

const initBalances = async () => {
  const initialBalances = [
    { externalId: 'CommerzbankTopLeader', attributesToUpdate: { balance: 4_500_751.63 } },

    { externalId: 'UnicreditTopLeader', attributesToUpdate: { balance: 2489.46 } },
    { externalId: 'CP80017802400', attributesToUpdate: { balance: -30 } },

    { externalId: 'BNPTopLeader', attributesToUpdate: { balance: 16_998_839.58 } },
  ];

  for (const { externalId, attributesToUpdate } of initialBalances) {
    const cashPoolParticipantAccount = await cashPoolParticipantAccountIdsRepository.getOne({
      where: { externalId },
    });
    if (cashPoolParticipantAccount == null) continue;

    const [updatedRows] = await cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount({
      where: { id: cashPoolParticipantAccount.account.id },
      attributesToUpdate,
    });
    if (updatedRows !== 1) throw new Error('Initial balance not updated correctly');
  }
};

(async () => {
  await models.sequelize.transaction(async () => {
    try {
      await companyRepository.createCompany(parentCompany, config.userData);

      await companyRepository.createCompanies(companies, config.userData);

      await createCashPool(commerzbankBottomLayerCashPoolBottom, config.userData, commerzBottomCompanyIdMapper);
      await createCashPool(commerzbankTopLayerCashPoolTop, config.userData, commerzTopCompanyIdMapper);
      await createCashPool(unicreditTopCashPool, config.userData, unicreditTopCompanyIdMapper);
      await createCashPool(bnpTopCashPool, config.userData, bnpTopCompanyIdMapper);

      await initBalances();
    } catch (error) {
      logger.error({ message: 'Main function failed', error: error as Error });
      throw error;
    }
  });
  console.log('DB initialized.');
  process.exit(0);
})();
