/* eslint-disable no-console */
/* eslint-disable node/no-unsupported-features/es-syntax */
import * as dotenv from 'dotenv';
dotenv.config();

import { parseISO, addDays, format } from 'date-fns';

import processStatement from './processStatement';
import models from '../models';

/** To be used comment out leader related logic in camt053Extractor.ts */
(async () => {
  const azureService = await import('../services/azureService');

  const cashPoolId = 5000;

  const startDate = '2024-06-01';
  const endDate = '2024-07-02';

  const accountIdMapper = {
    CP01102356627: 72,
    CP00105149768: 73,
  };

  let currentDate = parseISO(startDate);
  const end = parseISO(endDate);

  try {
    await models.sequelize.transaction(async () => {
      while (currentDate <= end) {
        const filename = `${cashPoolId}_${format(currentDate, 'yyyy-MM-dd')}.xml`;

        try {
          console.log(filename);
          const fileBuffer = await azureService.getFile(`statement/15/${filename}`);

          await processStatement(fileBuffer, cashPoolId, accountIdMapper);
        } catch (error: any) {
          if (error.statusCode !== 404) {
            throw error;
          }
        }

        currentDate = addDays(currentDate, 1);
      }
    });
  } catch (error) {
    console.error(error);
  }
})();
