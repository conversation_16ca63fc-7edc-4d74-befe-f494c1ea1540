# Pooling Flow

## Setup

Initial setup step includes removing all companies and cash pools for clientId = 15, which is GLS.
Total init script then creates all the companies and pools.

```bash
npx ts-node gls-bootstrap/resetdb.ts
npx ts-node gls-bootstrap/total-init.ts
```

## Running The Pools

The stuff that the pooling does can be changed bu commenting out specific stuff from `cron-job.ts`.
It's meant to work by first downloading all the statements from the SFTP folders and storing them locally.
Files are first parsed to get the `cashPoolId` and the `date` and the file is renamed to include that data so it's easier to work with the files.

After renaming is done, for each of the files the cash pool job is run.

### ExternalId <-> AccountId Mapping

First step of the cash pool job is to get the mappings between `externalId` (IDs clients use on their end) and `cashPoolAccountId` we use for `CashPoolParticipantAccounts`. `cashPoolAccountId` and `accountId` are used interchangeably in the documentation and code.

### Camt053 Parsing

Next step is the parsing of the file. Note that the parsing was already done when renaming the files, but this is done again for simplicity of the code. This should be refactored.

`camt053Extractor.extract` does the extraction of data important for the job. It creates objects that get inserted in the `CashPoolStatementData` table. Data is extracted from <Ntry> tags.

First step is to check if the value in the <Cd> tag isn't in the expected array. If it's not the balance is added to the leader's balance. Values in the expected array are different for different banks.

Next, all the strings in the tags that could contain the `externalId` are concatenated and then regex is used to try and find externalIds in that string. The related `externalIds` are fetched from the database based on the `cashPoolId`. Balance is accumulated for all accounts.
Note that one account can have multiple externalIds. That means `accountIdMapper` will have the same value for two different keys. Balance is accumulated based on our `accountId` and because it's mapped from their `externalId` (`accountIdMapper` has multiple different keys (`externalId`), but they map to the same value (`accountId`)) to our `accountId` it's not a problem.

### Preparing Statement Data for DB Insert

Because new balance is accumulated it's dependent on the current date's balance. The current balance is kept in the `CashPoolParticipantAccounts` `balance` column and the new balance is just added to it.
Final thing to do before inserting is to create the statement data for accounts that didn't appear in the statement and therefore their new balance is the same as the current.

### Creating the Excel Template and Running the Cash Pool Model

The Excel batch file is created in the same structure as the template in the Manual Flow of the cash pool uses. The batch is uploaded to Azure Storage and the model is run. This part here could also be be optimized because the code currently uploads the batch file to Azure Storage and is then downloaded in the `runBatchPhysical` function. That's because `runBatchPhysical` is used for Manual Flow and this way the same code is reused. This should be refactored after the cash pool module is more stable.

## Various Notes

Sometimes in bottom layer pools there appear transactions of the top leader pool. Those are currently ignored and they are logged as skipped. They have these IDs:

- W/ ************
- W/******************
