export const userData = { clientId: 15, userId: 73, username: 'herman.<PERSON><PERSON><PERSON><PERSON><PERSON>' } as const;

const remoteDirRoot = '_btm.mappingEngine';
export const remoteDirs = [`${remoteDirRoot}/CAMT_053_001_02`, `${remoteDirRoot}/CAMT_053_001_08`] as const;
export type RemoteDirType = typeof remoteDirs[number];

export const localDir = 'gls-bootstrap/statements';

export const externalIdMapper = {
  AT381200010039182687: 5000,
  DE59520800800463623500: 6000,
  NL10COBA0637067940: 7000,
  NL60BNPA0227753712: 9000,
} as const;
export const validCashPoolIds = Object.values(externalIdMapper);
