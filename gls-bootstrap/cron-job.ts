/* eslint-disable node/no-unsupported-features/es-syntax */
import * as dotenv from 'dotenv';
dotenv.config();

import fs from 'fs/promises';
import { Parser } from 'xml2js';
import SFTPClient from 'ssh2-sftp-client';
import _ from 'lodash';
import { isWithinInterval } from 'date-fns';

import { camt053Schema, Camt053Type } from '../schemas/statementSchemas';
import models from '../models';
import logger from '../utils/logger';
import * as camtExtractorUtils from '../services/statementParsers/extractors/camt053.utils';

import { getSftpConfig, config } from './helpers';
import processStatement from './processStatement';

type FilenameType = `${number}_${string}`;
type RemotePathType = `${config.RemoteDirType}/${string}`;

const downloadFilesFromSFTP = async (
  files: SFTPClient.FileInfo[],
  sftpClient: SFTPClient,
  remoteDir: config.RemoteDirType,
): Promise<void> => {
  for (const file of files) {
    const filename = file.name;
    await sftpClient.get(`${remoteDir}/${filename}`, `${config.localDir}/${filename}`);
  }
};

/**
 * Returned object contains mapping between the new filenames and and their remote paths.
 * This is used to track which files were processed and to delete them from the SFTP server after processing.
 */
const renameFiles = async (
  files: SFTPClient.FileInfo[],
  remoteDir: config.RemoteDirType,
): Promise<Record<string, string>> => {
  const newFilenameToRemotePathMapper: Record<FilenameType, RemotePathType> = {};

  for (const file of files) {
    const remoteFilename = file.name;
    const localFilepath = `${config.localDir}/${remoteFilename}`;

    const parsedXml = await new Parser().parseStringPromise(await fs.readFile(localFilepath));

    const camtJson: Camt053Type = await camt053Schema.parseAsync(parsedXml);

    const cashPoolExternalId = camtExtractorUtils.getIban(camtJson);
    const date = camtExtractorUtils.getStatementDate(camtJson);

    const cashPoolId = config.externalIdMapper[cashPoolExternalId as keyof typeof config.externalIdMapper];
    if (!cashPoolId) throw new Error(`Cash pool with external id ${cashPoolExternalId} not found.`);

    const newFilename: FilenameType = `${cashPoolId}_${date}`;

    newFilenameToRemotePathMapper[newFilename] = `${remoteDir}/${remoteFilename}`;

    await fs.rename(localFilepath, `${config.localDir}/${newFilename}.xml`);
  }

  return newFilenameToRemotePathMapper;
};

const isValidDate = (date: string): boolean => {
  return isWithinInterval(new Date(date), { start: new Date(2023, 0, 1), end: new Date(3000, 11, 31) });
};

(async () => {
  const sftpClient = new SFTPClient();

  try {
    await models.sequelize.transaction(async () => {
      const azureService = await import('../services/azureService');

      await fs.access(config.localDir).catch(async () => await fs.mkdir(config.localDir));

      await sftpClient.connect(await getSftpConfig());
      logger.toFile('Connected to SFTP.\n');
      logger.toFile('Downloading files...\n');

      /*for (const remoteDir of config.remoteDirs) {
        const files = await sftpClient.list(remoteDir);

        await downloadFilesFromSFTP(files, sftpClient, remoteDir);
        logger.toFile(`Downloaded files from ${remoteDir}`);

        await renameFiles(files, remoteDir);
        logger.toFile(`Renamed files from ${remoteDir}\n`);
      }*/

      const files = await fs.readdir(config.localDir);

      for (const filename of files) {
        const filenameNoExtension = filename.split('.xml')[0] as FilenameType;
        const [cashPoolIdString, date] = filenameNoExtension.split('_');
        const cashPoolId = Number(cashPoolIdString);

        if (filename.includes('.weekend')) continue;

        if (!isValidDate(date)) throw new Error(`date not a valid date: ${date}`);
        if (!config.validCashPoolIds.includes(cashPoolId as any)) {
          logger.toFile(`Unexpected cash pool ID: ${cashPoolId}`);
          continue;
        }

        logger.toFile(`Processing file ${filename}.`);

        const fileBuffer = await fs.readFile(`${config.localDir}/${filename}`);

        await processStatement(fileBuffer, cashPoolId);

        await azureService.uploadFile(
          `statement/${config.userData.clientId}/${filename}`,
          fileBuffer,
          fileBuffer.byteLength,
        );

        logger.toFile(`Processed file ${filename}.\n`);
      }
      // await fs.rm(config.localDir, { recursive: true, force: true });
    });
  } catch (error: any) {
    // eslint-disable-next-line no-console
    console.error(error);
  } finally {
    sftpClient.end();
    logger.toFile('Disconnected from SFTP.');
    logger.toFile('Job finished.\n\n\n\n');
  }
})();
