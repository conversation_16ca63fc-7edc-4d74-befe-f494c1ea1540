import _ from 'lodash';

import {
  cashPoolRepository,
  topCurrencyAccountRepository,
  cashPoolParticipantAccountRepository,
  cashPoolParticipantAccountIdsRepository,
} from '../repositories';
import logger from '../utils/logger';
import cashPoolUtils from '../utils/cashPool';
import { ParticipantAccountIdType } from '../types';

const createCashPool = async (cashPool: any, user: any, companyIdMapper: Record<number, Array<string>>) => {
  try {
    const clientId = user.clientId;

    cashPoolUtils.runCashPoolCreateUpdateGuards(cashPool);

    const createdCashPool = await cashPoolRepository.createCashPool({ ...(cashPool as any), clientId });

    const participants = cashPoolUtils.getCashPoolParticipants(cashPool, createdCashPool.id);

    const participantsByCompanyId = _.keyBy(participants, 'companyId');

    const createdParticipants = await cashPoolRepository.createCashPoolParticipants(participants);
    const [createdTopCurrencyAccount] = await topCurrencyAccountRepository.createTopCurrencyAccounts([
      {
        cashPoolId: createdCashPool.id,
        overnightRate: createdCashPool.overnightRate,
        name: `${createdCashPool.name}---TOP_CURRENCY_ACCOUNT_OF_PHYSICAL_CASHPOOL`,
        currency: createdCashPool.currencies,
        interestType: createdCashPool.interestType,
        creditInterestRate: createdCashPool.creditInterestRate,
        debitInterestRate: createdCashPool.debitInterestRate,
      },
    ]);

    const createdAccounts = await Promise.all(
      createdParticipants.map((participant: any) => {
        const participantAccount = participantsByCompanyId[participant.companyId];
        if (participantAccount.creditInterestRate == null) return;

        const account = {
          cashPoolParticipantId: participant.id,
          topCurrencyAccountId: createdTopCurrencyAccount.id,
          creditInterestRate: participantAccount.creditInterestRate,
          debitInterestRate: participantAccount.debitInterestRate,
          currency: createdCashPool.currencies,
        };

        return cashPoolParticipantAccountRepository.createCashPoolParticipantAccount(account);
      }),
    );

    //! From this point on is the difference compared to regular cash pool create in the controller
    const accountsWithCompanyId = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
      whereAccount: { id: createdAccounts.filter(Boolean).map(({ id }) => id) },
    });

    const participantAccountIdsToCreate: Array<ParticipantAccountIdType> = [];
    for (const account of accountsWithCompanyId) {
      const externalIds = companyIdMapper[account.participant.company.id];
      if (!Array.isArray(externalIds)) throw new Error('Account identification is not an array');

      for (const externalId of externalIds) {
        participantAccountIdsToCreate.push({ cashPoolAccountId: account.id, externalId });
      }
    }

    await cashPoolParticipantAccountIdsRepository.bulkCreate(participantAccountIdsToCreate);
  } catch (error) {
    logger.error({ message: 'createCashPool function failed', error: error as Error });
    throw error;
  }
};

export default createCashPool;
