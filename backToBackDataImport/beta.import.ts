import fs from 'fs/promises';
import * as XLSX from 'xlsx';

import { betaRepository } from '../repositories';
import { BetaRegionType } from '../types';

const getHeaderRowIndex = (jsonSheet: Array<Array<string | number>>): number | undefined => {
  for (let i = 0, length = jsonSheet.length; i < length; i++) {
    if (String(jsonSheet[i]).startsWith('Industry')) {
      return i + 1;
    }
  }
};

const getNumberOrNull = (value: number | string): number | null => {
  return typeof value === 'number' ? value : null;
};

const getRegionFromFilename = (filename: string): BetaRegionType => {
  if (filename.toLowerCase().includes('japan')) return 'japan';
  if (filename.toLowerCase().includes('emerg')) return 'emerging';
  if (filename.toLowerCase().includes('europe')) return 'europe';
  if (filename.toLowerCase().includes('global')) return 'global';
  if (filename.toLowerCase().includes('betas')) return 'us';

  throw new Error('Invalid filename.');
};

const betaImport = async () => {
  try {
    const dirPath = './backToBackDataImport/betas';
    const files = await fs.readdir(dirPath);

    for (const filename of files) {
      const file = await fs.readFile(`${dirPath}/${filename}`);
      const region = getRegionFromFilename(filename);

      const year = Number(`20${filename.split('.')[0].slice(-2)}`) + 1;
      const arbitraryMonth = 3;
      if (!year) throw new Error('Invalid year.');

      const workbook = XLSX.read(file, { cellDates: true, type: 'array' });
      const numberOfSheets = Object.keys(workbook.Sheets).length;
      /**
       * Different years have different sheets. If there are three sheets we are interested
       * in the second sheet and if there are one or two sheets we are interested in the first sheet.
       */
      const sheetIndex = numberOfSheets === 3 ? 1 : 0;

      const jsonSheet: Array<Array<string | number>> = XLSX.utils.sheet_to_json(
        workbook.Sheets[workbook.SheetNames[sheetIndex]],
        {
          header: 1,
          blankrows: false,
        },
      );

      /** Used to remove header rows and to just have the data cells in `sheetData` */
      const headerRowIndex = getHeaderRowIndex(jsonSheet);
      if (!headerRowIndex) throw new Error('Header row index not found.');

      const sheetData = jsonSheet.slice(headerRowIndex);

      const betasToCreate = sheetData.map((row) => ({
        industry: String(row[0]),
        region,
        numberOfFirms: getNumberOrNull(row[1]),
        beta: getNumberOrNull(row[2]),
        deRatio: getNumberOrNull(row[3]),
        effectiveTaxRate: getNumberOrNull(row[4]),
        unleveredBeta: getNumberOrNull(row[5]),
        cashFirmValue: getNumberOrNull(row[6]),
        unleveredBetaCorrectedForCash: getNumberOrNull(row[7]),
        hiloRisk: getNumberOrNull(row[8]),
        standardDeviationOfEquity: getNumberOrNull(row[9]),
        standardDeviationInOperatingIncomeLast10Years: getNumberOrNull(row[10]),
        date: new Date(year, arbitraryMonth),
      }));

      await betaRepository.bulkCreate(betasToCreate);
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
    throw err;
  }
};

export default betaImport;
