import { equityRiskPremiumUtils } from '../utils/equityRiskPremiumUtils';

const { regionsToErpRegions } = equityRiskPremiumUtils;

const date2024 = new Date(2024, 3);
const regional2024 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 13.76 / 100, date: date2024 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 6.28 / 100, date: date2024 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 4.6 / 100, date: date2024 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 18.75 / 100, date: date2024 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 10.36 / 100, date: date2024 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 9.66 / 100, date: date2024 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.76 / 100, date: date2024 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 4.6 / 100, date: date2024 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 5.89 / 100, date: date2024 },
];

const date2023 = new Date(2023, 3);
const regional2023 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 15.58 / 100, date: date2023 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 7.87 / 100, date: date2023 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.94 / 100, date: date2023 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 17.13 / 100, date: date2023 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 12.51 / 100, date: date2023 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 13.73 / 100, date: date2023 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 8.45 / 100, date: date2023 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.94 / 100, date: date2023 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 7.45 / 100, date: date2023 },
];

const date2022 = new Date(2022, 3);
const regional2022 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 9.49 / 100, date: date2022 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 5.28 / 100, date: date2022 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 4.24 / 100, date: date2022 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 11.07 / 100, date: date2022 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 8.03 / 100, date: date2022 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 6.35 / 100, date: date2022 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 5.84 / 100, date: date2022 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 4.24 / 100, date: date2022 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 5.07 / 100, date: date2022 },
];

const date2021 = new Date(2021, 3);
const regional2021 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 9.64 / 100, date: date2021 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 5.75 / 100, date: date2021 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 4.72 / 100, date: date2021 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 10.03 / 100, date: date2021 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 8.71 / 100, date: date2021 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 6.8 / 100, date: date2021 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.25 / 100, date: date2021 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 4.72 / 100, date: date2021 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 5.56 / 100, date: date2021 },
];

const date2020 = new Date(2020, 3);
const regional2020 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 9.89 / 100, date: date2020 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 6.21 / 100, date: date2020 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.2 / 100, date: date2020 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 10.62 / 100, date: date2020 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 8.48 / 100, date: date2020 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 7.34 / 100, date: date2020 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.77 / 100, date: date2020 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.2 / 100, date: date2020 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 6.01 / 100, date: date2020 },
];

const date2019 = new Date(2019, 3);
const regional2019 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 12.63 / 100, date: date2019 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 7.43 / 100, date: date2019 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.96 / 100, date: date2019 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 13.61 / 100, date: date2019 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 10.61 / 100, date: date2019 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 9.24 / 100, date: date2019 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 7.96 / 100, date: date2019 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.96 / 100, date: date2019 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 7.11 / 100, date: date2019 },
];

const date2018 = new Date(2018, 3);
const regional2018 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 10.63 / 100, date: date2018 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 6.27 / 100, date: date2018 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.08 / 100, date: date2018 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 11.39 / 100, date: date2018 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 8.63 / 100, date: date2018 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 7.77 / 100, date: date2018 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.69 / 100, date: date2018 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.08 / 100, date: date2018 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 6.01 / 100, date: date2018 },
];

const date2017 = new Date(2017, 3);
const regional2017 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 12.0 / 100, date: date2017 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 7.12 / 100, date: date2017 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.7 / 100, date: date2017 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 13.92 / 100, date: date2017 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 10.21 / 100, date: date2017 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 9.09 / 100, date: date2017 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 7.5 / 100, date: date2017 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.69 / 100, date: date2017 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 6.81 / 100, date: date2017 },
];

const date2016 = new Date(2016, 3);
const regional2016 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 12.99 / 100, date: date2016 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 7.82 / 100, date: date2016 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 6.26 / 100, date: date2016 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 15.31 / 100, date: date2016 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 11.27 / 100, date: date2016 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 10.17 / 100, date: date2016 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 7.56 / 100, date: date2016 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 6.25 / 100, date: date2016 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 7.49 / 100, date: date2016 },
];

const date2015 = new Date(2015, 3);
const regional2015 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 11.73 / 100, date: date2015 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 7.26 / 100, date: date2015 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.75 / 100, date: date2015 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 14.27 / 100, date: date2015 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 9.95 / 100, date: date2015 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 9.08 / 100, date: date2015 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.85 / 100, date: date2015 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.75 / 100, date: date2015 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 6.88 / 100, date: date2015 },
];

const date2014 = new Date(2014, 3);
const regional2014 = [
  { region: regionsToErpRegions['Africa'], equityRiskPremium: 10.04 / 100, date: date2014 },
  { region: regionsToErpRegions['Asia'], equityRiskPremium: 6.51 / 100, date: date2014 },
  { region: regionsToErpRegions['Australia & New Zealand'], equityRiskPremium: 5.0 / 100, date: date2014 },
  { region: regionsToErpRegions['Caribbean'], equityRiskPremium: 12.65 / 100, date: date2014 },
  { region: regionsToErpRegions['Central and South America'], equityRiskPremium: 8.62 / 100, date: date2014 },
  { region: regionsToErpRegions['Eastern Europe & Russia'], equityRiskPremium: 7.96 / 100, date: date2014 },
  { region: regionsToErpRegions['Middle East'], equityRiskPremium: 6.14 / 100, date: date2014 },
  { region: regionsToErpRegions['North America'], equityRiskPremium: 5.0 / 100, date: date2014 },
  { region: regionsToErpRegions['Western Europe'], equityRiskPremium: 6.29 / 100, date: date2014 },
];

export default [
  ...regional2024,
  ...regional2023,
  ...regional2022,
  ...regional2021,
  ...regional2020,
  ...regional2019,
  ...regional2018,
  ...regional2017,
  ...regional2016,
  ...regional2015,
  ...regional2014,
];
