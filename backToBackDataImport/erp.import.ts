import fs from 'fs/promises';
import * as XLSX from 'xlsx';

import { equityRiskPremiumRepository } from '../repositories';
import { moodyToCombinedRateMapper } from '../utils/providerDataUtils';
import { equityRiskPremiumUtils } from '../utils/equityRiskPremiumUtils';
import { getCountries } from '../singletons';
import { EquityRiskPremiumsType } from '../types';

const getHeaderRowIndex = (jsonSheet: Array<Array<string | number>>): number | undefined => {
  const comma = ',';
  for (let i = 0, length = jsonSheet.length; i < length; i++) {
    if (String(jsonSheet[i]).startsWith(`Country${comma}`)) {
      return i + 1;
    }
  }
};

const getLastRowIndex = (jsonSheet: Array<Array<string | number>>): number | undefined => {
  for (let i = 0, length = jsonSheet.length; i < length; i++) {
    if (String(jsonSheet[i]).startsWith(`Zambia`)) {
      return i + 1;
    }
  }
};

const getNumberOrNull = (value: number | string): number | null => {
  return typeof value === 'number' ? value : null;
};

const equityRiskPremiumImport = async () => {
  try {
    const { countriesByName } = await getCountries();
    const dirPath = './backToBackDataImport/erps';
    const files = await fs.readdir(dirPath);

    for (const filename of files) {
      const file = await fs.readFile(`${dirPath}/${filename}`);

      const year = Number(`20${filename.split('.')[0].slice(-2)}`) + 1;
      const arbitraryMonth = 3;
      if (!year) throw new Error('Invalid year.');

      const workbook = XLSX.read(file, { cellDates: true, type: 'array' });
      const sheetIndex = 2;
      const jsonSheet: Array<Array<string | number>> = XLSX.utils.sheet_to_json(
        workbook.Sheets[workbook.SheetNames[sheetIndex]],
        { header: 1, blankrows: false },
      );

      /** Used to remove header rows and to just have the data cells in `sheetData` */
      const headerRowIndex = getHeaderRowIndex(jsonSheet);
      if (!headerRowIndex) throw new Error('Header row index not found.');

      const lastRowIndex = getLastRowIndex(jsonSheet);
      if (!lastRowIndex) throw new Error('Last row index not found.');

      const sheetData = jsonSheet.slice(headerRowIndex, lastRowIndex);

      const countriesNotAvailable: Array<string> = [];
      const equityRiskPremiums: Array<EquityRiskPremiumsType | null> = sheetData
        .map((row) => {
          const countryName = equityRiskPremiumUtils.countryMapper[String(row[0])] || String(row[0]);
          const country = countriesByName[countryName];
          if (!country) {
            countriesNotAvailable.push(countryName);
            return null;
          }

          const moodyRating = moodyToCombinedRateMapper[String(row[2]) as keyof typeof moodyToCombinedRateMapper];
          if (!moodyRating) {
            throw new Error(`Moody rating does not map correctly ${row[2]}`);
          }

          return {
            countryId: country.id,
            moodyRating,
            ratingBasedDefaultSpread: Number(row[3]),
            totalEquityRiskPremium: Number(row[4]),
            countryRiskPremium: Number(row[5]),
            sovereignCds: getNumberOrNull(row[6]),
            totalEquityRiskPremium2: getNumberOrNull(row[7]),
            countryRiskPremium3: getNumberOrNull(row[8]),
            date: new Date(year, arbitraryMonth),
          };
        })
        .filter(Boolean);

      await equityRiskPremiumRepository.bulkCreate(equityRiskPremiums);
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
    throw err;
  }
};

export default equityRiskPremiumImport;
