import equityRiskPremiumImport from './erp.import';
import betaImport from './beta.import';
import regionalErpData from './regionalErp';
import models from '../models';

const { sequelize } = models;

const insertB2BData = async () => {
  await sequelize.transaction(async () => {
    const queryInterface = sequelize.getQueryInterface();

    await queryInterface.sequelize.query('TRUNCATE TABLE "Betas"');
    await queryInterface.sequelize.query('TRUNCATE TABLE "RegionalEquityRiskPremiums"');
    await queryInterface.sequelize.query('TRUNCATE TABLE "EquityRiskPremiums"');

    await queryInterface.bulkInsert('RegionalEquityRiskPremiums', regionalErpData);
    await betaImport();
    await equityRiskPremiumImport();
  });
};

export default insertB2BData;
