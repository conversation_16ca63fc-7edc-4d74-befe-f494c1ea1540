import { Request, Response, NextFunction } from 'express';

import { featureNames } from '../enums';
import { featureRepository } from '../repositories';
import { ForbiddenError, InternalServerError } from '../utils/ErrorHandler';

const {
  CASH_POOL,
  CREDIT_RATING,
  GUARANTEE,
  LOAN,
  PAYMENT,
  CUFT_DATA,
  BACK_TO_BACK_LOAN,
  PHYSICAL_CASH_POOL,
  NOTIONAL_CASH_POOL,
  NORDIC_CASH_POOL,
} = featureNames;

const features = (featureName: featureNames) => async (req: Request, _: Response, next: NextFunction) => {
  const clientId = req.user!.clientId!;

  if (
    ![
      CASH_POOL,
      CREDIT_RATING,
      GUARANTEE,
      LOAN,
      PAYMENT,
      CUFT_DATA,
      BACK_TO_BACK_LOAN,
      PHYSICAL_CASH_POOL,
      NOTIONAL_CASH_POOL,
      NORDIC_CASH_POOL,
    ].includes(featureName)
  ) {
    throw new InternalServerError('Wrong feature name provided');
  }

  const { isEnabled } = await featureRepository.getClientFeatureByName({ clientId, featureName });

  if (!isEnabled) {
    return next(new ForbiddenError());
  }

  next();
};

export default features;
