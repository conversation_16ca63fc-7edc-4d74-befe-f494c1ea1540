import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import { BadRequestError } from '../utils/ErrorHandler';

const upload = multer({ limits: { fileSize: 10 * 1024 * 1024 } }).single('file');

function multerMiddleware(req: Request, res: Response, next: NextFunction) {
  upload(req, res, function (err) {
    if (err instanceof multer.MulterError) {
      next(new BadRequestError('File too large.'));
    } else if (err) {
      next(err);
    }

    next();
  });
}

export default multerMiddleware;
