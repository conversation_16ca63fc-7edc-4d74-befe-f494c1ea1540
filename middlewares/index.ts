import authMethodMiddleware from './authMethodMiddleware';
import compression from './compression';
import cors from './cors';
import features from './features';
import xssMiddleware from './xssMiddleware';
import handleError from './handleError';
import helmet from './helmet';
import multerMiddleware from './multerMiddleware';
import permissions from './permissions';
import rateLimiter from './rateLimiter';
import sentry from './sentry';

export {
  authMethodMiddleware,
  multerMiddleware,
  handleError,
  rateLimiter,
  permissions,
  features,
  xssMiddleware,
  compression,
  helmet,
  cors,
  sentry,
};
