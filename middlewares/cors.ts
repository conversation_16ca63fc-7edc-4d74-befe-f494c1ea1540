import cors from 'cors';

const includeMarsSubdomain = () => {
  const url = new URL(process.env.WEB_APP_URL!);

  const newHostname = `mars.${url.hostname}`;
  url.hostname = newHostname;

  return url.origin;
};

const allowedOrigins = [process.env.WEB_APP_URL!, process.env.WEB_LANDING_URL!];

export default cors({
  origin: [...allowedOrigins, includeMarsSubdomain()],
  credentials: true,
  exposedHeaders: ['Content-Disposition', 'Content-Type'],
  maxAge: 86400,
});
