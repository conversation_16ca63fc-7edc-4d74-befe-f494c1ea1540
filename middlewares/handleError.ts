import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

const isServerError = (statusCode: number) => statusCode >= 500;

const getErrorDetails = (statusCode: number, details: { message: string; payload: any; name: string }) => {
  if (isServerError(statusCode) && process.env.NODE_ENV === 'production') return {};
  return details;
};

interface HandleError {
  message: string;
  name: string;
  statusCode?: number;
  payload?: any;
}

/**
 * Logs errors (console or Sentry) if it's a server error (logger.error)
 * Error details are omitted from the response if it's a server error in production
 */
function handleError(err: HandleError, req: Request, res: Response, next: NextFunction) {
  const { statusCode = 500, message, payload = [], name } = err;

  if (isServerError(statusCode)) {
    logger.error({ message, error: err, payload });
  }

  res.status(statusCode).json({
    status: 'error',
    statusCode,
    ...getErrorDetails(statusCode, { message, payload, name }),
  });

  next();
}

export default handleError;
