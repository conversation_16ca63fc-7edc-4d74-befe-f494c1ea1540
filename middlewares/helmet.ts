import helmet from 'helmet';

import { csp } from '../config/config';

export default helmet({
  contentSecurityPolicy: {
    directives: {
      ...helmet.contentSecurityPolicy.getDefaultDirectives(),
      'connect-src': ["'self'", csp.connectSrcHosts!],
      'img-src': ["'self'", csp.imgSrcHosts!, 'data:'],
      'worker-src': ["'self'", 'blob:'],
      'script-src-elem': ["'self'", csp.scriptSrcElemHosts!],
    },
  },
});
