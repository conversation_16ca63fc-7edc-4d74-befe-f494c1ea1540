import { Express, Request, Response, NextFunction } from 'express';

import * as Sentry from '@sentry/node';
import * as Tracing from '@sentry/tracing';

const isEnabled = process.env.SENTRY_ENABLED === '1';
const environment = process.env.NODE_ENV;
const dsn = process.env.SENTRY_DSN;

const sentryInit = (app: Express, { appName }: { appName: string }) => {
  if (!isEnabled) return;

  Sentry.init({
    dsn,
    environment,
    integrations: [new Sentry.Integrations.Http({ tracing: true }), new Tracing.Integrations.Express({ app })],
    // 0.0 = 0% chance of a given trace being sent (send no traces) 1.0 = 100% (send all traces)
    // Adjust or turn off in production to avoid spending quota that we need for errors.
    // Use tracesSampler for finer control.
    tracesSampleRate: 0.0,
  });

  Sentry.setTag('app', appName);

  app.use(Sentry.Handlers.requestHandler());
  app.use(Sentry.Handlers.tracingHandler());
};

const sentrySetUser = (req: Request, res: Response, next: NextFunction) => {
  if (req.user) {
    const { username, role } = req.user;
    Sentry.configureScope((scope) => scope.setUser({ username, role }));
  }
  next();
};

const sentryErrorHandler = () => Sentry.Handlers.errorHandler();

export default { sentryInit, sentrySetUser, sentryErrorHandler };
