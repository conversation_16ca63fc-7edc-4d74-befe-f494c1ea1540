import { Request, Response, NextFunction } from 'express';
import _ from 'lodash';
import xss from 'xss';

import { XSSError } from '../utils/ErrorHandler';
import logger from '../utils/logger';

type SingleKeyObject = { [key: string]: string | number | null };
type FlattenedReqObjectType = Array<SingleKeyObject>;

function getAllValues(obj: Record<string, any>): FlattenedReqObjectType {
  const result: FlattenedReqObjectType = [];

  function recurse(obj: Record<string, any>) {
    for (const key in obj) {
      if (typeof obj[key] === 'object') recurse(obj[key]);
      else result.push({ [key]: obj[key] });
    }
  }

  recurse(obj);

  return result;
}

const ignoredPaths = ['/api/user', '/api/user/reset-password'];

const xssMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (_.isEmpty(req.body)) return next();

  // Temporary just to see if it logs anything. Delete in a few months if Sen<PERSON> does not report any errors.
  if (typeof req.body !== 'object') {
    logger.error({ message: `req.body is not an object on ${req.originalUrl}`, error: req.body });
    return next();
  }

  if (ignoredPaths.includes(req.path)) return next();

  const bodyValues: FlattenedReqObjectType = getAllValues(req.body);
  for (const field of bodyValues) {
    const [, value] = Object.entries(field)[0];
    if (typeof value !== 'string') continue;

    const strippedString = xss(value, { whiteList: {}, stripIgnoreTag: true, stripIgnoreTagBody: ['script'] });
    if (strippedString !== value) {
      logger.error({ message: 'XSS attempted', error: new XSSError(), payload: { value, strippedString } });
    }
  }

  next();
};

export default xssMiddleware;
