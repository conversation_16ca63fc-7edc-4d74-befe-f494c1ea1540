import { Request, Response, NextFunction } from 'express';

import { ForbiddenError, InternalServerError } from '../utils/ErrorHandler';
import { RoleType } from '../types';

const permissions = (roles: Array<RoleType>) => (req: Request, _: Response, next: NextFunction) => {
  if (!Array.isArray(roles)) {
    throw new InternalServerError('Pass roles as an array');
  }

  if (!roles.length) {
    throw new InternalServerError("Permissions array can't be empty");
  }

  if (!req.user?.role) {
    throw new ForbiddenError();
  }

  if (!roles.includes(req.user.role)) {
    throw new ForbiddenError();
  }

  next();
};

export default permissions;
