import { RateLimiterPostgres } from 'rate-limiter-flexible';
import { Request, Response, NextFunction } from 'express';

import models from '../models';
import logger from '../utils/logger';

const points = process.env.NODE_ENV === 'test' ? 2000 : 20;

const opts = {
  storeClient: models.sequelize,
  points,
  duration: 1,
  tableCreated: true,
  keyPrefix: '"RateLimiter"',
};

const rateLimiter = new RateLimiterPostgres(opts);

function rateLimiterMiddleware(req: Request, res: Response, next: NextFunction) {
  rateLimiter
    .consume(String(req.ip))
    .then(() => next())
    .catch((err) => {
      logger.error({ message: 'Too Many Requests', error: new Error(err), payload: req });
      res.status(429).send('Too Many Requests');
    });
}

export default rateLimiterMiddleware;
