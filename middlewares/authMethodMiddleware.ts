import { Request, Response, NextFunction } from 'express';
import { verify } from 'jsonwebtoken';
import { differenceInMinutes } from 'date-fns';

import { tokenRepository } from '../repositories';
import { ForbiddenError, UnauthorizedError } from '../utils/ErrorHandler';
import { UserDataAccessTokenType } from '../types';
import { checkClientFeatureFlags, extractClientFromRequest } from '../utils/clientUtils';
import { Client } from '../enums';

async function authMethodMiddleware(req: Request, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1];
  const requestUrl = req.get('X-Request-Origin');
  const clientFromUrl = extractClientFromRequest(requestUrl);

  if (token == null) {
    return next(new UnauthorizedError());
  }

  try {
    const userData = verify(token, process.env.TOKEN_SECRET!) as UserDataAccessTokenType;
    const tokenData = await tokenRepository.getTokenByUserId(userData.id);

    if (!tokenData) throw new UnauthorizedError('Refresh token not found!');

    // Prevent other user from using client specific urls
    if (clientFromUrl && userData.role !== 'superadmin' && clientFromUrl !== userData.clientName.toLocaleLowerCase())
      throw new ForbiddenError('Invalid url');

    if (checkClientFeatureFlags(Client.MARS, userData.clientName)) {
      // Prevent login for Mars users on the standard url
      if (!clientFromUrl) throw new ForbiddenError('Invalid url');
      // Logout after 15 minute of inactivity: Mars requirement
      if (tokenData && differenceInMinutes(new Date(), tokenData.lastActivity) >= 15) {
        res.clearCookie('refresh-token', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'development' ? false : true,
          sameSite: 'strict',
        });
        await tokenRepository.deleteToken({ userId: userData.id });
        throw new UnauthorizedError('Inactivity logout');
      }
    }

    await tokenRepository.updateToken({ userId: userData.id }, { lastActivity: new Date() });

    req.user = userData;
    next();
  } catch (err) {
    if (err instanceof UnauthorizedError || err instanceof ForbiddenError) {
      return next(err);
    }
    return next(new UnauthorizedError());
  }
}

export default authMethodMiddleware;
