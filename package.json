{"name": "accurate", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.4.0", "@sentry/react": "^6.13.3", "@sentry/tracing": "^6.13.3", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@tippy.js/react": "^3.1.1", "@tippyjs/react": "^4.2.0", "axios": "^0.21.0", "chart.js": "^3.9.1", "chartjs-adapter-date-fns": "^2.0.1", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "docxtemplater": "^3.37.12", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.20", "moment": "^2.29.1", "pizzip": "^3.1.3", "prop-types": "^15.7.2", "query-string": "^7.0.1", "react": "^16.13.1", "react-chartjs-2": "^4.3.1", "react-datepicker": "^3.2.2", "react-dom": "^16.13.1", "react-error-boundary": "^3.1.4", "react-helmet": "^6.1.0", "react-inlinesvg": "^2.1.0", "react-jss": "^10.4.0", "react-paginate": "^6.5.0", "react-pdf": "^5.0.0", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-scripts": "5.0.0", "react-select": "^5.7.3", "react-simple-maps": "^2.3.0", "react-table": "^7.5.1", "react-toastify": "^6.1.0", "react-tooltip": "^4.2.11", "theme-ui": "^0.3.1", "xlsx": "^0.17.0"}, "lint-staged": {"*.+(js|jsx)": ["eslint --fix"], "*.+(json|css|md)": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "scripts": {"start": "craco start", "start:docker": "docker-compose -f dev.docker-compose.yml up", "build": "craco build", "test": "craco test", "eject": "craco eject", "lint": "eslint . --ext .js,.jsx", "lint:fix": "npm run lint -- --fix"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^6.4.5", "@types/chart.js": "^2.9.37", "@types/file-saver": "^2.0.5", "@types/lodash": "^4.14.195", "@types/node": "^18.16.19", "@types/react": "^16.14.66", "@types/react-dom": "^16.9.25", "@types/react-helmet": "^6.1.5", "@types/react-redux": "^7.1.16", "@types/react-router-dom": "^5.3.3", "@types/react-select": "^5.0.1", "@types/react-table": "^7.7.12", "@types/theme-ui": "^0.6.0", "eslint": "^7.11.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": "^4.3.8", "lint-staged": "^10.5.3", "prettier": "^2.8.8", "react-error-overlay": "6.0.9", "snyk": "^1.1187.0", "typescript": "^4.7.4"}}