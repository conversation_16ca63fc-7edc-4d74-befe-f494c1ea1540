# Repayments

`compoundedInterest` and `additionalInterest` exist only for fixed balloon payments
`interestPayment` exists only for fixed bullet payments

`totalRepayment = principalAmount + totalInterest`
`totalInterest` is saved in the Loan/Guarantee model
`principalAmount` is `amount` in the Loan/Guarantee model

Guarantees are always `fixed` and `Bullet`

Bullet payments are paid every period so every `Payments.paymentDueDate`
Ballon payments are only paid once, at maturity. That's why their `Payments.paymentDueDate` is NULL for every row except for the last one.
That way periods for Bullet are tracked with `Payments.paymentDueDate` and for Balloon are tracked with `BalloonPayments.compoundingPeriodEndDate`

Repayment algorithm input and output

```javascript
// Input for both fixed bullet and fixed balloon payments
{
	issueDate: "2021-10-31",
	maturityDate: "2021-12-31",
	principalAmount: 300000,
	paymentFrequency: "Monthly",
	finalInterestRate: 3
}

// Fixed bullet payments output
{
	"interestRatePerInterestRepaymentFrequency": 0.0025,
	"paymentAtMaturity": 300750.0,
	"paymentSchedule": [
		{
			"interestPayment": 750.0,
			"paymentDueDate": "Tue, 30 Nov 2021 00:00:00 GMT"
		},
		{
			"interestPayment": 750.0,
			"paymentDueDate": "Fri, 31 Dec 2021 00:00:00 GMT"
		}
	],
	"totalInterest": 1500.0,
	"totalNumberOfPayments": 2.0,
	"totalRepayment": 301500.0
}

// Fixed balloon payments output
{
  "compoundingSchedule": [
    {
      additionalInterest: 750.0,
      compoundedInterest: 750.0,
      compoundingPeriodEndDate: "Tue, 30 Nov 2021 00:00:00 GMT",
    },
    {
      additionalInterest: 751.8749999999418,
      compoundedInterest: 1501.8749999999418,
      compoundingPeriodEndDate: "Fri, 31 Dec 2021 00:00:00 GMT",
    }
  ],
  interestRatePerInterestRepaymentFrequency: 0.0025,
  totalInterest: 1501.8749999999418,
  totalNumberOfCompoundingPeriods: 2.0,
  totalRepayment: 301501.87499999994
}
```

`getFixedBulletPayments` function returns data below. Data is saved in `Payments` and `BulletPayments` table

```javascript
[
  {
    loanId: 216,
    interestRatePerInterestRepaymentFrequency: 0.0025,
    interestPayment: 750,
    paymentDueDate: 'Tue, 30 Nov 2021 00:00:00 GMT',
    isPaid: false,
  },
  {
    loanId: 216,
    interestRatePerInterestRepaymentFrequency: 0.0025,
    interestPayment: 750,
    paymentDueDate: 'Fri, 31 Dec 2021 00:00:00 GMT',
    isPaid: false,
  },
];
```

`getFixedBalloonPayments` function returns data below. Data is saved in `Payments` and `BalloonPayments` table

```javascript
[
  {
    loanId: 176,
    interestRatePerInterestRepaymentFrequency: 0.0025,
    compoundedInterest: 750,
    additionalInterest: 750,
    compoundingPeriodEndDate: 'Tue, 30 Nov 2021 00:00:00 GMT',
    paymentDueDate: null,
    isPaid: false,
  },
  {
    loanId: 176,
    interestRatePerInterestRepaymentFrequency: 0.0025,
    compoundedInterest: 1501.8749999999418,
    additionalInterest: 751.8749999999418,
    compoundingPeriodEndDate: 'Fri, 31 Dec 2021 00:00:00 GMT',
    paymentDueDate: 'Fri, 31 Dec 2021 00:00:00 GMT',
    isPaid: false,
  },
];
```
