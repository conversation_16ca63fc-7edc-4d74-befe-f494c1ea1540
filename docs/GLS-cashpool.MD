# GLS Cash Pools

## Introduction

GLS wanted a special type of cash pool setup. It's a multilayer cash pool that consists of three Physical cash pools in layer 1 and one cash pool in layer 2. The layer 2 cash pool has three participants which are effectively the three cash pools in layer 1.

The three participants in the layer 2 cash pool are the leaders of each of the layer 1 cash pools. They are the same companies from the Group Information section.

Balances of each participant in a single cash pool are summed up and act as a balance of the layer 2 participant. It's much easier to understand with an example.

The following snippet is the data for each of the cash pools in layer 1.
First column is the date, second is the participant name and third is the balance.
GLS should send us data on a daily basis so there will always be only one date in each cash pool, but the algorithm works for multiple dates as well in case some other client will send their data on a weekly/monthly basis.
A1, B1 and C1 are names of the cash pools in layer 1 and also the names of participants in the layer 2 cash pool.
This transformation of data from three cash pool to a single cash pool data is done by the solver.

```javascript
{
  A1: [
    ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 100],
    ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', 300],
    ['2022-01-01T23:00:00.000Z', 'Apricot Tree Inc', 200],
    ['2022-01-01T23:00:00.000Z', 'Avocado Tree AS', -100],
  ],
  B1: [
    ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', 400],
    ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', -200],
    ['2021-12-31T23:00:00.000Z', 'Orange Tree', -200],
    ['2022-01-01T23:00:00.000Z', 'Bamboo Tree Ltd.', 300],
    ['2022-01-01T23:00:00.000Z', 'Banana Tree AS', -300],
    ['2022-01-01T23:00:00.000Z', 'Orange Tree', 100],
  ],
  C1: [
    ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', 200],
    ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', -400],
    ['2021-12-31T23:00:00.000Z', 'Green Tree Group, Inc.', 300],
    ['2021-12-31T23:00:00.000Z', 'Gum Tree Ltda.', -200],
    ['2022-01-01T23:00:00.000Z', 'Cheese Tree B.V.', 300],
    ['2022-01-01T23:00:00.000Z', 'Cherry Tree Ltd.', 200],
    ['2022-01-01T23:00:00.000Z', 'Green Tree Group, Inc.', 100],
    ['2022-01-01T23:00:00.000Z', 'Gum Tree Ltda.', -100],
  ],
};
```

From the input above the output is

```javascript
[
  ['2021-12-31T23:00:00.000Z', 'A1', 400],
  ['2021-12-31T23:00:00.000Z', 'B1', 0],
  ['2021-12-31T23:00:00.000Z', 'C1', -100],
  ['2022-01-01T23:00:00.000Z', 'A1', 100],
  ['2022-01-01T23:00:00.000Z', 'B1', 100],
  ['2022-01-01T23:00:00.000Z', 'C1', 500],
];
```

The first row is calculated by summing the balances of all participants in the A1 cash pool based on the date, that is 100 + 300 = 400. Then for the same date the balances of the the B1 cash pool summed, that is 400 + (-200) + (-200) = 0. And finally the balances of the C1 cash pool summed, that is 200 + (-400) + 300 + (-200) = -100. The same logic applies for the second date.

## Running the calculations

These are the rough steps to run the calculations (this would happen daily):

1. Pull the data from their SFTP server (this still needs to be agreed upon, maybe they will push the data to us)
2. Transform the data to a format we can use
3. Transformed data of three layer 1 cash pools is used to calculate the layer 2 cash pool data (using the solver)
4. Run the cash pool model for each of the four cash pools (using the solver)
