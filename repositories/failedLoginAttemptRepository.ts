import { Op } from 'sequelize';
import { startOfMinute } from 'date-fns';

import models from '../models';
import { FailedLoginAttemptType } from '../types';

const { FailedLoginAttempt } = models;

function getFailedLoginAttemptInLastMinute(email: string): Promise<FailedLoginAttemptType> {
  const now = new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

  return FailedLoginAttempt.findOne({
    where: {
      email,
      createdAt: { [Op.between]: [oneMinuteAgo, now] },
    },
  });
}

function getFailedLoginAttempts(): Promise<FailedLoginAttemptType[]> {
  return FailedLoginAttempt.findAll({});
}

async function createFailedLoginAttempt(failedLoginAttempt: FailedLoginAttemptType): Promise<void> {
  const createdAtWithoutSeconds = startOfMinute(new Date());
  await FailedLoginAttempt.create({ ...failedLoginAttempt, createdAt: createdAtWithoutSeconds }).catch(() => ({}));
}

export = { getFailedLoginAttemptInLastMinute, getFailedLoginAttempts, createFailedLoginAttempt };
