import { Express } from 'express';

import models from '../models';
import { CuftDataFileType } from '../types';

const { CuftDataFile } = models;

const getCuftDataFiles = ({ where }: any): Promise<CuftDataFileType[]> => CuftDataFile.findAll({ where });

const getCuftDataFile = ({ where }: any): Promise<CuftDataFileType | null> => CuftDataFile.findOne({ where });

const createCuftFile = async (file: Express.Multer.File): Promise<CuftDataFileType> => {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/) || [];

  return CuftDataFile.create({ name: originalNameMatch[1], extension: originalNameMatch[2], mimeType: mimetype }, {});
};

const deleteCuftFile = (id: number): Promise<number> => CuftDataFile.destroy({ where: { id } });

export default { getCuftDataFiles, getCuftDataFile, createCuftFile, deleteCuftFile };
