import { literal } from 'sequelize';

import models from '../models';
import { RegionalEquityRiskPremiumsType } from '../types';

const { RegionalEquityRiskPremiums } = models;

function findOne({ region, date }: { region: string; date: Date }): Promise<RegionalEquityRiskPremiumsType | null> {
  return RegionalEquityRiskPremiums.findOne({
    attributes: ['id', 'region', 'date', 'equityRiskPremium'],
    where: { region, date: literal(`EXTRACT(YEAR FROM "RegionalEquityRiskPremiums".date) = ${date.getFullYear()}`) },
  });
}

export default { findOne };
