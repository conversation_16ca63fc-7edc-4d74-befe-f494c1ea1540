import models from '../models';
import {
  CreateNotificationType,
  NotificationType,
  CreateReceivingUserNotificationType,
  ReceivingUserNotificationType,
  ReceivingUserNotificationWithNotificationType,
} from '../types';

const { Notification, Receiving_Users_Notifications, User } = models;

const model = Notification;

async function createNotification(notification: CreateNotificationType): Promise<NotificationType> {
  const { dataValues } = await model.create(notification);
  return dataValues;
}

function createUserNotifications(
  notifications: CreateReceivingUserNotificationType[],
): Promise<ReceivingUserNotificationType[]> {
  return Receiving_Users_Notifications.bulkCreate(notifications);
}

function getNotificationsByReceivingUserId(
  where: any,
  offset: number,
  limit: number,
): Promise<ReceivingUserNotificationWithNotificationType[]> {
  return Receiving_Users_Notifications.findAll({
    where,
    offset,
    limit,
    order: [['createdAt', 'DESC']],
    include: {
      model: Notification,
      as: 'notification',
      include: {
        model: User,
        as: 'createdByUser',
        attributes: ['id', 'email', 'fullName'],
      },
    },
    attributes: {
      exclude: ['receivingUserId', 'notificationId'],
    },
  });
}

function getNotificationCount(where: any): Promise<number> {
  return Receiving_Users_Notifications.count({ where });
}

function deleteNotificationByReceivingUserId(notificationId: number, receivingUserId: number): Promise<number> {
  return Receiving_Users_Notifications.destroy({ where: { notificationId, receivingUserId } });
}

function updateNotificationIsHandled(
  notificationId: number,
  receivingUserId: number,
  isHandled: boolean,
  returning = true,
): Promise<[number, ReceivingUserNotificationType[]]> {
  return Receiving_Users_Notifications.update(isHandled, {
    where: { notificationId, receivingUserId },
    returning,
  });
}

export = {
  createNotification,
  createUserNotifications,
  getNotificationsByReceivingUserId,
  getNotificationCount,
  deleteNotificationByReceivingUserId,
  updateNotificationIsHandled,
};
