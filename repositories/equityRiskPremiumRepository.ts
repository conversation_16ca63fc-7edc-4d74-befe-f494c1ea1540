import { literal } from 'sequelize';

import models from '../models';
import { EquityRiskPremiumsType, EquityRiskPremiumsRepoType } from '../types';

const { EquityRiskPremiums } = models;

function bulkCreate(equityRiskPremiums: Array<EquityRiskPremiumsType | null>): Promise<Array<EquityRiskPremiumsType>> {
  return EquityRiskPremiums.bulkCreate(equityRiskPremiums);
}

function findOne({
  countryId,
  date,
}: {
  countryId: number;
  date: Date;
}): Promise<{ dataValues: EquityRiskPremiumsRepoType } | null> {
  return EquityRiskPremiums.findOne({
    attributes: ['id', 'countryId', 'date', [literal('"totalEquityRiskPremium"'), 'equityRiskPremium']],
    where: { countryId, date: literal(`EXTRACT(YEAR FROM "EquityRiskPremiums".date) = ${date.getFullYear()}`) },
  });
}

export default { bulkCreate, findOne };
