import { Express } from 'express';
import { col, fn } from 'sequelize';

import models from '../models';
import { TemplateFileLabelsEnum } from '../enums/templateFiles';
import { TemplateFileType, TemplateFileTypeType } from '../types';

const { TemplateFile, Company } = models;

type CreateFileQueryParams = {
  label: TemplateFileLabelsEnum;
  type: TemplateFileTypeType;
  country?: string;
  companyId?: number;
};

type UpdateableTemplateMetaData = {
  name: string;
  label: string;
};

const getTemplateFiles = ({ where }: any): Promise<TemplateFileType[]> =>
  TemplateFile.findAll({ where, include: { model: Company, as: 'company' } });

const getTemplateFile = ({ where }: any): Promise<TemplateFileType | null> =>
  TemplateFile.findOne({ where, include: { model: Company, as: 'company' } });

const getTemplateFilesLabels = ({ where }: any): Promise<TemplateFileType[]> =>
  TemplateFile.findAll({ where, attributes: [[fn('DISTINCT', col('label')), 'label']] });

const createTemplateFile = async (
  file: Express.Multer.File,
  clientId: number,
  { label, type, country, companyId }: CreateFileQueryParams,
): Promise<TemplateFileType> => {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/) || [];

  return TemplateFile.create(
    {
      name: originalNameMatch[1],
      extension: originalNameMatch[2],
      mimeType: mimetype,
      clientId,
      label,
      type,
      country,
      companyId,
    },
    {},
  );
};

const deleteTemplateFile = (id: number, clientId: number): Promise<number> =>
  TemplateFile.destroy({ where: { id, clientId } });

const updateTemplateFileMetadata = (
  id: number,
  clientId: number,
  data: UpdateableTemplateMetaData,
): Promise<[number]> => TemplateFile.update(data, { where: { id, clientId } });

const createTemplateFile__Migrations = (
  clientId: number,
  name: string,
  label: TemplateFileLabelsEnum,
  type: TemplateFileTypeType,
) => {
  return TemplateFile.create({
    clientId,
    name,
    label,
    type,
    extension: '.docx',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  });
};

export default {
  getTemplateFiles,
  getTemplateFile,
  getTemplateFilesLabels,
  createTemplateFile,
  deleteTemplateFile,
  updateTemplateFileMetadata,
  createTemplateFile__Migrations,
};
