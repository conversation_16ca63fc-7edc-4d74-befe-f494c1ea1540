const { sequelize, ParticipantAccountTrails } = require('../models');

function createCashPoolParticipantTrails(trails) {
  return ParticipantAccountTrails.bulkCreate(trails);
}

async function getTotalAccountBenefit({ cashPoolId }) {
  const result = await sequelize.query(
    `SELECT SUM("netInterestBenefit") AS "netInterestBenefit"
      FROM "ParticipantAccountTrails"
      INNER JOIN "CashPoolBatches" AS cpb ON "cashPoolBatchId" = cpb.id
      WHERE cpb."cashPoolId" = :cashPoolId`,
    { replacements: { cashPoolId } },
  );
  return result[0][0]['netInterestBenefit'];
}

function getCashPoolParticipantTrailsByBatchId({ batchId, limit }) {
  return ParticipantAccountTrails.findAll({ where: { cashPoolBatchId: batchId }, limit });
}

function getCashPoolParticipantTrail({ where, order }) {
  return ParticipantAccountTrails.findOne({ where, order });
}

module.exports = {
  createCashPoolParticipantTrails,
  getTotalAccountBenefit,
  getCashPoolParticipantTrailsByBatchId,
  getCashPoolParticipantTrail,
};
