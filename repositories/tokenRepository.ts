import models from '../models';

const { Token, sequelize } = models;

async function getToken(accessToken: string) {
  return await Token.findOne({ where: { accessToken } });
}

async function getTokenByUserId(userId: number) {
  return await Token.findOne({ where: { userId } });
}

async function createToken({ accessToken, userId }: { accessToken: string; userId: number }) {
  return await Token.create({ accessToken, userId });
}

async function deleteToken(where: Record<string, string | number>) {
  return await Token.destroy({ where });
}

async function updateToken(where: Record<string, string | number>, data: any) {
  return await Token.update({ ...data }, { where });
}

export default { getToken, getTokenByUserId, createToken, deleteToken, updateToken };
