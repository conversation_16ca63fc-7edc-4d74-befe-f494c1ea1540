const model = require('../models').LoanFile;

function getAll() {
  return model.findAll({ attributes: ['id', 'file'] });
}

function getLoanFile(id) {
  return model.findByPk(id, {
    attributes: ['name', 'extension', 'mimeType'],
  });
}

// returning not working
async function createLoanFile(file, loanId, { label, newName }) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/);
  const loanFile = await model.create(
    {
      extension: originalNameMatch[2],
      name: newName ? newName : originalNameMatch[1],
      mimeType: mimetype,
      loanId,
      label,
    },
    {},
  );

  return loanFile;
}

function deleteLoanFile(id, loanId) {
  return model.destroy({
    where: { id, loanId },
  });
}

function updateLoanFile(id, data) {
  return model.update(data, {
    where: {
      id,
    },
  });
}

module.exports = {
  getAll,
  getLoanFile,
  createLoanFile,
  deleteLoanFile,
  updateLoanFile,
};
