const {
  sequelize,
  Company,
  Cash_Pool_Participants,
  CashPoolParticipantAccounts,
  ParticipantAccountTrails,
  TopCurrencyAccounts,
  ParticipantAccountIds,
} = require('../models');

async function getCashPoolParticipantAccountTrailDates({ cashPoolId }) {
  /**
   * Cannot exclude primary keys from joined tables so DISTINCT cannot work.
   * Not sure there is a solution for this in Sequelize (https://github.com/sequelize/sequelize/issues/3417).
   * Leaving this here to show how I got the SQL statement from which primary keys were removed so only DISTINCT was left.
   */
  // return ParticipantAccountTrails.findAll({
  //   attributes: [[fn('DISTINCT', col('ParticipantAccountTrails.date')), 'date']],
  //   include: [
  //     {
  //       model: CashPoolParticipantAccounts,
  //       as: 'account',
  //       attributes: ['id', 'cashPoolParticipantId'],
  //       required: true,
  //       include: {
  //         model: Cash_Pool_Participants,
  //         as: 'participant',
  //         required: true,
  //         attributes: ['id', 'companyId'],
  //         where: whereParticipant,
  //       },
  //     },
  //   ],
  // });
  const result = await sequelize.query(
    `SELECT DISTINCT("ParticipantAccountTrails"."date") AS "date"
      FROM "ParticipantAccountTrails" AS "ParticipantAccountTrails"
      INNER JOIN "CashPoolParticipantAccounts" AS "account"
      ON "ParticipantAccountTrails"."participantAccountId" = "account"."id"
      INNER JOIN "Cash_Pool_Participants" AS "account->participant"
      ON "account"."cashPoolParticipantId" = "account->participant"."id" AND "account->participant"."cashPoolId" = :cashPoolId;`,
    {
      replacements: { cashPoolId },
    },
  );
  return result[0];
}

/**
 * Used for exporting entire cash pool data
 */
function getCashPoolTrails({ cashPoolId, whereCompany, whereTrails, whereTopCurrencyAccount }) {
  return ParticipantAccountTrails.findAll({
    where: whereTrails,
    include: {
      model: CashPoolParticipantAccounts,
      paranoid: false,
      as: 'account',
      required: true,
      include: [
        {
          model: Cash_Pool_Participants,
          as: 'participant',
          attributes: ['companyId', 'isLeader', 'uniqueId'],
          where: { cashPoolId },
          required: true,
          include: {
            model: Company,
            as: 'company',
            where: whereCompany,
            attributes: ['id', 'name'],
          },
        },
        {
          model: TopCurrencyAccounts,
          as: 'topCurrencyAccount',
          where: whereTopCurrencyAccount,
          required: true,
        },
        {
          model: ParticipantAccountIds,
          as: 'externalIds',
          attributes: ['externalId'],
        },
      ],
    },
    order: [['date', 'ASC']],
  });
}

module.exports = { getCashPoolParticipantAccountTrailDates, getCashPoolTrails };
