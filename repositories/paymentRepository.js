const { fn, col, cast } = require('sequelize');

const { Payment, BulletPayment, BalloonPayment } = require('../models');
const { InternalServerError } = require('../utils/ErrorHandler');
const { getReportModel } = require('../utils/payments');
const model = Payment;

function getPayments({ where, order, limit, offset, loanFilter, guaranteeFilter }) {
  if (!loanFilter && !guaranteeFilter) throw new InternalServerError('Pass one filter at least as empty object');

  const reportModel = getReportModel(loanFilter, guaranteeFilter);

  return model.findAll({
    where,
    order,
    limit,
    offset,
    include: [
      reportModel,
      {
        model: BulletPayment,
        as: 'bulletPayment',
      },
      {
        model: BalloonPayment,
        as: 'balloonPayment',
      },
    ],
  });
}

function getPayment({ where, type }) {
  const include =
    type === 'Bullet' ? { model: BulletPayment, as: 'bulletPayment' } : { model: BalloonPayment, as: 'balloonPayment' };

  return model.findOne({ where, include });
}

function getTotalPaymentsCount({ where, loanFilter, guaranteeFilter, bulletPaymentFilter }) {
  if (!loanFilter && !guaranteeFilter) throw new InternalServerError('Pass one filter at least as empty object');

  const reportModel = getReportModel(loanFilter, guaranteeFilter);

  return model.count({
    where,
    include: [
      reportModel,
      {
        model: BulletPayment,
        as: 'bulletPayment',
        where: bulletPaymentFilter,
      },
    ],
  });
}

function getPaymentsCount(where, countColumn) {
  return model.findAll({
    where,
    attributes: {
      include: [
        [fn('SUM', cast(col('Payment.isPaid'), 'INTEGER')), 'paidPayments'],
        [fn('COUNT', col(countColumn)), 'totalNumberOfPayments'],
      ],
      exclude: [
        'id',
        'interestRatePerInterestRepaymentFrequency',
        'interestPayment',
        'paymentDueDate',
        'isPaid',
        'createdAt',
        'updatedAt',
        'compoundedInterest',
        'additionalInterest',
        'paymentAmount',
        'paymentNumber',
        'isPrincipalPayment',
        'referenceRate',
        'interestCalculationDate',
      ],
    },
    group: ['Payment.loanId', 'Payment.guaranteeId'],
  });
}

async function createPayments(payments, type) {
  const result = await model.bulkCreate(payments);

  const paymentIds = result.map(({ dataValues }) => dataValues.id);

  if (type === 'Bullet') {
    const bulletPayments = paymentIds.map((paymentId, index) => ({
      paymentId,
      interestPayment: payments[index].interestPayment,
    }));
    return BulletPayment.bulkCreate(bulletPayments);
  }

  const balloonPayments = paymentIds.map((paymentId, index) => ({
    paymentId,
    compoundedInterest: payments[index].compoundedInterest,
    additionalInterest: payments[index].additionalInterest,
    compoundingPeriodEndDate: payments[index].compoundingPeriodEndDate,
  }));
  return BalloonPayment.bulkCreate(balloonPayments);
}

async function updatePayment(id, attributesToUpdate, returning = false) {
  const [rowsAffected, rows] = await model.update(attributesToUpdate, {
    where: { id },
    returning,
  });

  if (returning) {
    return [rowsAffected, rows[0]?.dataValues];
  }

  return [rowsAffected];
}

async function updateSpecificPayment({ type, paymentId, attributesToUpdate, returning = false }) {
  const model = type === 'Bullet' ? BulletPayment : BalloonPayment;

  const [rowsAffected, rows] = await model.update(attributesToUpdate, {
    where: { paymentId },
    returning,
  });

  if (returning) {
    return [rowsAffected, rows[0]?.dataValues];
  }

  return [rowsAffected];
}

async function getSpecificPaymentByPaymentId({ type, paymentId }) {
  const model = type === 'Bullet' ? BulletPayment : BalloonPayment;

  return model.findOne({ where: { paymentId } });
}

function deletePayments(where) {
  return model.destroy({ where });
}

module.exports = {
  getPayments,
  getPayment,
  getTotalPaymentsCount,
  getPaymentsCount,
  createPayments,
  updatePayment,
  updateSpecificPayment,
  getSpecificPaymentByPaymentId,
  deletePayments,
};
