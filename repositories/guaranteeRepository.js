const { checkClientFeatureFlags, marsUtils } = require('../utils/clientUtils');
const { Guarantee, GuaranteeFile } = require('../models');
const { Client } = require('../enums');

const model = Guarantee;

function getGuarantees({ where, order, limit, paranoid }) {
  return model.findAll({
    where,
    order,
    limit,
    paranoid,
  });
}

function getGuarantee(id, clientId, exclude = ['clientId', 'calculationLog']) {
  return model.findOne({
    where: {
      id,
      clientId,
    },
    paranoid: false,
    include: {
      model: GuaranteeFile,
      as: 'files',
      where: {
        guaranteeId: id,
      },
      attributes: ['id', 'name', 'label', 'status', 'createdAt', 'isGenerated'],
      required: false,
    },
    attributes: {
      exclude,
    },
  });
}

function getGuaranteesCount(clientId) {
  return model.count({ where: { clientId } });
}

async function guaranteeExists(id, clientId) {
  return !!(await model.findOne({ where: { id, clientId }, attributes: ['id'] }));
}

async function createGuarantee(guarantee, user) {
  const { clientId, username, clientName } = user;

  guarantee.clientId = clientId;
  guarantee.createdBy = username;
  guarantee.originalIssueDate = guarantee.issueDate;

  if (checkClientFeatureFlags(Client.MARS, clientName)) {
    const externalId = await marsUtils.incrementExternalId(user);

    guarantee.externalId = externalId;
  }

  const res = await model.create(guarantee);
  delete res.dataValues.clientId;
  delete res.dataValues.calculationLog;

  return res;
}

async function updateGuarantee(id, guarantee, user, returning = true) {
  guarantee.updatedBy = user?.username;

  const response = await model.update(guarantee, {
    where: {
      id,
    },
    returning,
  });

  if (returning) {
    const returnValue = response[1][0];
    delete returnValue.dataValues.clientId;
    delete returnValue.dataValues.calculationLog;

    return returnValue;
  }
}

function deleteGuarantee(id, clientId, force) {
  return model.destroy({
    where: {
      id,
      clientId,
    },
    force,
  });
}

function restoreGuarantee(id, clientId) {
  return model.restore({
    where: {
      id,
      clientId,
    },
  });
}

const updateOrCreateFiles = (guarantee, guaranteeFiles) => {
  return guarantee.files.map((guaranteeFile) => {
    const guaranteeFileId = guaranteeFiles.find(
      (file) => file.label === guaranteeFile.label && guaranteeFile.isGenerated,
    )?.id;
    if (!guaranteeFileId) {
      return GuaranteeFile.create(guaranteeFile);
    }

    return GuaranteeFile.update(guaranteeFile, {
      where: { id: guaranteeFileId },
      returning: true,
    });
  });
};

async function updateGuaranteeAndCreateFiles(id, guarantee, user) {
  await updateGuarantee(id, guarantee, user, false);

  const guaranteeFiles = await GuaranteeFile.findAll({
    where: {
      guaranteeId: id,
      label: ['TP Report', 'Agreement'],
      isGenerated: true,
    },
    attributes: ['id', 'label'],
  });
  const [response] = await Promise.all(updateOrCreateFiles(guarantee, guaranteeFiles));
  return response;
}

module.exports = {
  getGuarantees,
  getGuarantee,
  getGuaranteesCount,
  guaranteeExists,
  createGuarantee,
  updateGuarantee,
  updateGuaranteeAndCreateFiles,
  deleteGuarantee,
  restoreGuarantee,
};
