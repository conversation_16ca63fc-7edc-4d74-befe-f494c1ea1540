const { fn, col } = require('sequelize');

const { sequelize, Company } = require('../models');

const model = Company;

async function getCompanies(where) {
  return model.findAll({ where, order: [[fn('lower', col('name')), 'ASC']] });
}

async function getCompanyById(id, clientId) {
  return (await model.findOne({ where: { id: id ?? null, clientId } }))?.dataValues;
}

async function getCompany(where, attributes) {
  return await model.findOne({ where, attributes });
}

async function companyExists(id, clientId) {
  return !!(await model.findOne({ where: { id, clientId }, attributes: ['id'] }));
}

function updateCompany(where, values, createAuditTrail, returning, username) {
  if (username) values.updatedBy = username;

  return model.update(values, { where, individualHooks: createAuditTrail, returning });
}

function deleteCompany(id, clientId) {
  return sequelize.transaction(() => {
    return model.destroy({ where: { id, clientId }, individualHooks: true });
  });
}

function createCompany(company, user) {
  const { username, clientId } = user;

  company.clientId = clientId;
  company.createdBy = username;

  return model.create(company);
}

function createCompanies(companies, user) {
  const { username, clientId } = user;

  companies.forEach((company) => {
    company.clientId = clientId;
    company.createdBy = username;
  });

  return model.bulkCreate(companies);
}

module.exports = {
  getCompanies,
  getCompanyById,
  getCompany,
  companyExists,
  updateCompany,
  deleteCompany,
  createCompany,
  createCompanies,
};
