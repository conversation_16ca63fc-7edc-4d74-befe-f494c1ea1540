const { CreditRating, CreditRatingAttribute, CreditRatingFile, sequelize } = require('../models');
const model = CreditRating;

function getCreditRatings({ where, order, limit, paranoid }) {
  return model.findAll({
    where,
    order,
    limit,
    paranoid,
  });
}

function getCompanyCreditRatings(companyId) {
  return model.findAll({
    where: {
      'company.id': companyId,
    },
    attributes: ['id', 'creditRating', 'closingDate', 'probabilityOfDefault', 'createdAt'],
    order: [['createdAt', 'DESC']],
  });
}

function getCreditRating(id, clientId) {
  return model.findOne({
    where: {
      id,
      clientId,
    },
    paranoid: false,
    include: [
      {
        model: CreditRatingAttribute,
        as: 'attributes',
        attributes: {
          exclude: ['id', 'creditRatingId', 'createdAt', 'updatedAt'],
        },
      },
      {
        model: CreditRatingFile,
        as: 'files',
        attributes: ['id', 'name', 'label', 'status', 'isGenerated', 'createdAt'],
        required: false,
      },
    ],
    order: [[{ model: CreditRatingFile, as: 'files' }, 'createdAt', 'ASC']],
  });
}

function getCreditRatingsCount(clientId) {
  return model.count({ where: { clientId } });
}

async function creditRatingExists(id, clientId) {
  return !!(await model.findOne({ where: { id, clientId }, attributes: ['id'] }));
}

function deleteCreditRating(id, clientId, force) {
  return model.destroy({
    where: {
      id,
      clientId,
    },
    force,
  });
}

function restoreCreditRating(id, clientId) {
  return model.restore({
    where: {
      id,
      clientId,
    },
  });
}

async function createCreditRating(creditRating, user) {
  const { clientId, username } = user;

  creditRating.clientId = clientId;
  creditRating.createdBy = username;

  const res = await model.create(creditRating, {
    include: [
      {
        association: model.associations.attributes,
        as: 'attributes',
      },
      {
        association: model.associations.files,
        as: 'files',
      },
    ],
  });
  delete res.dataValues.clientId;
  delete res.dataValues?.attributes?.dataValues?.pdf;
  return res;
}

async function updateCreditRating(id, creditRating, user) {
  creditRating.updatedBy = user?.username;

  const { attributes, ...rest } = creditRating;
  const creditRatingResponse = (
    await model.update(rest, {
      where: {
        id,
      },
      returning: true,
    })
  )[1][0];
  delete creditRatingResponse.dataValues.clientId;

  return {
    ...creditRatingResponse?.dataValues,
    attributes,
  };
}

async function updateCreditRatingAndFile(id, creditRating, user) {
  creditRating.updatedBy = user?.username;
  return sequelize.transaction(async () => {
    const { attributes, ...rest } = creditRating;
    const creditRatingResponse = (
      await model.update(rest, {
        where: {
          id,
        },
        returning: true,
      })
    )[1][0];
    delete creditRatingResponse.dataValues.clientId;

    const attributesResponse = (
      await CreditRatingAttribute.update(attributes, {
        where: {
          creditRatingId: id,
        },
        returning: true,
      })
    )[1][0];

    const generatedCreditRatingFile = await CreditRatingFile.findOne({
      where: {
        creditRatingId: id,
        isGenerated: true,
      },
      attributes: ['id'],
    });

    await CreditRatingFile.update(creditRating.files[0], {
      where: {
        id: generatedCreditRatingFile.dataValues.id,
      },
    });

    return {
      ...creditRatingResponse.dataValues,
      attributes: {
        ...attributesResponse.dataValues,
      },
      fileId: generatedCreditRatingFile.dataValues.id,
    };
  });
}

module.exports = {
  getCreditRatings,
  getCompanyCreditRatings,
  getCreditRating,
  getCreditRatingsCount,
  creditRatingExists,
  deleteCreditRating,
  restoreCreditRating,
  createCreditRating,
  updateCreditRating,
  updateCreditRatingAndFile,
};
