const { fn, col, cast } = require('sequelize');

import { WHTPayment } from '../types';
import models from '../models';

const { WHTPayment, Loan } = models;

function getWHTPayments({
  where,
  order,
  limit,
  offset,
  loanFilter,
}: {
  where: any;
  order: any;
  limit: number;
  offset: number;
  loanFilter: any;
}): Promise<WHTPayment[]> {
  return WHTPayment.findAll({
    where,
    order,
    limit,
    offset,
    include: {
      model: Loan,
      as: 'loan',
      attributes: { exclude: ['createdAt', 'calculationLog'] },
      where: loanFilter,
    },
  });
}

function getWHTPayment(where: any): Promise<WHTPayment> {
  return WHTPayment.findOne({ where });
}

function createWHTPayments(payments: any): Promise<any[]> {
  return WHTPayment.bulkCreate(payments);
}

function getTotalPaymentsCount({ where, loanFilter }: { where: any; loanFilter: any }): Promise<number> {
  return WHTPayment.count({
    where,
    include: {
      model: Loan,
      as: 'loan',
      attributes: { exclude: ['createdAt'] },
      where: loanFilter,
    },
  });
}

function getPaymentsCount(where: any, countColumn: any): Promise<any> {
  return WHTPayment.findAll({
    where,
    attributes: {
      include: [
        [fn('SUM', cast(col('WHTPayment.isPaid'), 'INTEGER')), 'paidPayments'],
        [fn('COUNT', col(countColumn)), 'totalNumberOfPayments'],
      ],
      exclude: ['id', 'paymentId', 'isPaid', 'createdAt', 'updatedAt', 'paymentAmount', 'paymentNumber'],
    },
    group: ['WHTPayment.loanId'],
  });
}

async function updatePayment(id: number, attributesToUpdate: any, returning = false): Promise<(number | WHTPayment)[]> {
  const [rowsAffected, rows] = await WHTPayment.update(attributesToUpdate, {
    where: { id },
    returning,
  });

  if (returning) {
    return [rowsAffected, rows[0]?.dataValues];
  }

  return [rowsAffected];
}

function deletePayments(where: any): Promise<number> {
  return WHTPayment.destroy({ where });
}

export = {
  getWHTPayments,
  getWHTPayment,
  createWHTPayments,
  getTotalPaymentsCount,
  getPaymentsCount,
  updatePayment,
  deletePayments,
};
