import models from '../models';

const { CashPoolLeaderBenefit } = models;

function createLeaderBenefit(leaderBenefit: {
  cashPoolId: number;
  cashPoolBatchId: number;
  leaderBenefit: number;
  date: Date;
}) {
  return CashPoolLeaderBenefit.create(leaderBenefit);
}

function getLeaderBenefit(where: any) {
  return CashPoolLeaderBenefit.findAll({ where, order: [['date', 'ASC']] });
}

export { createLeaderBenefit, getLeaderBenefit };
