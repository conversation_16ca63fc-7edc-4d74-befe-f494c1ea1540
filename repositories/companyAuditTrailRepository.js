const model = require('../models').CompanyAuditTrail;

function getCompanyAuditTrailAll(companyId) {
  return model.findAll({
    where: {
      companyId,
    },
    order: [['updatedAt', 'DESC']],
  });
}

function getCompanyAuditTrail(id) {
  return model.findByPk(id);
}

function deleteCompanyAuditTrail(id) {
  return model.destroy({
    where: {
      id,
    },
  });
}

module.exports = {
  getCompanyAuditTrailAll,
  getCompanyAuditTrail,
  deleteCompanyAuditTrail,
};
