import models from '../models';
import { CreditRatingDraftType } from '../types';

const { CreditRatingDraft, CreditRatingAttributesDraft } = models;

const getCreditRatingDrafts = (clientId: number): Promise<CreditRatingDraftType[]> => {
  return CreditRatingDraft.findAll({
    where: { clientId },
    order: [['updatedAt', 'DESC']],
    include: [
      {
        model: CreditRatingAttributesDraft,
        as: 'attributes',
        attributes: {
          exclude: ['id', 'creditRatingDraftId', 'createdAt', 'updatedAt'],
        },
      },
    ],
  });
};

const createCreditRatingDraft = (creditRatingDraft: CreditRatingDraftType): Promise<CreditRatingDraftType> =>
  CreditRatingDraft.create(creditRatingDraft, {
    include: [
      {
        association: CreditRatingDraft.associations.attributes,
        as: 'attributes',
      },
    ],
  });

const deleteCreditRatingDraft = (id: string, clientId: number): Promise<number> =>
  CreditRatingDraft.destroy({ where: { id, clientId } });

export default {
  getCreditRatingDrafts,
  createCreditRatingDraft,
  deleteCreditRatingDraft,
};
