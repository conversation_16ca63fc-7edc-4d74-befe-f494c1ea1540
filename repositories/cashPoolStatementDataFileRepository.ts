import models from '../models';
const { CashPoolStatementDataFile, User } = models;

function getStatementDataFile(id: number) {
  return CashPoolStatementDataFile.findByPk(id, { attributes: ['name', 'extension', 'id'] });
}

async function getStatementDataFiles(cashPoolId: number) {
  return await CashPoolStatementDataFile.findAll({
    where: {
      cashPoolId,
    },
    order: [['createdAt', 'DESC']],
    include: {
      model: User,
      as: 'createdByUser',
      required: true,
      attributes: ['username', 'fullName'],
    },
  });
}

async function getStatementDataFilesCount(cashPoolId: number) {
  return await CashPoolStatementDataFile.count({
    where: {
      cashPoolId,
    },
  });
}

async function createStatementDataFile(
  file: any,
  cashPoolId: number,
  startDate: string,
  endDate: string,
  createdByUserId: number,
) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/);
  const cashPoolFile = await CashPoolStatementDataFile.create(
    {
      extension: originalNameMatch[2],
      name: originalNameMatch[1],
      mimeType: mimetype,
      cashPoolId,
      startDate,
      endDate,
      createdByUserId,
    },
    {},
  );

  return cashPoolFile;
}

async function deleteStatementDataFile(id: number, cashPoolId: number) {
  return await CashPoolStatementDataFile.destroy({ where: { id, cashPoolId } });
}

export = {
  getStatementDataFile,
  getStatementDataFiles,
  getStatementDataFilesCount,
  createStatementDataFile,
  deleteStatementDataFile,
};
