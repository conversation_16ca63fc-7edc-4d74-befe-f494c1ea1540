import { Op } from 'sequelize';
import models from '../models';
import {
  CashPoolStatementBlockType,
  DbCashPoolStatementBlockType,
  StatementDataForTemplateFileType,
  StatementDataForTemplateFileType2,
} from '../types';
const {
  CashPoolStatementData,
  CashPoolParticipantAccounts,
  Cash_Pool_Participants,
  CashPool,
  TopCurrencyAccounts,
  Company,
  sequelize,
} = models;

type StatementDataForTemplateFileFilterType = {
  startDate: Date;
  endDate: Date;
  cashPoolId: number;
};

type GetStatementType = {
  cashPoolId: number;
  clientId: number;
  where?: any;
  whereCompany?: any;
  cutOffDate?: Date;
  offset?: number;
  limit?: number;
  order?: Array<Array<any>>;
};

function bulkCreate(statementData: Array<CashPoolStatementBlockType>): Promise<Array<DbCashPoolStatementBlockType>> {
  return CashPoolStatementData.bulkCreate(statementData);
}

function getTotalCashPoolStatementDataCount({ cashPoolId, clientId, where, whereCompany }: GetStatementType) {
  return CashPoolStatementData.count({
    where,
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      required: true,
      include: [
        {
          model: Cash_Pool_Participants,
          as: 'participant',
          required: true,
          include: [
            {
              model: CashPool,
              as: 'cashPool',
              required: true,
              where: { id: cashPoolId, clientId },
            },
            {
              model: Company,
              as: 'company',
              required: true,
              attributes: ['id', 'name'],
              where: whereCompany,
            },
          ],
        },
      ],
    },
  });
}

function getStatementDataForTemplateFile({
  cashPoolId,
  clientId,
  where,
  whereCompany,
  offset,
  limit,
  order = [['date', 'ASC']],
}: GetStatementType): Promise<Array<StatementDataForTemplateFileType>> {
  return CashPoolStatementData.findAll({
    where,
    attributes: [
      'id',
      'date',
      'statementDate',
      'balanceChange',
      'statementNumber',
      'comment',
      'cashPoolBatchId',
      'createdAt',
    ],
    order,
    offset,
    limit,
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      attributes: ['id'],
      required: true,
      include: [
        { model: TopCurrencyAccounts, as: 'topCurrencyAccount', required: true, attributes: ['id', 'interestType'] },
        {
          model: Cash_Pool_Participants,
          as: 'participant',
          attributes: ['id'],
          required: true,
          include: [
            {
              model: CashPool,
              as: 'cashPool',
              attributes: ['id'],
              required: true,
              where: { id: cashPoolId, clientId },
            },
            {
              model: Company,
              as: 'company',
              required: true,
              attributes: ['id', 'name'],
              where: whereCompany,
            },
          ],
        },
      ],
    },
  });
}

const statementDataQuery = `
FROM "CashPoolStatementData" AS "CashPoolStatementData"
INNER JOIN "CashPoolParticipantAccounts" AS "account" ON "CashPoolStatementData"."cashPoolAccountId" = "account"."id" AND ("account"."deletedAt" IS NULL)
INNER JOIN "TopCurrencyAccounts" AS "account->topCurrencyAccount" ON "account"."topCurrencyAccountId" = "account->topCurrencyAccount"."id"
INNER JOIN "Cash_Pool_Participants" AS "account->participant" ON "account"."cashPoolParticipantId" = "account->participant"."id"
INNER JOIN "CashPools" AS "account->participant->cashPool" ON "account->participant"."cashPoolId" = "account->participant->cashPool"."id" AND "account->participant->cashPool"."id" = :cashPoolId
INNER JOIN "Companies" AS "account->participant->company" ON "account->participant"."companyId" = "account->participant->company"."id"
WHERE "CashPoolStatementData"."date" BETWEEN :startDate AND :endDate and "CashPoolStatementData"."cashPoolBatchId" IS NULL
`;

async function getStatementDataForTemplateFile2({
  startDate,
  endDate,
  cashPoolId,
}: StatementDataForTemplateFileFilterType): Promise<Array<StatementDataForTemplateFileType2>> {
  const result = await sequelize.query(
    `SELECT "CashPoolStatementData"."date", "CashPoolStatementData"."cashPoolAccountId", SUM("CashPoolStatementData"."balanceChange") as "totalBalanceChange"
      ${statementDataQuery}
      GROUP BY "CashPoolStatementData"."date", "cashPoolAccountId"
      ORDER BY "CashPoolStatementData"."date" ASC;
    `,
    { replacements: { startDate, endDate, cashPoolId } },
  );
  return result[0];
}

async function getStatementDataForTemplateIds({
  startDate,
  endDate,
  cashPoolId,
}: StatementDataForTemplateFileFilterType): Promise<Array<{ id: number }>> {
  const result = await sequelize.query(
    `SELECT "CashPoolStatementData"."id"
      ${statementDataQuery}
      ORDER BY "CashPoolStatementData"."date" ASC;
    `,
    { replacements: { startDate, endDate, cashPoolId } },
  );
  return result[0];
}

function addStatementData(data: CashPoolStatementBlockType): Promise<DbCashPoolStatementBlockType> {
  return CashPoolStatementData.create(data);
}

function updateStatementDataById(
  id: number,
  data: Partial<CashPoolStatementBlockType>,
): Promise<DbCashPoolStatementBlockType> {
  return CashPoolStatementData.update(data, { where: { id }, returning: true });
}

function updateCashPoolBatchIdOfStatementData(cashPoolBatchId: number, statementDataIds: Array<number>) {
  return CashPoolStatementData.update({ cashPoolBatchId }, { where: { id: { [Op.in]: statementDataIds } } });
}

function getStatementDataById(id: number, clientId: number): Promise<DbCashPoolStatementBlockType> {
  return CashPoolStatementData.findOne({
    where: { id },
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      attributes: [],
      required: true,
      include: [
        {
          model: Cash_Pool_Participants,
          as: 'participant',
          attributes: [],
          required: true,
          include: [
            {
              model: CashPool,
              as: 'cashPool',
              attributes: [],
              required: true,
              where: { clientId },
            },
          ],
        },
      ],
    },
  });
}

function getStatementData({ where, order }: any) {
  return CashPoolStatementData.findOne({ where, order });
}

function deleteStatementById(id: number): Promise<number> {
  return CashPoolStatementData.destroy({ where: { id } });
}

function deleteStatement(where: Record<string, any>): Promise<number> {
  return CashPoolStatementData.destroy({ where });
}

export = {
  getTotalCashPoolStatementDataCount,
  bulkCreate,
  getStatementDataForTemplateFile,
  getStatementDataForTemplateFile2,
  getStatementDataForTemplateIds,
  updateCashPoolBatchIdOfStatementData,
  updateStatementDataById,
  addStatementData,
  getStatementDataById,
  getStatementData,
  deleteStatementById,
  deleteStatement,
};
