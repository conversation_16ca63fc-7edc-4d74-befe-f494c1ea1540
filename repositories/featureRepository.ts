import models from '../models';
import {
  ClientFeatureType,
  ClientFeatureWithFeatureType,
  CreateClientFeatureType,
  FeatureType,
  ClientFeatureByNameArgsType,
} from '../types';
const { Client_Feature, Feature } = models;

function getAllFeatures(): Promise<FeatureType[]> {
  return Feature.findAll();
}

function getAllClientFeatures({ clientId }: { clientId: number }): Promise<ClientFeatureWithFeatureType[]> {
  return Client_Feature.findAll({
    where: { clientId },
    include: {
      model: Feature,
      as: 'feature',
    },
    order: [['id']],
  });
}

function createClientFeatures(clientFeatures: CreateClientFeatureType[]): Promise<ClientFeatureType> {
  return Client_Feature.bulkCreate(clientFeatures);
}

function getClientFeatureByName({
  clientId,
  featureName,
}: ClientFeatureByNameArgsType): Promise<ClientFeatureWithFeatureType> {
  return Client_Feature.findOne({
    where: { clientId },
    include: {
      model: Feature,
      as: 'feature',
      where: { name: featureName },
    },
  });
}

function updateClientFeatureValues({ values, where }: { values: any; where: any }) {
  return Client_Feature.update(values, { where });
}

export = {
  getAllFeatures,
  getAllClientFeatures,
  createClientFeatures,
  getClientFeatureByName,
  updateClientFeatureValues,
};
