const { TopCurrencyAccountAuditTrail } = require('../models');

function createTopCurrencyAccountAuditTrails(topCurrencyAccountsTrails) {
  return TopCurrencyAccountAuditTrail.bulkCreate(topCurrencyAccountsTrails);
}

function getTopCurrencyAccountAuditTrailAll(cashPoolAuditTrailId) {
  return TopCurrencyAccountAuditTrail.findAll({
    where: {
      cashPoolAuditTrailId,
    },
    order: [['updatedAt', 'DESC']],
  });
}

function getTopCurrencyAccountAuditTrail({ where }) {
  return TopCurrencyAccountAuditTrail.findOne({ where });
}

function deleteTopCurrencyAccountAuditTrail(id) {
  return TopCurrencyAccountAuditTrail.destroy({
    where: {
      id,
    },
  });
}

module.exports = {
  createTopCurrencyAccountAuditTrails,
  getTopCurrencyAccountAuditTrailAll,
  getTopCurrencyAccountAuditTrail,
  deleteTopCurrencyAccountAuditTrail,
};
