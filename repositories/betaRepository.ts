import { literal } from 'sequelize';

import models from '../models';
import { BetaType } from '../types';

const { Betas } = models;

function bulkCreate(betas: Array<BetaType>): Promise<Array<BetaType>> {
  return Betas.bulkCreate(betas);
}

function findOne({
  industry,
  region,
  date,
}: {
  industry: string;
  region: string;
  date: Date;
}): Promise<BetaType | null> {
  return Betas.findOne({
    where: {
      industry,
      region,
      date: literal(`EXTRACT(YEAR FROM "Betas".date) = ${date.getFullYear()}`),
    },
  });
}

export default { bulkCreate, findOne };
