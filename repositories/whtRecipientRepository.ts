import { literal } from 'sequelize';

import models from '../models';
import {
  WHTOriginWithCountry,
  WHTRecipientType,
  CreateWHTRecipientType,
  WHTRecipientWithOriginType,
  DeloitteApiWHTRecipientType,
} from '../types';

const { WHTRecipient, WHTOrigin, Country } = models;

function getAll(): Promise<WHTRecipientType[]> {
  return WHTRecipient.findAll({});
}

function bulkCreate(recipients: CreateWHTRecipientType[]): Promise<WHTRecipientType[]> {
  return WHTRecipient.bulkCreate(recipients);
}

function deloitteApiBulkCreate(recipients: DeloitteApiWHTRecipientType[]): Promise<WHTRecipientType[]> {
  return WHTRecipient.bulkCreate(recipients);
}

function getLatestRecipientWhtData(
  originCountryId: number,
  recipientCountryId: number,
): Promise<WHTRecipientWithOriginType | null> {
  return WHTRecipient.findOne({
    where: { countryId: recipientCountryId },
    attributes: {
      include: [[literal('"recipientCountry"."name"'), 'country']],
    },
    include: [
      {
        model: WHTOrigin,
        as: 'origin',
        attributes: { include: [[literal('"origin->originCountry"."name"'), 'country']] },
        where: { countryId: originCountryId },
        include: { model: Country, as: 'originCountry', attributes: [] },
      },
      { model: Country, as: 'recipientCountry', attributes: [] },
    ],
    order: [[{ model: WHTOrigin, as: 'origin' }, 'date', 'DESC']],
  });
}

function getOriginWhtData(recipientCountryId: number): Promise<WHTOriginWithCountry | null> {
  return WHTOrigin.findOne({
    where: { countryId: recipientCountryId },
    attributes: {
      include: [[literal('"originCountry"."name"'), 'country']],
    },
    include: {
      model: Country,
      as: 'originCountry',
      attributes: [],
    },
    order: [['date', 'DESC']],
  });
}

export = { getAll, bulkCreate, deloitteApiBulkCreate, getLatestRecipientWhtData, getOriginWhtData };
