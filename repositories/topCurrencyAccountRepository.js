const { TopCurrencyAccounts } = require('../models');

const model = TopCurrencyAccounts;

function createTopCurrencyAccount(topCurrencyAccounts) {
  return model.create(topCurrencyAccounts);
}

function createTopCurrencyAccounts(topCurrencyAccounts) {
  return model.bulkCreate(topCurrencyAccounts);
}

function updateTopCurrencyAccount(id, data) {
  return model.update(data, { where: { id }, returning: true });
}

function getTopCurrencyAccount({ id, cashPoolId }) {
  return model.findOne({ where: { id, cashPoolId } });
}

function getCashPoolTopCurrencyAccounts({ cashPoolId }) {
  return model.findAll({
    attributes: ['id', 'name'],
    where: { cashPoolId },
  });
}

function deleteTopCurrencyAccount(where) {
  return model.destroy({ where });
}

module.exports = {
  createTopCurrencyAccount,
  createTopCurrencyAccounts,
  updateTopCurrencyAccount,
  getTopCurrencyAccount,
  getCashPoolTopCurrencyAccounts,
  deleteTopCurrencyAccount,
};
