const { CashPool, CashPoolBatchFile, CashPoolBatch } = require('../models');

function getCashPoolBatchFile({ clientId, cashPoolId, batchId }) {
  return CashPoolBatchFile.findOne({
    include: {
      model: CashPoolBatch,
      as: 'batch',
      where: { id: batchId },
      include: {
        model: CashPool,
        as: 'cashPool',
        where: { id: cashPoolId, clientId },
      },
    },
  });
}

function getAllCashPoolBatchFiles({ clientId, cashPoolId }) {
  return CashPoolBatchFile.findAll({
    include: {
      model: CashPoolBatch,
      as: 'batch',
      include: {
        model: CashPool,
        as: 'cashPool',
        where: { id: cashPoolId, clientId },
      },
    },
  });
}

async function createCashPoolBatchFile(data) {
  const batchFile = await CashPoolBatchFile.create(data);
  /**
   * `cashPoolBatchId` and `id` of `CashPoolBatchFile` should always be the same.
   * They can only go out of sync if the `CashPoolBatchFile` or `CashPoolBatch` is created manually.
   * Refactor is needed to prevent this from happening. For now, we throw an error.
   * `createCashPoolBatch` and `createCashPoolBatchFile` should always be called together and in a transaction.
   * That is currently happening only in the `uploadBatchFile` function.
   */
  if (batchFile.id !== batchFile.cashPoolBatchId) {
    throw new Error('Batch and Batch File are out of sync.');
  }

  return batchFile;
}

async function deleteCashPoolBatchFileById(id) {
  return CashPoolBatchFile.destroy({ where: { id } });
}

module.exports = {
  getCashPoolBatchFile,
  getAllCashPoolBatchFiles,
  createCashPoolBatchFile,
  deleteCashPoolBatchFileById,
};
