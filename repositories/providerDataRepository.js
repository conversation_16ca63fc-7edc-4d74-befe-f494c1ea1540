const { Op } = require('sequelize');

const model = require('../models').ProviderData;

async function getProviderData(where, attributes) {
  return await model.findAll({
    where,
    attributes: ['id', 'rating', ...attributes],
  });
}

// If there is not data for the requestedIssueDate then use the data for the highest lower issueDate that exists in database
async function getProviderDataIssueDate(requestedIssueDate) {
  return await model.max('issueDate', { where: { issueDate: { [Op.lte]: requestedIssueDate } } });
}

async function issueDateExists(issueDate) {
  const count = await model.count({ where: { issueDate } });
  return count !== 0;
}

async function currencyExists(currency) {
  const result = await model.findOne({ where: { currency }, attributes: ['id'] });
  return result !== null;
}

async function createProviderData(data, options) {
  return await model.bulkCreate(data, options);
}

module.exports = {
  getProviderData,
  getProviderDataIssueDate,
  issueDateExists,
  currencyExists,
  createProviderData,
};
