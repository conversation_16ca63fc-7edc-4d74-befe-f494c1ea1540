const model = require('../models').CashPoolFile;

function getCashPoolFile(id) {
  return model.findByPk(id, { attributes: ['name', 'extension', 'mimeType'] });
}

async function createCashPoolFile(file, cashPoolId, { label, newName }) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/);
  const cashPoolFile = await model.create(
    {
      extension: originalNameMatch[2],
      name: newName ? newName : originalNameMatch[1],
      mimeType: mimetype,
      cashPoolId,
      label,
    },
    {},
  );

  return cashPoolFile;
}

function deleteCashPoolFile(id, cashPoolId) {
  return model.destroy({ where: { id, cashPoolId } });
}

function updateCashPoolFile(id, data) {
  return model.update(data, { where: { id } });
}

module.exports = {
  getCashPoolFile,
  createCashPoolFile,
  deleteCashPoolFile,
  updateCashPoolFile,
};
