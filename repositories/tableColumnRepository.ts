import models from '../models';

const { TableColumn } = models;

function getTableColumns(clientId: number) {
  return TableColumn.findOne({ where: { clientId } });
}

function createTableColumns(clientId: number, body: any) {
  return TableColumn.create({ clientId, ...body });
}

function updateTableColumns(clientId: number, body: any) {
  return TableColumn.update(body, { where: { clientId } });
}

export default { getTableColumns, createTableColumns, updateTableColumns };
