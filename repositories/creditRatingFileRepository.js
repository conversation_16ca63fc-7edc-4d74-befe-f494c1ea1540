const model = require('../models').CreditRatingFile;

function getAll() {
  return model.findAll({ attributes: ['id', 'file'] });
}

function getCreditRatingFile(id) {
  return model.findByPk(id, {
    attributes: ['name', 'extension', 'mimeType'],
  });
}

async function createCreditRatingFile(file, creditRatingId, { label, newName, status }) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/);
  const creditRatingFile = await model.create(
    {
      extension: originalNameMatch[2],
      name: newName ? newName : originalNameMatch[1],
      mimeType: mimetype,
      status,
      creditRatingId,
      label,
    },
    {},
  );

  return creditRatingFile;
}

function deleteCreditRatingFile(id) {
  return model.destroy({
    where: {
      id,
    },
  });
}

function updateCreditRatingFile(id, data) {
  return model.update(data, {
    where: {
      id,
    },
  });
}

module.exports = {
  getAll,
  getCreditRatingFile,
  createCreditRatingFile,
  deleteCreditRatingFile,
  updateCreditRatingFile,
};
