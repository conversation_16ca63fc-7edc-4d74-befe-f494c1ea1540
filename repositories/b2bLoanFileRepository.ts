import { Express } from 'express';
import models from '../models';

const { BackToBackLoanFile } = models;

function getAll() {
  return BackToBackLoanFile.findAll({ attributes: ['id', 'file'] });
}

function getLoanFile(id: number) {
  return BackToBackLoanFile.findByPk(id, { attributes: ['name', 'extension', 'mimeType'] });
}

async function createLoanFile(
  file: Express.Multer.File,
  loanId: number,
  { label, newName }: { label: string; newName?: string },
) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/) || [];
  const loanFile = await BackToBackLoanFile.create(
    {
      extension: originalNameMatch[2],
      name: newName ? newName : originalNameMatch[1],
      mimeType: mimetype,
      loanId,
      label,
    },
    {},
  );

  return loanFile;
}

async function simpleCreateFile(agreementMetadata: any) {
  return BackToBackLoanFile.create(agreementMetadata);
}

function deleteLoanFile(id: number, loanId: number) {
  return BackToBackLoanFile.destroy({ where: { id, loanId } });
}

function updateLoanFile(id: number, data: any) {
  return BackToBackLoanFile.update(data, { where: { id } });
}

export { getAll, getLoanFile, createLoanFile, deleteLoanFile, updateLoanFile, simpleCreateFile };
