import models from '../models';
import { DbParticipantAccountExcludedIdType, ParticipantAccountExcludedIdType } from '../types';

const { ParticipantAccountExcludedIds, CashPoolParticipantAccounts, Cash_Pool_Participants, CashPool } = models;

function bulkCreate(
  participantAccountExcludedIds: Array<ParticipantAccountExcludedIdType>,
): Promise<Array<DbParticipantAccountExcludedIdType>> {
  return ParticipantAccountExcludedIds.bulkCreate(participantAccountExcludedIds);
}

function createIfNotExists({ cashPoolAccountId, excludedId }: ParticipantAccountExcludedIdType) {
  return ParticipantAccountExcludedIds.findOrCreate({
    where: { cashPoolAccountId, excludedId },
    defaults: { cashPoolAccountId, excludedId },
  });
}

function getOne({ where }: any) {
  return ParticipantAccountExcludedIds.findOne({
    where,
    include: { model: CashPoolParticipantAccounts, as: 'account' },
  });
}

function getExcludedIdsByCashPoolId(cashPoolId: number): Promise<Array<{ excludedId: string }>> {
  return ParticipantAccountExcludedIds.findAll({
    attributes: ['excludedId'],
    raw: true,
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      attributes: [],
      required: true,
      include: {
        model: Cash_Pool_Participants,
        as: 'participant',
        attributes: [],
        required: true,
        include: {
          model: CashPool,
          as: 'cashPool',
          where: { id: cashPoolId },
          attributes: [],
          required: true,
        },
      },
    },
  });
}

function deleteByExcludedId(excludedId: string | Array<string>): Promise<number> {
  return ParticipantAccountExcludedIds.destroy({ where: { excludedId } });
}

function deleteByCashPoolAccountId(cashPoolAccountId: string | Array<string>): Promise<number> {
  return ParticipantAccountExcludedIds.destroy({ where: { cashPoolAccountId } });
}

async function getLeaderParticipantExcludedId(cashPoolId: number): Promise<string> {
  const { excludedId } = await ParticipantAccountExcludedIds.findOne({
    attributes: ['excludedId'],
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      required: true,
      attributes: [],
      include: {
        model: Cash_Pool_Participants,
        as: 'participant',
        where: { cashPoolId, isLeader: true },
        required: true,
        attributes: [],
      },
    },
  });

  return excludedId;
}

export {
  bulkCreate,
  createIfNotExists,
  deleteByCashPoolAccountId,
  deleteByExcludedId,
  getExcludedIdsByCashPoolId,
  getLeaderParticipantExcludedId,
  getOne,
};
