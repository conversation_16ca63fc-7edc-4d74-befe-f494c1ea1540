import { literal } from 'sequelize';

import models from '../models';
import {
  WHTOriginType,
  WHTOriginWithCountry,
  CreateWHTOriginType,
  DeloitteApiWHTOriginType,
  DeloitteWHTOriginType,
} from '../types';

const { WHTOrigin, Country } = models;

function getAll(): Promise<WHTOriginType[]> {
  return WHTOrigin.findAll({});
}

function getCount(): Promise<number> {
  return WHTOrigin.count();
}

function create(origin: CreateWHTOriginType): Promise<WHTOriginType> {
  return WHTOrigin.create(origin);
}

function deloitteApiCreate(origin: DeloitteApiWHTOriginType): Promise<DeloitteWHTOriginType> {
  return WHTOrigin.create(origin);
}

function getLatestOriginWhtData(originCountryId: number): Promise<WHTOriginWithCountry | null> {
  return WHTOrigin.findOne({
    where: { countryId: originCountryId },
    attributes: { include: [[literal('"originCountry"."name"'), 'country']] },
    include: {
      model: Country,
      as: 'originCountry',
      attributes: [],
    },
    order: [['date', 'DESC']],
  });
}

function destroy(where: any): Promise<number> {
  return WHTOrigin.destroy({ where });
}

export = { getLatestOriginWhtData, getAll, getCount, create, deloitteApiCreate, destroy };
