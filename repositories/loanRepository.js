const { Loan, LoanFile } = require('../models');
const { checkClientFeatureFlags, marsUtils } = require('../utils/clientUtils');
const { Client } = require('../enums');

const model = Loan;

function getLoans({ where, order, limit, paranoid }) {
  return model.findAll({ where, order, limit, paranoid });
}

async function getLoan(id, clientId, exclude = ['clientId', 'calculationLog']) {
  return model.findOne({
    where: { id, clientId },
    paranoid: false,
    include: {
      model: LoanFile,
      as: 'files',
      where: { loanId: id },
      attributes: ['id', 'name', 'label', 'status', 'createdAt', 'isGenerated'],
      required: false,
    },
    attributes: { exclude },
  });
}

function getLoansCount(clientId) {
  return model.count({ where: { clientId } });
}

async function loanExists(id, clientId) {
  return !!(await model.findOne({ where: { id, clientId }, attributes: ['id'] }));
}

async function createLoan(loan, user) {
  const { clientId, username, clientName } = user;

  loan.clientId = clientId;
  loan.createdBy = username;
  loan.originalIssueDate = loan.issueDate;

  if (checkClientFeatureFlags(Client.MARS, clientName)) {
    const externalId = await marsUtils.incrementExternalId(user);

    loan.externalId = externalId;
  }

  const res = await model.create(loan);
  delete res.dataValues.clientId;
  delete res.dataValues.calculationLog;

  return res;
}

async function updateLoan(id, loan, user, returning = true) {
  loan.updatedBy = user?.username;

  const response = await model.update(loan, { where: { id }, returning });

  if (returning) {
    const returnValue = response[1][0];
    delete returnValue.dataValues.clientId;
    delete returnValue.dataValues.calculationLog;

    return returnValue;
  }
}

async function updateLoanWithoutUpdatedBy({ id, attributesToUpdate, returning = false }) {
  const response = await model.update(attributesToUpdate, { where: { id }, returning });

  if (returning) {
    const returnValue = response[1][0];
    delete returnValue.dataValues.clientId;
    delete returnValue.dataValues.calculationLog;

    return returnValue;
  }
}

const updateOrCreateFiles = (loan, loanFiles) => {
  return loan.files.map((loanFile) => {
    const loanFileId = loanFiles.find((file) => file.label === loanFile.label && loanFile.isGenerated)?.id;
    if (!loanFileId) {
      return LoanFile.create(loanFile);
    }

    return LoanFile.update(loanFile, {
      where: { id: loanFileId },
      returning: true,
    });
  });
};

/* used with putLoanIsPortfolio which creates two files or updates them
  also used in generating an agreement in which case on file is created */
async function updateLoanAndCreateFiles(id, loan, user) {
  await updateLoan(id, loan, user, false);

  const loanFiles = await LoanFile.findAll({
    where: {
      loanId: id,
      label: ['TP Report', 'Agreement'],
      isGenerated: true,
    },
    attributes: ['id', 'label'],
  });
  const [response] = await Promise.all(updateOrCreateFiles(loan, loanFiles));
  return response;
}

function deleteLoan(id, clientId, force) {
  return model.destroy({
    where: { id, clientId },
    force,
  });
}

function restoreLoan(id, clientId) {
  return model.restore({
    where: { id, clientId },
  });
}

module.exports = {
  getLoans,
  getLoan,
  getLoansCount,
  loanExists,
  createLoan,
  updateLoan,
  updateLoanWithoutUpdatedBy,
  updateLoanAndCreateFiles,
  deleteLoan,
  restoreLoan,
};
