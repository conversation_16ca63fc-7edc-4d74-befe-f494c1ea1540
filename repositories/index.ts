import * as b2bLoanFileRepository from './b2bLoanFileRepository';
import * as b2bLoanLegRepository from './b2bLoanLegRepository';
import b2bLoanRepository from './b2bLoanRepository';
import betaRepository from './betaRepository';
import cashPoolAuditTrailRepository from './cashPoolAuditTrailRepository';
import cashPoolBatchFileRepository from './cashPoolBatchFileRepository';
import cashPoolBatchPaymentsRepository from './cashPoolBatchPaymentsRepository';
import cashPoolBatchRepository from './cashPoolBatchRepository';
import cashPoolFileRepository from './cashPoolFileRepository';
import * as cashPoolLeaderBenefitRepository from './cashPoolLeaderBenefitRepository';
import cashPoolOperatingExpensesRepository from './cashPoolOperatingExpensesRepository';
import * as cashPoolParticipantAccountExcludedIdsRepository from './cashPoolParticipantAccountExcludedIdsRepository';
import * as cashPoolParticipantAccountIdsRepository from './cashPoolParticipantAccountIdsRepository';
import cashPoolParticipantAccountRepository from './cashPoolParticipantAccountRepository';
import cashPoolParticipantsRepository from './cashPoolParticipantsRepository';
import cashPoolParticipantTrailRepository from './cashPoolParticipantTrailRepository';
import cashPoolRepository from './cashPoolRepository';
import cashPoolStatementDataFileRepository from './cashPoolStatementDataFileRepository';
import cashPoolStatementDataRepository from './cashPoolStatementDataRepository';
import clientRepository from './clientRepository';
import clientTemplateRepository from './clientTemplateRepository';
import companyAuditTrailRepository from './companyAuditTrailRepository';
import companyRepository from './companyRepository';
import countryRepository from './countryRepository';
import creditRatingDraftRepository from './creditRatingDraftRepository';
import creditRatingFileRepository from './creditRatingFileRepository';
import creditRatingRepository from './creditRatingRepository';
import cuftDataFileRepository from './cuftDataFileRepository';
import cuftDataRepository from './cuftDataRepository';
import equityRiskPremiumRepository from './equityRiskPremiumRepository';
import failedLoginAttemptRepository from './failedLoginAttemptRepository';
import featureRepository from './featureRepository';
import guaranteeFileRepository from './guaranteeFileRepository';
import guaranteeRepository from './guaranteeRepository';
import loanFileRepository from './loanFileRepository';
import loanRepository from './loanRepository';
import notificationRepository from './notificationRepository';
import participantAccountTrailsRepository from './participantAccountTrailsRepository';
import paymentRepository from './paymentRepository';
import providerDataRepository from './providerDataRepository';
import regionalEquityRiskPremiumRepository from './regionalEquityRiskPremiumRepository';
import tableColumnRepository from './tableColumnRepository';
import tokenRepository from './tokenRepository';
import topCurrencyAccountAuditTrailRepository from './topCurrencyAccountAuditTrailRepository';
import topCurrencyAccountRepository from './topCurrencyAccountRepository';
import userRepository from './userRepository';
import whtOriginRepository from './whtOriginRepository';
import whtPaymentRepository from './whtPaymentRepository';
import whtRecipientRepository from './whtRecipientRepository';

export {
  b2bLoanFileRepository,
  b2bLoanLegRepository,
  b2bLoanRepository,
  betaRepository,
  cashPoolAuditTrailRepository,
  cashPoolBatchFileRepository,
  cashPoolBatchPaymentsRepository,
  cashPoolBatchRepository,
  cashPoolFileRepository,
  cashPoolLeaderBenefitRepository,
  cashPoolOperatingExpensesRepository,
  cashPoolParticipantAccountExcludedIdsRepository,
  cashPoolParticipantAccountIdsRepository,
  cashPoolParticipantAccountRepository,
  cashPoolParticipantsRepository,
  cashPoolParticipantTrailRepository,
  cashPoolRepository,
  cashPoolStatementDataFileRepository,
  cashPoolStatementDataRepository,
  clientRepository,
  clientTemplateRepository,
  companyAuditTrailRepository,
  companyRepository,
  countryRepository,
  creditRatingDraftRepository,
  creditRatingFileRepository,
  creditRatingRepository,
  cuftDataFileRepository,
  cuftDataRepository,
  equityRiskPremiumRepository,
  failedLoginAttemptRepository,
  featureRepository,
  guaranteeFileRepository,
  guaranteeRepository,
  loanFileRepository,
  loanRepository,
  notificationRepository,
  participantAccountTrailsRepository,
  paymentRepository,
  providerDataRepository,
  regionalEquityRiskPremiumRepository,
  tableColumnRepository,
  tokenRepository,
  topCurrencyAccountAuditTrailRepository,
  topCurrencyAccountRepository,
  userRepository,
  whtOriginRepository,
  whtPaymentRepository,
  whtRecipientRepository,
};
