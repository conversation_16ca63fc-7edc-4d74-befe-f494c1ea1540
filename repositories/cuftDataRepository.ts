import { Op, col, fn } from 'sequelize';
import subWeeks from 'date-fns/subWeeks';

import models from '../models';
import {
  CuftDataType,
  DbCuftDataType,
  DbCuftDataTypeWithCreditRatingsType,
  FilterType,
  CuftOrderByType,
  CreateCuftDataFromCSVType,
  CreateCuftDataCurrenciesType,
  CreateCuftDataCreditRatingsType,
  DbCuftDataCurrenciesType,
  DbCuftDataCreditRatingsType,
  DbCuftDataFromCSVType,
  CurrencyWhereType,
  CuftDataSavedSearchType,
  DbCuftDataSavedSearchType,
} from '../types';

const {
  CuftData,
  CuftDataCurrencies,
  CuftDataCreditRatings,
  CuftDataLeadArrangers,
  CuftDataGuarantorNames,
  CuftDataSecondaryBorrowers,
  CuftDataSavedSearches,
  CuftDataExportCounters,
} = models;

function getCuftData(
  where: FilterType,
  whereCreditRatings: any,
  whereCurrencies: CurrencyWhereType,
  order: CuftOrderByType,
): Promise<DbCuftDataTypeWithCreditRatingsType[]> {
  return CuftData.findAll({
    raw: true,
    nest: true,
    where,
    include: [
      { model: CuftDataCurrencies, as: 'currencies', attributes: [], where: whereCurrencies },
      {
        model: CuftDataCreditRatings,
        as: 'creditRatings',
        attributes: ['id', 'creditRatingName', 'creditRatingValue'],
        where: whereCreditRatings,
      },
    ],
    group: ['CuftData.id', 'creditRatings.id', 'creditRatings.creditRatingName', 'creditRatings.creditRatingValue'],
    order,
  });
}

async function getUniqueBorrowerCountries(
  where: FilterType,
  whereCreditRatings: any,
  whereCurrencies: CurrencyWhereType,
): Promise<any> {
  return CuftData.findAll({
    raw: true,
    attributes: [[fn('DISTINCT', col('cuftBorrowerCountry')), 'distinctBorrowerCountries']],
    where,
    include: [
      { model: CuftDataCurrencies, as: 'currencies', attributes: [], where: whereCurrencies },
      { model: CuftDataCreditRatings, as: 'creditRatings', attributes: [], where: whereCreditRatings },
    ],
  });
}

async function getUniqueTrancheAssetClass(
  where: FilterType,
  whereCreditRatings: any,
  whereCurrencies: CurrencyWhereType,
): Promise<any> {
  return CuftData.findAll({
    raw: true,
    attributes: [[fn('DISTINCT', col('cuftTrancheAssetClass')), 'distinctTrancheAssetClasses']],
    where,
    include: [
      { model: CuftDataCurrencies, as: 'currencies', attributes: [], where: whereCurrencies },
      { model: CuftDataCreditRatings, as: 'creditRatings', attributes: [], where: whereCreditRatings },
    ],
  });
}

async function getUniqueCurrencies(where: FilterType, whereCreditRatings: any): Promise<any> {
  return CuftData.findAll({
    raw: true,
    nest: true,
    attributes: [],
    where,
    include: [
      {
        model: CuftDataCurrencies,
        as: 'currencies',
        attributes: [[fn('DISTINCT', col('currency')), 'distinctCurrencies']],
      },
      { model: CuftDataCreditRatings, as: 'creditRatings', attributes: [], where: whereCreditRatings },
    ],
  });
}

function createSingleCuftData(data: CuftDataType): Promise<DbCuftDataType> {
  return CuftData.create(data);
}

function createCuftDataCurrencies(data: Array<CreateCuftDataCurrenciesType>): Promise<DbCuftDataCurrenciesType> {
  return CuftDataCurrencies.bulkCreate(data);
}

function createCuftDataCreditRatings(
  data: Array<CreateCuftDataCreditRatingsType>,
): Promise<DbCuftDataCreditRatingsType> {
  return CuftDataCreditRatings.bulkCreate(data);
}

function createCuftDataLeadArrangers(data: Array<CreateCuftDataFromCSVType>): Promise<DbCuftDataFromCSVType> {
  return CuftDataLeadArrangers.bulkCreate(data);
}

function createCuftDataGuarantorNames(data: Array<CreateCuftDataFromCSVType>): Promise<DbCuftDataFromCSVType> {
  return CuftDataGuarantorNames.bulkCreate(data);
}

function createCuftDataSecondaryBorrowers(data: Array<CreateCuftDataFromCSVType>): Promise<DbCuftDataFromCSVType> {
  return CuftDataSecondaryBorrowers.bulkCreate(data);
}

function createCuftDataSavedSearch(data: CuftDataSavedSearchType): Promise<DbCuftDataSavedSearchType> {
  return CuftDataSavedSearches.create(data);
}

function getUserCuftDataSavedSearch(userId: number): Promise<Array<DbCuftDataSavedSearchType>> {
  return CuftDataSavedSearches.findAll({ where: { userId } });
}

function createCuftDataExportCounters(userId: number): Promise<any> {
  return CuftDataExportCounters.create({ userId });
}

function getNumberOfCuftDataExportsInLastWeek(userId: number): Promise<any> {
  const today = new Date();
  return CuftDataExportCounters.count({
    where: { userId, createdAt: { [Op.between]: [subWeeks(today, 1), today] } },
  });
}

export = {
  getCuftData,
  getUniqueBorrowerCountries,
  getUniqueTrancheAssetClass,
  getUniqueCurrencies,
  createSingleCuftData,
  createCuftDataCurrencies,
  createCuftDataCreditRatings,
  createCuftDataLeadArrangers,
  createCuftDataGuarantorNames,
  createCuftDataSecondaryBorrowers,
  createCuftDataSavedSearch,
  getUserCuftDataSavedSearch,
  createCuftDataExportCounters,
  getNumberOfCuftDataExportsInLastWeek,
};
