const { CashPoolAuditTrail } = require('../models');

function createCashPoolAuditTrail(auditTrail) {
  return CashPoolAuditTrail.create(auditTrail);
}

function getCashPoolAuditTrailAll(cashPoolId) {
  return CashPoolAuditTrail.findAll({
    where: {
      cashPoolId,
    },
    order: [['updatedAt', 'DESC']],
  });
}

function getCashPoolAuditTrail({ where }) {
  return CashPoolAuditTrail.findOne({ where });
}

function deleteCashPoolAuditTrail(id) {
  return CashPoolAuditTrail.destroy({
    where: {
      id,
    },
  });
}

module.exports = {
  createCashPoolAuditTrail,
  getCashPoolAuditTrailAll,
  getCashPoolAuditTrail,
  deleteCashPoolAuditTrail,
};
