const model = require('../models').GuaranteeFile;

function getAll() {
  return model.findAll({ attributes: ['id', 'file'] });
}

function getGuaranteeFile(id) {
  return model.findByPk(id, {
    attributes: ['extension', 'name', 'mimeType'],
  });
}

// returning not working
async function createGuaranteeFile(file, guaranteeId, { label, newName }) {
  const { originalname, mimetype } = file;
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/);
  const guaranteeFile = await model.create(
    {
      extension: originalNameMatch[2],
      name: newName ? newName : originalNameMatch[1],
      mimeType: mimetype,
      guaranteeId,
      label,
    },
    {},
  );

  return guaranteeFile;
}

function deleteGuaranteeFile(id, guaranteeId) {
  return model.destroy({
    where: { id, guaranteeId },
  });
}

function updateGuaranteeFile(id, data) {
  return model.update(data, {
    where: {
      id,
    },
  });
}

module.exports = {
  getAll,
  getGuaranteeFile,
  createGuaranteeFile,
  deleteGuaranteeFile,
  updateGuaranteeFile,
};
