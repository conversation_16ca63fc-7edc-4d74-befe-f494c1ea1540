const { Op } = require('sequelize');

const { CashPool, CashPoolBatch, CashPoolBatchFile, User } = require('../models');
const { cashPoolEnums } = require('../enums');

function getCashPoolBatch({ where, cashPoolId, clientId, order = [] }) {
  return CashPoolBatch.findOne({
    where,
    required: true,
    order,
    include: [
      {
        model: User,
        as: 'createdByUser',
        required: true,
        attributes: ['username', 'fullName'],
      },
      {
        model: CashPool,
        as: 'cashPool',
        required: true,
        where: { id: cashPoolId, clientId },
        attributes: ['name', 'grossBenefit'], // grossBenefit needed for batch delete
      },
      {
        model: CashPoolBatchFile,
        as: 'cashPoolBatchFile',
        required: true,
        attributes: ['id'],
      },
    ],
  });
}

function getCashPoolBatches({ cashPoolId, clientId }) {
  return CashPoolBatch.findAll({
    where: { status: { [Op.ne]: cashPoolEnums.batchStatus.AUTO_GENERATED } },
    order: [['createdAt', 'ASC']],
    include: [
      {
        model: User,
        as: 'createdByUser',
        attributes: ['username', 'fullName'],
      },
      {
        // included only to make sure batch belongs to the cashPool and the client
        model: CashPool,
        as: 'cashPool',
        where: { id: cashPoolId, clientId },
        attributes: ['name', 'type'],
      },
    ],
  });
}

function createCashPoolBatch(data) {
  return CashPoolBatch.create(data);
}

function updateCashPoolBatch(id, data) {
  return CashPoolBatch.update(data, { where: { id }, returning: true });
}

module.exports = {
  getCashPoolBatch,
  getCashPoolBatches,
  createCashPoolBatch,
  updateCashPoolBatch,
};
