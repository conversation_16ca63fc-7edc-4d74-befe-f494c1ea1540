import models from '../models';

import { B2BLoanLegType } from '../types';

const { BackToBackLoanLeg } = models;

export function bulkCreate(legs: Array<B2BLoanLegType>): Promise<Array<B2BLoanLegType>> {
  return BackToBackLoanLeg.bulkCreate(legs);
}

export function update(where: any, attributes: any): Promise<B2BLoanLegType> {
  return BackToBackLoanLeg.update(attributes, { where });
}

export function getOne(where: any): Promise<B2BLoanLegType | null> {
  return BackToBackLoanLeg.findOne({ where });
}
