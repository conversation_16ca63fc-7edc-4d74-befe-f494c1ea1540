const bcrypt = require('bcrypt');

const { User, Client, Client_Feature, Feature, SocialLogin } = require('../models');
const model = User;

function getUserById(id, exclude = ['password']) {
  return model.findByPk(id, {
    attributes: { exclude },
    include: {
      model: Client,
      as: 'client',
      attributes: ['id', 'name'],
    },
  });
}

function getUser(where, order) {
  return model.findOne({
    where,
    order,
    include: { model: Client, as: 'client', attributes: ['id', 'name'] },
  });
}

function getUserBySocialProvider(whereSocialLogin) {
  return model.findOne({
    include: [
      { model: Client, as: 'client', attributes: ['id', 'name'] },
      {
        model: SocialLogin,
        as: 'socialLogins',
        where: whereSocialLogin,
        required: true,
        attributes: ['provider', 'credential'],
      },
    ],
  });
}

function getUserWithClientFeatures(where, exclude) {
  return model.findOne({
    where,
    attributes: { exclude },
    include: {
      model: Client,
      as: 'client',
      attributes: ['id', 'name', 'industry', 'isLoanApproachCalculated', 'isCreditRatingAnonymized'],
      include: {
        model: Client_Feature,
        where: { isEnabled: true },
        as: 'clientFeatures',
        attributes: { exclude: ['clientId', 'createdAt', 'updatedAt'] },
        include: {
          model: Feature,
          as: 'feature',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
        },
      },
    },
  });
}

async function createUser(user) {
  const { dataValues } = await model.create(user);
  delete dataValues.password;

  return dataValues;
}

async function createSocialLogin(socialLogin) {
  const { dataValues } = await SocialLogin.create(socialLogin);

  return dataValues;
}

function getUsers(where) {
  return model.findAll({
    where,
    order: [
      ['clientId', 'ASC'],
      ['email', 'ASC'],
    ],
    attributes: { exclude: ['password'] },
    include: {
      model: Client,
      as: 'client',
      attributes: ['id', 'name'],
    },
  });
}

function getUsersCount(clientId) {
  return model.count({ where: { clientId } });
}

async function updateUserById(id, attributesToUpdate, returning = false) {
  const [rowsAffected, rows] = await model.update(attributesToUpdate, {
    where: { id },
    returning,
  });

  if (returning) {
    delete rows[0]?.dataValues.password;
    return [rowsAffected, rows[0]?.dataValues];
  }

  return [rowsAffected];
}

async function updatePassword({ id, newPassword }) {
  const saltRounds = 10;
  const password = await bcrypt.hash(newPassword, saltRounds);

  return model.update({ password }, { where: { id } });
}

function deleteUser(id) {
  return model.destroy({ where: { id } });
}

module.exports = {
  getUserById,
  getUser,
  getUserBySocialProvider,
  getUserWithClientFeatures,
  getUsers,
  getUsersCount,
  createUser,
  createSocialLogin,
  updateUserById,
  updatePassword,
  deleteUser,
};
