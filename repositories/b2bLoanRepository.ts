import { literal } from 'sequelize';

import models from '../models';
import { B2BLoanType, UserDataAccessTokenType } from '../types';
import { checkClientFeatureFlags, marsUtils } from '../utils/clientUtils';
import { Client } from '../enums';

const { BackToBackLoan, BackToBackLoanFile, BackToBackLoanLeg } = models;

const createLoan = async (b2bLoan: B2BLoanType, user: Partial<UserDataAccessTokenType>): Promise<B2BLoanType> => {
  const { clientId, username, clientName } = user;

  if (clientName && checkClientFeatureFlags(Client.MARS, clientName)) {
    const externalId = await marsUtils.incrementExternalId(user);

    b2bLoan.externalId = externalId;
  }

  const b2bLoanToCreate = { ...b2bLoan, clientId, createdBy: username, originalIssueDate: b2bLoan.issueDate };

  const createdB2BLoan = await BackToBackLoan.create({ ...b2bLoanToCreate });

  delete createdB2BLoan.dataValues.clientId;
  delete createdB2BLoan.dataValues.calculationLog;

  return createdB2BLoan;
};

const getLoans = ({
  where,
  order,
  limit,
  paranoid,
}: {
  where?: any;
  order?: Array<any>;
  limit?: number | null;
  paranoid?: boolean;
}) => {
  return BackToBackLoan.findAll({ where, order, limit, paranoid });
};

const getLoan = (
  id: number,
  clientId: number,
  exclude: Array<string> | null = ['clientId', 'calculationLog'],
): Promise<(B2BLoanType & { dataValues: B2BLoanType }) | null> => {
  return BackToBackLoan.findOne({
    where: { id, clientId },
    paranoid: false,
    order: [[{ model: BackToBackLoanLeg, as: 'legs' }, 'ordinal', 'ASC']],
    attributes: {
      exclude,
      include: [
        [
          literal(
            `(SELECT array_agg(lender) FROM (SELECT * FROM "BackToBackLoanLegs" WHERE "loanId" = ${id} ORDER BY ordinal) AS temp1)`,
          ),
          'lenders',
        ],
        [
          literal(
            `(SELECT array_agg(borrower) FROM (SELECT * FROM "BackToBackLoanLegs" WHERE "loanId" = ${id} ORDER BY ordinal) AS temp2)`,
          ),
          'borrowers',
        ],
      ],
    },
    include: [
      {
        model: BackToBackLoanLeg,
        as: 'legs',
        where: { loanId: id },
      },
      {
        model: BackToBackLoanFile,
        as: 'files',
        where: { loanId: id },
        attributes: ['id', 'name', 'label', 'status', 'createdAt', 'isGenerated'],
        required: false,
      },
    ],
  });
};

const updateLoan = async (
  id: number,
  loan: B2BLoanType,
  user: Partial<UserDataAccessTokenType>,
  returning = true,
): Promise<B2BLoanType | null | undefined> => {
  loan.updatedBy = user?.username || null;

  const response = await BackToBackLoan.update(loan, { where: { id }, returning });

  if (returning) {
    const returnValue = response[1][0];
    delete returnValue.dataValues.clientId;
    delete returnValue.dataValues.calculationLog;

    return returnValue;
  }
};

const getLoansCount = (clientId: number) => {
  return BackToBackLoan.count({ where: { clientId } });
};

const deleteLoan = (id: number, clientId: number, force: boolean) => {
  return BackToBackLoan.destroy({ where: { id, clientId }, force });
};

const restoreLoan = (id: number, clientId: number) => {
  return BackToBackLoan.restore({ where: { id, clientId } });
};

export default {
  createLoan,
  getLoans,
  getLoan,
  updateLoan,
  getLoansCount,
  deleteLoan,
  restoreLoan,
};
