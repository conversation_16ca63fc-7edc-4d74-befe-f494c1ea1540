import 'styles/index.css';

import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'theme-ui';

import App from 'components/App';
import store from 'store';
import { theme } from 'ui';

ReactDOM.render(
  <Provider store={store as any}>
    <ThemeProvider theme={theme}>
      <App />
    </ThemeProvider>
  </Provider>,
  document.getElementById('root')
);
